<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>com.fxiaoke.cloud</groupId>
    <artifactId>fxiaoke-spring-cloud-parent</artifactId>
    <version>3.0.0-SNAPSHOT</version>
    <relativePath/>
  </parent>

  <groupId>com.fxiaoke</groupId>
  <artifactId>fs-file-server</artifactId>
  <version>1.0-SNAPSHOT</version>
  <modelVersion>4.0.0</modelVersion>

  <packaging>jar</packaging>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
    <maven.compiler.source>21</maven.compiler.source>
    <maven.compiler.target>21</maven.compiler.target>
    <java.version>21</java.version>
    <jdk.version>21</jdk.version>

    <fs-stone-commons-client.version>1.7.6-SNAPSHOT</fs-stone-commons-client.version>
    <aws.s3.sdk.version>1.12.783</aws.s3.sdk.version>
    <junit.version>4.13.2</junit.version>
    <commons-io.version>2.15.1</commons-io.version>
    <hutool-http.version>5.8.32</hutool-http.version>
    <fsi-proxy.version>4.2.0-SNAPSHOT</fsi-proxy.version>
    <fs-stone-sdk.version>1.1-SNAPSHOT</fs-stone-sdk.version>
    <commons-fileupload.version>1.5</commons-fileupload.version>
    <fs-warehouse-client.version>2.0-SNAPSHOT</fs-warehouse-client.version>
    <aws.sdk.version>2.28.28</aws.sdk.version>
    <fs-enterpriserelation-rest-api2.version>2.0.4-SNAPSHOT</fs-enterpriserelation-rest-api2.version>
  </properties>

  <dependencies>
    <!-- SpringBoot核心依赖 必须 start-->
    <dependency>
      <artifactId>spring-boot-starter-web</artifactId>
      <groupId>org.springframework.boot</groupId>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke.boot</groupId>
      <artifactId>actuator-ext-spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <artifactId>spring-boot-starter-aop</artifactId>
      <groupId>org.springframework.boot</groupId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
      <optional>true</optional>
    </dependency>
    <!--SpringBoot核心依赖 必须 end-->

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>

    <!--组件-SpringBoot MongoDB-->
    <dependency>
      <artifactId>mongo-spring-boot-starter</artifactId>
      <groupId>com.fxiaoke.boot</groupId>
    </dependency>

    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-rocketmq-support</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.rocketmq</groupId>
      <artifactId>rocketmq-client</artifactId>
    </dependency>

    <!--组件-SpringBoot Redis-->
    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>jedis-spring-support</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>jedis</artifactId>
          <groupId>redis.clients</groupId>
        </exclusion>
        <exclusion>
          <artifactId>config-core</artifactId>
          <groupId>com.github.colin-lee</groupId>
        </exclusion>
        <exclusion>
          <artifactId>j2objc-annotations</artifactId>
          <groupId>com.google.j2objc</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>redis.clients</groupId>
      <artifactId>jedis</artifactId>
      <version>3.7.1</version>
    </dependency>

    <!--组件-SpringBoot 配置中心-->
    <dependency>
      <groupId>com.fxiaoke.cloud</groupId>
      <artifactId>cms-spring-cloud-starter</artifactId>
    </dependency>

    <!-- 日志自动上报 -->
    <dependency>
      <groupId>com.fxiaoke.boot</groupId>
      <artifactId>metrics-spring-boot-starter</artifactId>
    </dependency>
    <!-- 链路追踪过滤器 -->
    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>core-filter</artifactId>
    </dependency>

    <!--Asm Server Cline-->
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-active-session-manage-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-enterprise-id-account-converter</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>zstd-jni</artifactId>
          <groupId>com.github.luben</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <!--互联业务 企业身份-->
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-enterpriserelation-rest-api2</artifactId>
      <version>${fs-enterpriserelation-rest-api2.version}</version>
    </dependency>

    <!-- FileSystem Client -->
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-stone-sdk</artifactId>
      <version>${fs-stone-sdk.version}</version>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-fsi-proxy</artifactId>
      <version>${fsi-proxy.version}</version>
    </dependency>
    <!-- fs-stone-auth Client -->
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-stone-commons-client</artifactId>
      <version>${fs-stone-commons-client.version}</version>
    </dependency>
    <!-- FastDFS Client -->
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-warehouse-client</artifactId>
      <version>${fs-warehouse-client.version}</version>
    </dependency>

    <!--公司组件-end-->

    <!--第三方组件-start-->

    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-http</artifactId>
      <version>${hutool-http.version}</version>
    </dependency>

    <!--通用工具类-->

    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>${junit.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <version>RELEASE</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.amazonaws</groupId>
      <artifactId>aws-java-sdk-s3</artifactId>
      <version>${aws.s3.sdk.version}</version>
    </dependency>
    <!--第三方组件 按需引入 非必须 end-->

    <dependency>
      <groupId>software.amazon.awssdk</groupId>
      <artifactId>aws-sdk-java</artifactId>
      <version>${aws.sdk.version}</version>
    </dependency>

    <dependency>
      <groupId>software.amazon.awssdk</groupId>
      <artifactId>apache-client</artifactId>
      <version>${aws.sdk.version}</version>
    </dependency>

    <dependency>
      <groupId>software.amazon.awssdk</groupId>
      <artifactId>s3</artifactId>
      <version>${aws.sdk.version}</version>
    </dependency>

    <!-- 测试相关依赖 -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-junit-jupiter</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-inline</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <!--添加打包插件以便支持jar模式运行-->
  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>


</project>