package com.fxiaoke.file.server.manager;

import com.fxiaoke.file.server.dao.mongo.StoneCoreMetaDao;
import com.fxiaoke.file.server.domain.constants.DataBaseCluster;
import com.fxiaoke.file.server.domain.constants.DataBaseTransferStatus;
import com.fxiaoke.file.server.domain.constants.ErInfo;
import com.fxiaoke.file.server.domain.constants.PathType;
import com.fxiaoke.file.server.domain.entity.StoneCoreMeta;
import com.fxiaoke.file.server.domain.exception.FileServerException;
import com.fxiaoke.file.server.domain.model.StoneCoreConfig;
import com.fxiaoke.file.server.domain.model.api.EnterpriseBaseInfo;
import com.fxiaoke.file.server.service.AsmService;
import com.github.mongo.support.DatastoreExt;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class DataBaseManager {

  private static final String MODULE = "DataBaseManager";

  @Autowired(required = false)
  private DatastoreExt nDataStore;
  @Autowired(required = false)
  private DatastoreExt n1DataStore;
  @Autowired(required = false)
  private DatastoreExt n2DataStore;
  @Autowired(required = false)
  private DatastoreExt shardDataStore;

  private final AsmService asmService;
  private final StoneCoreMetaDao stoneCoreMetaDao;

  LoadingCache<String, StoneCoreConfig> stoneCoreConfigCache;

  public DataBaseManager(AsmService asmService,
      StoneCoreMetaDao stoneCoreMetaDao) {
    this.stoneCoreMetaDao = stoneCoreMetaDao;
    this.asmService = asmService;
    stoneCoreConfigCache = CacheBuilder.newBuilder().maximumSize(100000)
        .expireAfterAccess(2, TimeUnit.HOURS).build(new CacheLoader<>() {
          @NotNull
          @Override
          public StoneCoreConfig load(@NotNull String key) {
            return loadStoneCoreConfig(key);
          }
        });
  }

  public DatastoreExt getReadDataStore(String ea, PathType pathType) {
    return switch (pathType) {
      case N_FILE, TN_FILE, C_FILE, TC_FILE -> getEnterpriseReadDataStore(ea);
      default -> throw new FileServerException(MODULE, ErInfo.DATABASE_MANAGER_PATH_TYPE_NOT_SUPPORT,ea,pathType);
    };
  }

  private DatastoreExt getEnterpriseReadDataStore(String ea) {
    StoneCoreConfig stoneCoreConfig = stoneCoreConfigCache.getUnchecked(ea);
    DataBaseCluster cluster = stoneCoreConfig.getCluster();
    return switch (cluster) {
      case N -> nDataStore.use(stoneCoreConfig.getDbName());
      case N1 -> n1DataStore.use(stoneCoreConfig.getDbName());
      case N2 -> n2DataStore.use(stoneCoreConfig.getDbName());
      case Shard -> shardDataStore.setTenantId(stoneCoreConfig.getTenantId());
    };
  }

  private StoneCoreConfig loadStoneCoreConfig(String ea) {

    // 查找文件系统核心路由表
    Optional<StoneCoreMeta> stoneCoreMetaOpt = stoneCoreMetaDao.find(ea);
    if (stoneCoreMetaOpt.isEmpty()) {
      throw new FileServerException(MODULE, ErInfo.STONE_CORE_META_NOT_FOUND, ea);
    }
    StoneCoreMeta stoneCoreMeta = stoneCoreMetaOpt.get();

    StoneCoreConfig stoneCoreConfig = new StoneCoreConfig();
    EnterpriseBaseInfo baseEnterpriseInfo = asmService.getBaseEnterpriseInfo(ea);
    stoneCoreConfig.setEa(ea);
    stoneCoreConfig.setTenantId(baseEnterpriseInfo.getTenantId());
    String dfsAddress = baseEnterpriseInfo.getDfsAddress();
    String createTimeStr = baseEnterpriseInfo.getCreateTimeStr();
    DataBaseTransferStatus transferStatus = DataBaseTransferStatus.of(
        stoneCoreMeta.getTransferStatus());

    switch (transferStatus) {
      case NOT_STARTED, IN_PROGRESS -> {
        stoneCoreConfig.setDataBaseClusterByDfsAddress(dfsAddress);
        stoneCoreConfig.setDbNameByCreateTime(createTimeStr);
      }
      case COMPLETED -> {
        stoneCoreConfig.setCluster(DataBaseCluster.Shard);
      }
      default ->
          throw new FileServerException(MODULE, ErInfo.STONE_CORE_META_TRANSFER_STATUS_ERROR, ea,
              transferStatus);
    }

    return stoneCoreConfig;
  }
}

