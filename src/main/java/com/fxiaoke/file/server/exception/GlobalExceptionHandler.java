package com.fxiaoke.file.server.exception;


import com.fxiaoke.file.server.domain.exception.FileServerException;
import com.fxiaoke.stone.commons.domain.R;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.ClientAbortException;
import org.springframework.validation.BindException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MissingRequestHeaderException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.UnsatisfiedServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.async.AsyncRequestNotUsableException;
import org.springframework.web.context.request.async.AsyncRequestTimeoutException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.servlet.resource.NoResourceFoundException;
import org.springframework.web.method.annotation.HandlerMethodValidationException;
import org.springframework.web.multipart.support.MissingServletRequestPartException;

@RestControllerAdvice
@Slf4j(topic = "ExceptionHandler")
public class GlobalExceptionHandler {
  
  private static final String DEFAULT_CONTENT_TYPE = "application/json";

  @ExceptionHandler(value = Exception.class)
  public R<String> handler(HttpServletResponse response, Exception e) {
    log.error("unknown Reason:", e);
    response.setContentType(DEFAULT_CONTENT_TYPE);
    response.setStatus(500);
    return R.error(500,"Unknown exception,please contact the admin");
  }

  @ExceptionHandler(value = MaxUploadSizeExceededException.class)
  public R<String> handler(HttpServletResponse response, MaxUploadSizeExceededException e){
    logWarn(e.getMessage());
    response.setStatus(400);
    response.setContentType(DEFAULT_CONTENT_TYPE);
    return R.error(400,e.getMessage());
  }

  @ExceptionHandler(value = NoResourceFoundException.class)
  public R<String> handler(HttpServletResponse response, NoResourceFoundException e){
    logWarn(e.getMessage());
    response.setStatus(404);
    response.setContentType(DEFAULT_CONTENT_TYPE);
    return R.error(404,"Resource not found");
  }

  @ExceptionHandler(value = HttpRequestMethodNotSupportedException.class)
  public R<String> handler(HttpServletResponse response, HttpRequestMethodNotSupportedException e) {
    logWarn("HTTP method not supported: " + e.getMethod());
    response.setStatus(405);
    response.setContentType(DEFAULT_CONTENT_TYPE);
    String supportedMethods = e.getSupportedMethods() != null
        ? String.join(", ", e.getSupportedMethods())
        : "none";
    String message = "HTTP method '" + e.getMethod() + "' is not supported. Supported methods are: " + supportedMethods;
    return R.error(405, message);
  }

  @ExceptionHandler(value = AsyncRequestNotUsableException.class)
  public void handler(HttpServletResponse response, AsyncRequestNotUsableException e) {
    logWarn(e.getMessage());
    response.setStatus(499);
  }

  @ExceptionHandler(value = ClientAbortException.class)
  public void handler(HttpServletResponse response,ClientAbortException e) {
    // 客户端终止异常,不需要处理,仅记录日志
    logWarn(e.getMessage());
    response.setStatus(499);
  }

  @ExceptionHandler(value = AsyncRequestTimeoutException.class)
  public R<String> handler(HttpServletResponse response, AsyncRequestTimeoutException e) {
    logError("process async response timeout", e);
    response.setStatus(500);
    response.setContentType(DEFAULT_CONTENT_TYPE);
    return R.error(500,"async request timeout");
  }

  @ExceptionHandler(value = FileServerException.class)
  public R<String> handler(HttpServletResponse response, FileServerException e) {
    if (e.getCode()<500) {
      logWarn(e.getReason(),e);
    } else {
      logError(e.getReason(),e);
    }
    response.setContentType(DEFAULT_CONTENT_TYPE);
    response.setStatus(e.getCode());
    return R.error(e.getCode(),e.getMessage());
  }

  @ExceptionHandler(value = IllegalStateException.class)
  public R<String> handler(HttpServletResponse response, IllegalStateException e) {
    logWarn(e.getMessage(),e);
    response.setStatus(400);
    response.setContentType(DEFAULT_CONTENT_TYPE);
    return R.error(400,e.getMessage());
  }

  @ExceptionHandler(value = MissingRequestHeaderException.class)
  public R<String> handler(HttpServletResponse response, MissingRequestHeaderException e) {
    logWarn(e.getMessage());
    response.setStatus(400);
    response.setContentType(DEFAULT_CONTENT_TYPE);
    return R.error(400, "Request header parameter is missing: "+e.getHeaderName());
  }

  @ExceptionHandler(value = BindException.class)
  public R<String> handler(HttpServletResponse response, BindException e) {
    logWarn(e.getMessage());
    response.setStatus(400);
    response.setContentType(DEFAULT_CONTENT_TYPE);
    StringBuilder message=new StringBuilder();
    e.getAllErrors().forEach(error-> message.append(error.getDefaultMessage()).append(";"));
    return R.error(400,message.toString());
  }

  @ExceptionHandler(value = UnsatisfiedServletRequestParameterException.class)
  public R<String> handler(HttpServletResponse response, UnsatisfiedServletRequestParameterException e) {
    logWarn(e.getMessage());
    response.setStatus(400);
    response.setContentType(DEFAULT_CONTENT_TYPE);
    return R.error(400, e.getMessage());
  }

  @ExceptionHandler(value = MissingServletRequestParameterException.class)
  public R<String> handler(HttpServletResponse response, MissingServletRequestParameterException e) {
    logWarn(e.getMessage(), e);
    response.setStatus(400);
    response.setContentType(DEFAULT_CONTENT_TYPE);
    return R.error(400, e.getMessage());
  }

  @ExceptionHandler(value = HandlerMethodValidationException.class)
  public R<String> handler(HttpServletResponse response, HandlerMethodValidationException e) {
    logWarn("Validation failure: " + e.getMessage());
    response.setStatus(400);
    response.setContentType(DEFAULT_CONTENT_TYPE);
    return R.error(400,e.getMessage());
  }

  @ExceptionHandler(value = MissingServletRequestPartException.class)
  public R<String> handler(HttpServletResponse response, MissingServletRequestPartException e) {
    logWarn("Missing request part: " + e.getMessage());
    response.setStatus(400);
    response.setContentType(DEFAULT_CONTENT_TYPE);
    return R.error(400, e.getMessage());
  }

  private void logWarn(String message) {
    log.warn("Client exception: {}",message);
  }

  private void logWarn(String message, Exception e) {
    log.warn("Client exception: {}",message,e);
  }

  private void logError(String message, Exception e) {
    log.error("Server exception: {}",message,e);
  }

}
