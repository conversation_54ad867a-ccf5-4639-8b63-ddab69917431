package com.fxiaoke.file.server.service;

import com.alibaba.dubbo.common.URL;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.file.server.domain.model.imaginary.PipelineRequest;
import com.fxiaoke.file.server.help.SimpleLoadBalancerHelper;
import com.github.trace.TraceContext;
import java.io.IOException;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
public class ImaginaryService {

  private final OkHttpSupport client;
  private final SimpleLoadBalancerHelper loadBalancerHelper;
  private final ObjectMapper objectMapper = new ObjectMapper();
  private static final MediaType MEDIA_TYPE = MediaType.parse("application/octet-stream;");

  public ImaginaryService(@Qualifier("atsOkHttpClient") OkHttpSupport atsOkHttpClient,
      SimpleLoadBalancerHelper  simpleLoadBalancerHelper){
    this.client = atsOkHttpClient;
    this.loadBalancerHelper = simpleLoadBalancerHelper;
  }

  /**
   * 使用字节数组处理图像管道
   *
   * @param imageBytes      图像字节数组
   * @param pipelineRequest 管道请求
   * @return 处理后的图像字节数组
   * @throws IOException 如果发生IO错误
   */
  public byte[] processPipeline(byte[] imageBytes, PipelineRequest pipelineRequest)
      throws IOException {
    String serverHost = loadBalancerHelper.getImaginaryNodeByImageSize(imageBytes.length);
    String jsonOperations = objectMapper.writeValueAsString(pipelineRequest);
    String url = serverHost +
        "/pipeline?operations=" +
        jsonOperations +
        "&traceId=" +
        URL.encode(TraceContext.get().getTraceId());
    RequestBody imageRequestByteData = RequestBody.create(imageBytes, MEDIA_TYPE);
    Request request = new Request.Builder().url(url).post(imageRequestByteData).build();
    return (byte[]) client.syncExecute(request, new SyncCallback() {
      @Override
      public Object response(Response response) throws Exception {
        if (!response.isSuccessful()) {
          throw new IOException("Unexpected response : " + response);
        }

        if (response.body() == null) {
          throw new IOException("Empty response from server");
        }

        byte[] responseBytes = response.body().bytes();
        if (responseBytes.length == 0) {
          throw new IOException("Empty response from server");
        }

        return responseBytes;
      }
    });
  }

}
