package com.fxiaoke.file.server.service.impl;

import com.facishare.warehouse.fastdfs.client.WarehouseFastDFSClient;
import com.facishare.warehouse.fastdfs.exception.FastDFSException;
import com.fxiaoke.file.server.dao.mongo.AvatarFileMetaDao;
import com.fxiaoke.file.server.domain.constants.ErInfo;
import com.fxiaoke.file.server.domain.entity.AvatarFileMeta;
import com.fxiaoke.file.server.domain.exception.FileServerException;
import com.fxiaoke.file.server.service.IAvatarService;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Optional;
import jakarta.annotation.Nonnull;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;

public class AvatarService implements IAvatarService {

  private static final Logger log = LoggerFactory.getLogger(AvatarService.class);
  private final int avatarFileMetaCacheSize;
  private static final String MODULE = "AvatarFileMetaService";
  private static final String AVATAR_DEFAULT_PATH = "static/defaultAvatar.png";
  private static final byte[] defaultAvatar;

  static {
    ClassPathResource avatarResource = new ClassPathResource(AVATAR_DEFAULT_PATH);
    try {
      defaultAvatar = avatarResource.getInputStream().readAllBytes();
    } catch (IOException e) {
      throw new FileServerException(e, MODULE, ErInfo.SERVER_INIT_ERROR, AVATAR_DEFAULT_PATH);
    }
  }

  // 头像文件元数据Dao
  AvatarFileMetaDao avatarFileMetaDao;
  // 头像文件FastDFS客户端
  WarehouseFastDFSClient avatarFastDFSClient;
  // 缓存头像文件元数据避免频繁访问数据库
  LoadingCache<String,Optional<AvatarFileMeta> > avatarFileMetaCache;

  public AvatarService(int avatarFileMetaCacheSize, AvatarFileMetaDao avatarFileMetaDao, WarehouseFastDFSClient avatarFastDFSClient) {
    this.avatarFileMetaCacheSize = avatarFileMetaCacheSize;
    this.avatarFileMetaDao = avatarFileMetaDao;
    this.avatarFastDFSClient = avatarFastDFSClient;
    localCacheInit();
  }

  private void localCacheInit() {
    avatarFileMetaCache = CacheBuilder.newBuilder()
        .maximumSize(avatarFileMetaCacheSize)
        .build(new CacheLoader<>() {
          @NotNull
          @Override
          public Optional<AvatarFileMeta> load(@Nonnull String key) {
            try {
              AvatarFileMeta avatarFileMeta = avatarFileMetaDao.findByPath(key);
              return Optional.ofNullable(avatarFileMeta);
            } catch (Exception e) {
              throw new FileServerException(e, MODULE, ErInfo.AVATAR_DATABASE_ERROR, key);
            }
          }
        });
  }

  @Override
  public Optional<AvatarFileMeta> findMetaByPath(String path) {
    try {
      return avatarFileMetaCache.get(path);
    } catch (Exception e) {
      log.error("find avatar mongo error, path:{}", path, e);
      return Optional.empty();
    }
  }

  @Override
  public void saveMeta(AvatarFileMeta avatarFileMeta) {
    try {
      avatarFileMetaDao.save(avatarFileMeta);
    } catch (Exception e) {
      throw new FileServerException(e, MODULE, ErInfo.AVATAR_DATABASE_ERROR,
          avatarFileMeta.getPath());
    }
  }


  @Override
  public InputStream getAvatarFileByPath(String path, boolean isDefaultAvatar) {
    Optional<AvatarFileMeta> fileMetaOptional = findMetaByPath(path);
    if (fileMetaOptional.isPresent()) {
      String fileId = fileMetaOptional.get().getFileId();
      try (InputStream stream = new ByteArrayInputStream(avatarFastDFSClient.downloadFile(fileId))) {
        return stream;
      } catch (FastDFSException | IOException e) {
        throw new FileServerException(e, MODULE, ErInfo.AVATAR_FAST_DFS_ERROR, fileId, path);
      }
    }
    if (isDefaultAvatar) {
      try (InputStream stream = new ByteArrayInputStream(defaultAvatar)) {
        return stream;
      } catch (IOException e) {
        throw new FileServerException(e, MODULE, ErInfo.AVATAR_DEFAULT_ERROR, path,
            AVATAR_DEFAULT_PATH);
      }
    }
    throw new FileServerException(MODULE, ErInfo.AVATAR_NOT_FOUND, path);
  }

  public void downloadAvatarToOutStream(String path, OutputStream outputStream) {
    Optional<AvatarFileMeta> fileMetaOptional = findMetaByPath(path);

    if (fileMetaOptional.isPresent()) {
      try {
        avatarFastDFSClient.downloadByStream(fileMetaOptional.get().getFileId(), outputStream);
      } catch (FastDFSException e) {
        throw new FileServerException(e, MODULE, ErInfo.AVATAR_FAST_DFS_ERROR, fileMetaOptional.get().getFileId(), path);
      }
    }
    try {
      outputStream.write(defaultAvatar);
    } catch (IOException e) {
      throw new FileServerException(e, MODULE, ErInfo.AVATAR_DEFAULT_ERROR, path, AVATAR_DEFAULT_PATH);
    }
  }
}
