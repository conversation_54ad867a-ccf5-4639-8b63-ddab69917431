package com.fxiaoke.file.server.service;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import com.fxiaoke.file.server.domain.model.api.S3Config;
import com.google.common.base.Strings;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.model.PresignedGetObjectRequest;

import java.io.InputStream;
import java.net.URL;
import java.time.Duration;
import java.util.List;

/**
 * creator: liuys
 * CreateTime: 2025-05-26
 * Description: S3对接
 */
@Service
@Slf4j
public class S3FileService {

  @Resource
  public S3ConfigService s3ConfigService;

  /**
   * 初始化分片上传
   *
   * @param ea
   * @param objectKey
   * @return
   */
  public String initMutiPartUpload(String ea, String objectKey) {
    S3Client s3Client = s3ConfigService.getS3Client(ea);
    S3Config s3Config = s3ConfigService.getS3Config(ea);
    CreateMultipartUploadRequest request = CreateMultipartUploadRequest.builder()
        .bucket(s3Config.getBucket())
        .key(objectKey)
        .build();
    CreateMultipartUploadResponse multipartUpload = s3Client.createMultipartUpload(request);
    return multipartUpload.uploadId();
  }

  /**
   * 上传分片
   *
   * @param ea
   * @param objectKey
   * @param uploadId
   * @param partNumber
   * @param contentLength
   * @param inputStream
   * @return
   */
  public String uploadPart(String ea, String objectKey, String uploadId, int partNumber, long contentLength,
      InputStream inputStream) {
    UploadPartResponse uploadPartResponse = null;
    try {
      S3Client s3Client = s3ConfigService.getS3Client(ea);
      S3Config s3Config = s3ConfigService.getS3Config(ea);
      UploadPartRequest request = UploadPartRequest.builder()
          .bucket(s3Config.getBucket())
          .key(objectKey)
          .uploadId(uploadId)
          .partNumber(partNumber)
          .contentLength(contentLength)
          .build();
      uploadPartResponse = s3Client.uploadPart(request,
          RequestBody.fromInputStream(inputStream, contentLength));
    } catch (Exception e) {
      log.error("upload part error, ea is {}, objectKey is {}, uploadId is {}, partNumber is {}, error message is, {}",
          ea, objectKey, uploadId, partNumber, e.getMessage());
    }

    return uploadPartResponse.eTag();
  }

  /**
   * 完成分片上传
   *
   * @param ea
   * @param objectKey
   * @param uploadId
   * @param partETags
   * @return
   */
  public CompleteMultipartUploadResponse finishMultipartUpload(String ea, String objectKey, String uploadId,
      List<CompletedPart> partETags) {
    S3Client s3Client = s3ConfigService.getS3Client(ea);
    S3Config s3Config = s3ConfigService.getS3Config(ea);
    CompleteMultipartUploadRequest completeMultipartUploadRequest = CompleteMultipartUploadRequest.builder()
        .bucket(s3Config.getBucket())
        .key(objectKey)
        .uploadId(uploadId)
        .multipartUpload(multipartUpload -> multipartUpload.parts(partETags)).build();
    return s3Client.completeMultipartUpload(completeMultipartUploadRequest);
  }

  /**
   * 取消分片上传
   *
   * @param ea
   * @param objectKey
   * @param uploadId
   * @return
   */
  public boolean abortMultipartUpload(String ea, String objectKey, String uploadId) {
    S3Client s3Client = s3ConfigService.getS3Client(ea);
    S3Config s3Config = s3ConfigService.getS3Config(ea);
    AbortMultipartUploadRequest abortMultipartUploadRequest = AbortMultipartUploadRequest.builder()
        .bucket(s3Config.getBucket())
        .key(objectKey)
        .uploadId(uploadId).build();
    return s3Client.abortMultipartUpload(abortMultipartUploadRequest).sdkHttpResponse().isSuccessful();
  }

  /**
   * 生成下载url
   *
   * @param ea
   * @param objectKey
   * @param fileName
   * @param expireTime
   * @return
   */
  public String generateDownloadUrl(String ea, String objectKey, String fileName, int expireTime) {
    try {
      S3Presigner s3Presigner = s3ConfigService.getS3Presigner(ea);
      S3Config s3Config = s3ConfigService.getS3Config(ea);
      GetObjectRequest getObjectRequest = Strings.isNullOrEmpty(fileName) ? GetObjectRequest.builder()
          .bucket(s3Config.getBucket())
          .key(objectKey)
          .build()
          : GetObjectRequest.builder()
              .bucket(s3Config.getBucket())
              .key(objectKey)
              .responseContentDisposition(
                  "attachment; filename=\"" + fileName + "\"")
              .build();

      GetObjectPresignRequest getObjectPresignRequest = GetObjectPresignRequest.builder()
          .getObjectRequest(getObjectRequest)
          .signatureDuration(Duration.ofSeconds(expireTime))
          .build();
      PresignedGetObjectRequest presignedGetObjectRequest = s3Presigner
          .presignGetObject(getObjectPresignRequest);
      URL url = presignedGetObjectRequest.url();
      return url.toString();
    } catch (Exception e) {
      log.error("generate download url error , error message is, {}", e.getMessage());
      return "";
    }
  }

  /**
   * 获取对象输入流
   *
   * @param ea
   * @param objectKey
   * @return
   */
  public InputStream getObject(String ea, String objectKey) {
    S3Client s3Client = s3ConfigService.getS3Client(ea);
    S3Config s3Config = s3ConfigService.getS3Config(ea);
    GetObjectRequest getObjectRequest = GetObjectRequest.builder()
        .bucket(s3Config.getBucket())
        .key(objectKey)
        .build();
    return s3Client.getObject(getObjectRequest);
  }


  public HeadObjectResponse headObject(String ea, String objectKey) {
    S3Client s3Client = s3ConfigService.getS3Client(ea);
    S3Config s3Config = s3ConfigService.getS3Config(ea);
    HeadObjectRequest request = HeadObjectRequest.builder()
        .bucket(s3Config.getBucket())
        .key(objectKey)
        .build();
    return s3Client.headObject(request);
  }
}