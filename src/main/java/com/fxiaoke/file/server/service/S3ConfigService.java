package com.fxiaoke.file.server.service;

import com.fxiaoke.file.server.domain.model.api.S3Config;
import com.fxiaoke.file.server.utils.EncryptUtils;
import com.github.autoconf.ConfigFactory;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.moandjiezana.toml.Toml;

import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.http.SdkHttpClient;
import software.amazon.awssdk.http.apache.ApacheHttpClient;
import software.amazon.awssdk.http.apache.ProxyConfiguration;
import software.amazon.awssdk.regions.Region;

import javax.annotation.PostConstruct;

import org.springframework.stereotype.Service;

import java.net.URI;
import java.util.Map;

/**
 * creator: liuys
 * CreateTime: 2025-05-26
 * Description:
 */
@Service
@Slf4j
public class S3ConfigService {
  Cache<String, S3Config> configCache = CacheBuilder.newBuilder()
      .build();
  private LoadingCache<String, S3Client> s3Client;
  private LoadingCache<String, S3Presigner> s3PreSignerCache;

  @PostConstruct
  public void init() {
    s3Client = CacheBuilder.newBuilder()
    .build(new CacheLoader<String, S3Client>() {
          @NotNull
          @Override
          public S3Client load(@NotNull String key) {
            return initS3Client(key);
          } 
        });

    s3PreSignerCache = CacheBuilder.newBuilder()
        .build(new CacheLoader<String, S3Presigner>() {
          @NotNull
          @Override
          public S3Presigner load(@NotNull String key) {
            return initS3Presigner(key);
          }
        });

    ConfigFactory.getConfig("fs-self-s3-toml", config -> {
      Toml configString = new Toml().read(config.getString());
      for (Map.Entry<String, Object> entry : configString.entrySet()) {
        Toml toml = (Toml) entry.getValue();
        String bucket = toml.getString("bucket");
        String accessKey = EncryptUtils.decode(toml.getString("accesskey"));
        String secretKey = EncryptUtils.decode(toml.getString("secretkey"));
        String region = toml.getString("region");
        String endpoint = toml.getString("endpoint");
        String httpProxy = toml.getString("httpProxy");
        S3Config s3Config = S3Config.builder().
            bucket(bucket)
            .accessKey(accessKey)
            .secretKey(secretKey)
            .region(region)
            .endpoint(endpoint)
            .httpProxy(httpProxy)
            .build();
        String ea = entry.getKey();
        S3Config cacheConfig = configCache.getIfPresent(ea);
        if (s3Client.getIfPresent(ea) != null && s3PreSignerCache.getIfPresent(ea) != null && cacheConfig != null && (cacheConfig.hashCode() == s3Config.hashCode())) {
            continue;
        }
        configCache.put(ea, s3Config);
        S3Client newClient = initS3Client(ea);
        S3Presigner newS3Presigner = initS3Presigner(ea);
        s3Client.put(ea, newClient);
        s3PreSignerCache.put(ea, newS3Presigner);
        log.info("update s3 config and client, ea:{}, config:{}, old config:{}",ea, s3Config, configCache);
      }
    });
  }

  public S3Config getS3Config(String ea) {
    return configCache.getIfPresent(ea);
  }
  
  public S3Client initS3Client(String ea) {
    S3Config s3Config = getS3Config(ea);
    S3Client s3Client;
    if (s3Config == null) {
      return null;
    }
    if (s3Config.getHttpProxy() != null && !s3Config.getHttpProxy().isEmpty()) {
      SdkHttpClient httpClient = getProxyHttpClient(s3Config.getHttpProxy());
      s3Client = S3Client.builder()
          .httpClient(httpClient)
          .endpointOverride(URI.create(s3Config.getEndpoint()))
          .credentialsProvider(StaticCredentialsProvider.create(AwsBasicCredentials.create(s3Config.getAccessKey(), s3Config.getSecretKey())))
          .region(Region.of(s3Config.getRegion()))
        .build();
    } else {
      s3Client = S3Client.builder()
          .endpointOverride(URI.create(s3Config.getEndpoint()))
          .credentialsProvider(StaticCredentialsProvider.create(AwsBasicCredentials.create(s3Config.getAccessKey(), s3Config.getSecretKey())))
          .region(Region.of(s3Config.getRegion()))
          .build();
    }
    return s3Client;
  }

  public S3Presigner initS3Presigner(String ea) {
    S3Config s3Config = getS3Config(ea);
    if (s3Config == null) {
      return null;
    }
    return S3Presigner.builder()
          .s3Client(initS3Client(ea))
          .credentialsProvider(StaticCredentialsProvider.create(AwsBasicCredentials.create(s3Config.getAccessKey(), s3Config.getSecretKey())))
          .endpointOverride(URI.create(s3Config.getEndpoint()))
          .region(Region.of(s3Config.getRegion()))
          .build();
  }
  
  private SdkHttpClient getProxyHttpClient(String httpProxy) {
    ProxyConfiguration proxyConfiguration = ProxyConfiguration.builder().endpoint(URI.create("http://" + httpProxy)).build();
    return ApacheHttpClient.builder()
        .proxyConfiguration(proxyConfiguration)
        .build();
  }


  public S3Client getS3Client(String ea) {
    return s3Client.getUnchecked(ea);
  }

  public S3Presigner getS3Presigner(String ea) {
    return s3PreSignerCache.getUnchecked(ea);
  }
}
