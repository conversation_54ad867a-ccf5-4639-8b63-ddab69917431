package com.fxiaoke.file.server.service;

import com.facishare.fsi.proxy.model.warehouse.a.ADownloadFile;
import com.facishare.fsi.proxy.model.warehouse.a.User;
import com.facishare.fsi.proxy.model.warehouse.g.GFileDownload;
import com.facishare.fsi.proxy.service.AFileStorageService;
import com.facishare.fsi.proxy.service.GFileStorageService;
import com.facishare.restful.client.exception.FRestClientException;
import com.facishare.stone.sdk.StoneProxyApi;
import com.facishare.stone.sdk.request.StoneFileDownloadRequest;
import com.facishare.stone.sdk.request.StoneFileUploadRequest;
import com.facishare.stone.sdk.request.StoneImageThumbRequest;
import com.facishare.stone.sdk.response.StoneFileUploadResponse;
import com.fxiaoke.file.server.config.CmsPropertiesConfig;
import com.fxiaoke.file.server.domain.constants.Constant;
import com.fxiaoke.file.server.domain.constants.ErInfo;
import com.fxiaoke.file.server.domain.constants.FileSystemResourceEnum;
import com.fxiaoke.file.server.domain.exception.FileServerException;
import com.fxiaoke.file.server.domain.model.api.AcFileInfo;
import com.fxiaoke.file.server.domain.model.api.UpFileInfo;
import com.fxiaoke.file.server.domain.model.imaginary.ImageDimension;
import com.fxiaoke.file.server.domain.model.imaginary.ImageExifInfo;
import com.fxiaoke.file.server.domain.model.imaginary.PipelineOperation;
import com.fxiaoke.file.server.domain.model.imaginary.PipelineOperationBuilder;
import com.fxiaoke.file.server.domain.model.imaginary.PipelineRequest;
import com.fxiaoke.file.server.help.GrayHelper;
import com.fxiaoke.file.server.utils.ClusterEnvUtils;
import com.fxiaoke.file.server.utils.IgnoreErrorUtil;
import com.fxiaoke.file.server.utils.ImageBaseUtils;
import com.fxiaoke.stone.commons.domain.constant.FileResourceEnum;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j(topic = "StoneStorageService")
public class StoneStorageService {

  private static final String MODULE = "StoneStorageService";

  private final StoneProxyApi nFile;
  private final GFileStorageService gFile;
  private final AFileStorageService aFile;

  private final GrayHelper grayHelper;
  private final CmsPropertiesConfig config;

  private final AtsService atsService;
  private final ImaginaryService imaginaryService;
  private final CrossCloudService crossCloudService;

  // 专属云没有此服务相关基础设施因此不初始化
  @Autowired(required = false)
  IAvatarService avatarService;

  public StoneStorageService(StoneProxyApi nFile, GFileStorageService gFile,
      AFileStorageService aFile, GrayHelper grayHelper, CmsPropertiesConfig cmsPropertiesConfig,
      AtsService atsService, ImaginaryService imaginaryService,
      CrossCloudService crossCloudService) {
    this.nFile = nFile;
    this.gFile = gFile;
    this.aFile = aFile;
    this.grayHelper = grayHelper;
    this.config = cmsPropertiesConfig;
    this.atsService = atsService;
    this.imaginaryService = imaginaryService;
    this.crossCloudService = crossCloudService;
  }

  public String uploadSingleFile(UpFileInfo singleFileInfo, InputStream stream) {
    FileResourceEnum resourceEnum = FileResourceEnum.of(singleFileInfo.getResourceType());
    return switch (resourceEnum) {
      case TC -> uploadTcFile(singleFileInfo, stream);
      case C -> uploadCFile(singleFileInfo, stream);
      case TN -> uploadTnFile(singleFileInfo, stream);
      case N -> uploadNFile(singleFileInfo, stream);
    };
  }

  private String uploadTcFile(UpFileInfo singleFileInf, InputStream stream) {
    StoneFileUploadRequest request = initStoneFileUploadRequest(singleFileInf);
    request.setNeedCdn(true);
    try {
      StoneFileUploadResponse response = nFile.tempFileUploadByStream("n", request, stream);
      return response.getPath();
    } catch (FRestClientException e) {
      throw new FileServerException(e, MODULE, ErInfo.UPLOAD_FILE_TO_STORAGE_FAIL, singleFileInf);
    }
  }

  private String uploadCFile(UpFileInfo singleFileInf, InputStream stream) {
    StoneFileUploadRequest request = initStoneFileUploadRequest(singleFileInf);
    request.setNeedCdn(true);
    try {
      StoneFileUploadResponse response = nFile.uploadByStream("n", request, stream);
      return response.getPath();
    } catch (FRestClientException e) {
      throw new FileServerException(e, MODULE, ErInfo.UPLOAD_FILE_TO_STORAGE_FAIL, singleFileInf);
    }
  }

  private String uploadTnFile(UpFileInfo singleFileInf, InputStream stream) {
    StoneFileUploadRequest request = initStoneFileUploadRequest(singleFileInf);
    try {
      StoneFileUploadResponse response = nFile.tempFileUploadByStream("n", request, stream);
      return response.getPath();
    } catch (FRestClientException e) {
      throw new FileServerException(e, MODULE, ErInfo.UPLOAD_FILE_TO_STORAGE_FAIL, singleFileInf);
    }
  }

  private String uploadNFile(UpFileInfo singleFileInf, InputStream stream) {
    StoneFileUploadRequest request = initStoneFileUploadRequest(singleFileInf);
    try {
      StoneFileUploadResponse response = nFile.uploadByStream("n", request, stream);
      return response.getPath();
    } catch (FRestClientException e) {
      throw new FileServerException(e, MODULE, ErInfo.UPLOAD_FILE_TO_STORAGE_FAIL, singleFileInf);
    }
  }

  private StoneFileUploadRequest initStoneFileUploadRequest(UpFileInfo singleFileInfo) {
    StoneFileUploadRequest request = new StoneFileUploadRequest();
    request.setEa(singleFileInfo.getEa());
    request.setOriginName(singleFileInfo.getFileName());
    request.setFileSize(singleFileInfo.getFileSize());
    request.setEmployeeId(singleFileInfo.getEmployeeId());
    request.setBusiness(Constant.BUSINESS);
    String extension = singleFileInfo.getExtension();
    request.setExtensionName(extension);
    return request;
  }

  /**
   * 根据文件路径类型获取文件 支持NC类型文件和AG类型文件
   *
   * @param info 文件信息
   * @return 文件输入流
   */
  public InputStream getFile(AcFileInfo info) {
    // 根据文件路径前缀确定文件系统资源类型
    FileSystemResourceEnum resourceEnum = FileSystemResourceEnum.of(info.getPath());
    // 根据资源类型选择不同的文件获取方式
    return switch (resourceEnum) {
      // NC类型文件：普通文件和临时文件，可能使用ATS缓存
      case N, TN, C, TC -> getFileByNC(info);
      // AG类型文件：头像文件和群组文件
      case A, TA, G -> getFileByAG(info);
    };
  }

  /**
   * 获取NC类型文件 根据配置和文件类型决定从ATS服务或Stone服务获取
   *
   * @param info 文件信息
   * @return 文件输入流
   */
  private InputStream getFileByNC(AcFileInfo info) {

    // 判断是否使用ATS缓存(默认不使用ATS，需在配置中心显式指定开启)
    String enterpriseAccount = info.getUserInfo().getFileAcEnterpriseAccount();
    boolean useAtsCache = config.isAtsCacheEnable() &&
        grayHelper.isEnableATSCache(enterpriseAccount)
        && info.isImage();

    if (useAtsCache) {
      // 图片文件且启用了ATS缓存，从ATS服务获取
      try {
        return atsService.getNCImageByATS(info);
      } catch (IOException e) {
        // IO类异常降级直连文件服务器
        log.warn("call ats fail,fallback to stone file server,arg:{}", info, e);
        return getNCFileByStone(info);
      }
    } else {
      // 非图片文件或未启用ATS缓存，从Stone服务获取
      return getNCFileByStone(info);
    }
  }

  private InputStream getFileByAG(AcFileInfo info) {
    byte[] fileByte;
    if (info.getPath().startsWith("G")) {
      fileByte = getFileByG(info);
    } else {
      fileByte = getAFileByte(info);
    }
    if (info.isImage() && !info.isGetOriginal()) {
      fileByte = imageProcess(info, fileByte);
    }
    try (ByteArrayInputStream byteStream = new ByteArrayInputStream(fileByte)) {
      return byteStream;
    } catch (IOException e) {
      throw new FileServerException(e, MODULE, ErInfo.ACCESS_IO_EXCEPTION, info);
    }
  }

  private byte[] imageProcess(AcFileInfo info, byte[] imageByte) {
    ImageExifInfo imageDimensions = ImageBaseUtils.getImageDimensions(imageByte);
    boolean formatConsistency = info.getExtension().equals(imageDimensions.getType());
    // 目标分辨率是否高于原图分辨率且目标格式和原图一致直接返回原图
    if (imageDimensions.getImageResolution() <= info.getImageResolution() && formatConsistency) {
      return imageByte;
    }
    ImageDimension compressingWH = ImageBaseUtils.getCompressingWH(imageDimensions.getWidth(),
        imageDimensions.getHeight(), info.getImageResolution());
    List<PipelineOperation> pipelineOperationList = new ArrayList<>();
    // 目标格式和原图不一致,转换为目标格式
    PipelineOperation pipelineOperation;
    if (!formatConsistency) {
      pipelineOperation = PipelineOperationBuilder.thumbnail(compressingWH.getWidth(),
          compressingWH.getHeight(), info.getExtension(), true, 100);
    } else {
      pipelineOperation = PipelineOperationBuilder.thumbnail(compressingWH.getWidth(),
          compressingWH.getHeight());
    }
    pipelineOperationList.add(pipelineOperation);
    PipelineRequest pipelineRequest = PipelineRequest.builder().operations(pipelineOperationList)
        .build();
    try {
      return imaginaryService.processPipeline(imageByte, pipelineRequest);
    } catch (Exception e) {
      // 处理图片异常,直接返回原始数据
      log.error("image process error", e);
      return imageByte;
    }
  }

  private byte[] getAFileByte(AcFileInfo info) {
    ADownloadFile.Arg arg = new ADownloadFile.Arg();
    User user = new User();
    user.setEnterpriseAccount(info.getUserInfo().getFileAcEnterpriseAccount());
    user.setEmployId(info.getUserInfo().getFileAcEmployeeId());
    arg.setUser(user);
    arg.setBusiness(info.getBiz());
    arg.setaPath(info.getPath());
    arg.setFileSecurityGroup(Constant.DEFAULT_SECURITY_GROUP);
    try {
      return aFile.downloadFile(arg).getData();
    } catch (Exception e) {
      throw new FileServerException(e, MODULE, ErInfo.ACCESS_CROSS_FILE_FAIL, info);
    }
  }

  private byte[] getFileByG(AcFileInfo info) {
    GFileDownload.Arg arg = new GFileDownload.Arg();
    arg.downloadUser = String.valueOf(info.getUserInfo().getFileAcEmployeeId());
    arg.gPath = info.getPath();
    try {
      return gFile.downloadFile(arg).data;
    } catch (Exception e) {
      throw new FileServerException(e, MODULE, ErInfo.ACCESS_AVATAR_IMAGE_FAIL, info);
    }
  }

  /**
   * 从Stone文件服务器获取文件 根据文件类型和请求参数决定获取原图或缩略图
   *
   * @param info 文件信息
   * @return 文件输入流
   * @throws FileServerException 当文件不存在或访问失败时抛出
   */
  public InputStream getNCFileByStone(AcFileInfo info) {

    String enterpriseAccount = info.getUserInfo().getFileOwnerEnterpriseAccount();
    Integer employeeId = info.getReportEmployeeId();
    String path = info.getPath();
    String fileType = info.getExtension();
    String business = info.getBiz();
    boolean thumb = info.isImage() && !info.isGetOriginal();
    try {

      // 如果是图片并且需要获取缩略图
      if (thumb) {
        StoneImageThumbRequest request = new StoneImageThumbRequest();
        request.setEa(enterpriseAccount);
        request.setBusiness(business);
        request.setPath(path);
        request.setEmployeeId(employeeId);
        request.setFileType(fileType);
        request.setToHeight(info.getHeight());
        request.setToWidth(info.getWidth());
        request.setSecurityGroup(Constant.DEFAULT_SECURITY_GROUP);
        return nFile.getImageThumb(request);
      } else {
        // 获取原文件
        StoneFileDownloadRequest request = new StoneFileDownloadRequest();
        request.setEa(enterpriseAccount);
        request.setEmployeeId(employeeId);
        request.setBusiness(business);
        request.setPath(path);
        request.setSecurityGroup(Constant.DEFAULT_SECURITY_GROUP);
        request.setFileType(fileType);
        request.setCancelRemoteThumb(true);
        return nFile.downloadStream(request);
      }

    } catch (FRestClientException e) {
      String causeCodeStr = e.getCode();

      //TODO：兼容头像文件请求
      if (info.isImage()&&missMeta(causeCodeStr)) {
        InputStream avatarFileStream = getAvatarFile(info);
        if (avatarFileStream != null) {
          log.warn("compatible avatar file successful,arg:{}", info);
          return avatarFileStream;
        }
      }

      // 处理文件不存在或已过期的情况
      if (ignore(causeCodeStr)) {
        throw new FileServerException(MODULE, ErInfo.ACCESS_EXPIRED_OR_NOT_EXIST_FILE, info);
      }
      // 其他Stone服务访问失败情况
      throw new FileServerException(e, MODULE, ErInfo.ACCESS_FILE_FAIL_BY_STONE, info);
    }
  }


  public void getAvatarFile(String path, OutputStream outputStream) {
    if (ClusterEnvUtils.isExistsAvatarFileSystem()) {
      avatarService.downloadAvatarToOutStream(path, outputStream);
    } else {
      throw new FileServerException(MODULE, ErInfo.AVATAR_FILE_SYSTEM_NOT_EXISTS, path);
    }
  }

  private boolean missMeta(String errStr) {
    if (errStr == null || errStr.isEmpty()) {
      return false;
    }
    return IgnoreErrorUtil.isMiseMeta(errStr);
  }

  public InputStream getAvatarFile(AcFileInfo info) {
    String ea = info.getUserInfo().getFileOwnerEnterpriseAccount();
    return crossCloudService.getAvatarFile(ea, info.getPath(),
        info.getExtension());
  }


  private boolean ignore(String errStr) {
    if (errStr == null || errStr.isEmpty()) {
      return false;
    }
    return errStr.equals("400") || IgnoreErrorUtil.isIgnore(errStr);
  }


}
