package com.fxiaoke.file.server.service;

import com.facishare.asm.api.auth.AuthXC;
import com.facishare.asm.api.model.CookieToAuth;
import com.facishare.asm.api.service.ActiveSessionAuthorizeService;
import com.facishare.converter.EIEAConverter;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.fscore.EnterpriseData;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.enterpriserelation2.arg.AuthWithoutEaArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.AuthUserResult;
import com.fxiaoke.enterpriserelation2.service.AuthService;
import com.fxiaoke.file.server.config.CmsPropertiesConfig;
import com.fxiaoke.file.server.domain.constants.ErInfo;
import com.fxiaoke.file.server.domain.exception.FileServerException;
import com.fxiaoke.file.server.domain.model.api.AuthInfo;
import com.fxiaoke.file.server.domain.model.api.EnterpriseBaseInfo;
import com.github.autoconf.helper.ConfigEiHelper;
import com.google.common.base.Strings;
import java.util.Date;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j(topic = "asmService")
public class AsmService {
  private static final String MODULE = "AsmService";

  private final AuthService authService;
  private final EIEAConverter eieaConverter;
  private final EnterpriseEditionService enterpriseEditionService;
  private final ActiveSessionAuthorizeService activeSessionAuthorizeService;
  private final CmsPropertiesConfig cmsPropertiesConfig;

  public AsmService(AuthService authService,EIEAConverter eieaConverter, EnterpriseEditionService enterpriseEditionService,
      ActiveSessionAuthorizeService activeSessionAuthorizeService, CmsPropertiesConfig cmsPropertiesConfig) {
    this.authService = authService;
    this.eieaConverter = eieaConverter;
    this.enterpriseEditionService = enterpriseEditionService;
    this.activeSessionAuthorizeService = activeSessionAuthorizeService;
    this.cmsPropertiesConfig = cmsPropertiesConfig;
  }

  public int getEid(String ea) {
    return eieaConverter.enterpriseAccountToId(ea);
  }

  public String getEa(int eid) {
    return eieaConverter.enterpriseIdToAccount(eid);
  }

  public Optional<AuthInfo> getAuthXC(String cookie) {
    try {
      if (!Strings.isNullOrEmpty(cookie)) {
        CookieToAuth.Argument arg = new CookieToAuth.Argument();
        arg.setCookie(cookie);
        CookieToAuth.Result<AuthXC> result = activeSessionAuthorizeService.cookieToAuthXC(arg);
        if (result != null && result.getBody() != null) {
          AuthXC authXC = result.getBody();
          AuthInfo authInfo = new AuthInfo();
          // 设置企业账号
          authInfo.setEnterpriseAccount(authXC.getEnterpriseAccount());
          // 设置企业ID
          authInfo.setEnterpriseId(authXC.getEnterpriseId());
          // 设置员工ID
          authInfo.setEmployeeId(authXC.getEmployeeId());
          log.debug("get the authInfo from auth message success,authInfo:{}", authInfo);
          return Optional.of(authInfo);
        }
      }
      logWarn("get the authInfo fail,cookie:{}", cookie);
      return Optional.empty();
    } catch (Exception e) {
      throw new FileServerException(MODULE, ErInfo.REMOTE_SERVICE_EXCEPTION,
          "activeSessionAuthorizeService.cookieToAuthXC");
    }
  }

  public Optional<AuthInfo> getEm6(String cookie){
    try {
      if (!Strings.isNullOrEmpty(cookie)) {
        AuthWithoutEaArg arg = new AuthWithoutEaArg();
        arg.setErInfo(cookie);
        RestResult<AuthUserResult> result = authService.authWithoutEa(
            HeaderObj.newInstance(cmsPropertiesConfig.getEm6DefaultAppid(), null, null, null), arg);
        if (result.isSuccess()) {
          AuthUserResult authUserResult = result.getData();
          AuthInfo authInfo = AuthInfo.ofDownstream(authUserResult.getDownstreamOuterTenantId(),
              authUserResult.getDownstreamOuterUid());
          authInfo.setUpstreamEa(authUserResult.getUpstreamEa());
          log.debug("get the authInfo from em6 message success,authInfo:{}", authInfo);
          return Optional.of(authInfo);
        }
      }
      logWarn("get the authInfo fail,cookie:{}", cookie);
      return Optional.empty();
    }catch (Exception e){
      throw new FileServerException(MODULE, ErInfo.REMOTE_SERVICE_EXCEPTION,
          "authService.authWithoutEa");
    }
  }

  // todo:如果每次都需要调用接口，可以考虑在本层添加缓存或者更上层添加缓存
  public EnterpriseBaseInfo getBaseEnterpriseInfo(String enterpriseAccount) {
    GetEnterpriseDataArg arg = new GetEnterpriseDataArg();
    arg.setEnterpriseAccount(enterpriseAccount);
    EnterpriseData enterpriseData = enterpriseEditionService.getEnterpriseData(arg)
        .getEnterpriseData();
    String tenantId = String.valueOf(enterpriseData.getEnterpriseId());
    String fastDfsAddress = enterpriseData.getDfsAddress();
    Date createTime = enterpriseData.getCreateTime();
    return new EnterpriseBaseInfo(enterpriseAccount, tenantId, createTime, fastDfsAddress);
  }

  // 判断当前用户是否是当前云的用户
  public boolean isCurrentCloudUser(String tenantId) {
    return ConfigEiHelper.getInstance().isCurrentCloud(tenantId);
  }

  /**
   * 获取authInfo
   * 
   * @param authXC FSAuthXC cookie值
   * @param erInfo ERInfo cookie值
   * @return AuthInfo对象
   */
  public AuthInfo getAuthInfo(String authXC, String erInfo) {
    Optional<AuthInfo> authInfo = getAuthXC(authXC);
    return authInfo.orElseGet(() -> getEm6(erInfo)
        .orElseThrow(() -> new FileServerException(MODULE, "Authentication failed, authInfo is null")));
  }

  private void logWarn(String template,Object arg) {
    log.info(template,arg);
  }

}
