package com.fxiaoke.file.server.service;

import com.fxiaoke.file.server.domain.constants.ErInfo;
import com.fxiaoke.file.server.domain.exception.FileServerException;
import com.fxiaoke.stone.commons.StoneAuthClient;
import com.fxiaoke.stone.commons.domain.model.ASKOmit;
import org.springframework.stereotype.Service;

@Service
public class StoneAuthService {
  private static final String MODULE="StoneAuthService";
  private final StoneAuthClient stoneAuthClient;
  public StoneAuthService(StoneAuthClient stoneAuthClient) {
    this.stoneAuthClient = stoneAuthClient;
  }

  public String getSkByAccessKey(String accessKey) {
    try {
      return stoneAuthClient.getSkByAccessKey(accessKey);
    } catch (Exception e) {
      throw new FileServerException(MODULE, ErInfo.AUTH_INVALID_AK, accessKey);
    }
  }

  public ASKOmit getCredential(String ea) {
    try {
      return stoneAuthClient.getSkBySts(ea);
    } catch (Exception e) {
      throw new FileServerException(MODULE, ErInfo.AUTH_INVALID_AK, ea);
    }
  }
}
