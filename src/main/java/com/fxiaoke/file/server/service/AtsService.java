package com.fxiaoke.file.server.service;

import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.file.server.config.CmsPropertiesConfig;
import com.fxiaoke.file.server.domain.constants.ErInfo;
import com.fxiaoke.file.server.domain.exception.FileServerException;
import com.fxiaoke.file.server.domain.model.api.AcFileInfo;
import com.fxiaoke.file.server.help.GrayHelper;
import com.fxiaoke.file.server.help.SimpleLoadBalancerHelper;
import com.fxiaoke.file.server.utils.IgnoreErrorUtil;
import java.io.IOException;
import java.io.InputStream;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

@Service
@Slf4j(topic = "AtsService")
public class AtsService {

  private static final String MODULE = "AtsService";

  private final Headers atsHeaders;
  private final GrayHelper grayHelper;
  private final CmsPropertiesConfig config;
  private final OkHttpSupport okHttpClient;
  private final CrossCloudService crossCloudService;
  private final SimpleLoadBalancerHelper loadBalancer;

  public AtsService(CmsPropertiesConfig cmsPropertiesConfig, GrayHelper grayHelper,
      @Qualifier("atsOkHttpClient") OkHttpSupport atsOkHttpClient,
      CrossCloudService crossCloudService, SimpleLoadBalancerHelper loadBalancer) {
    this.atsHeaders = new Headers.Builder().add("accept", "application/octet-stream")
        .add("Host", cmsPropertiesConfig.getAtsServerNodeHost()).build();
    this.grayHelper = grayHelper;
    this.config = cmsPropertiesConfig;
    this.okHttpClient = atsOkHttpClient;
    this.crossCloudService = crossCloudService;
    this.loadBalancer = loadBalancer;
  }

  /**
   * 从ATS服务获取图片 根据传入的参数判断获取原图还是缩略图 如果ATS服务异常，会自动降级到Stone服务
   *
   * @param info 文件信息
   * @return 图片输入流
   */
  public InputStream getNCImageByATS(AcFileInfo info) throws IOException, FileServerException {
    String path = info.getPath();
    String ownerEnterpriseAccount = info.getUserInfo().getFileOwnerEnterpriseAccount();
    String atsNodeBaseUrl = loadBalancer.getAtsNode(path);
    String url;

    // 构建ATS请求URL
    if (info.isGetOriginal()) {
      // 判断是否需要移除文件类型以获取不经处理的原图（处理Exif信息混淆问题）
      boolean isOriginalImageGray = grayHelper.isStoneOriginalImageGray(
          info.getUserInfo().getFileAcEnterpriseAccount());
      String fileExtension = isOriginalImageGray ? "" : info.getExtension();
      url = atsNodeBaseUrl + String.format(config.getAtsRequestTemplate(), "n",
          ownerEnterpriseAccount, path, fileExtension);
    } else {
      // 缩略图请求
      url = atsNodeBaseUrl + String.format(config.getAtsThumbRequestTemplate(), "n",
          ownerEnterpriseAccount, path, info.getExtension(), info.getWidth(), info.getHeight());
    }

    return call(atsHeaders, url, info);
  }


  /**
   * 调用ATS服务获取文件流 注意：返回的流由Tomcat容器负责管理和关闭
   *
   * @param headers HTTP请求头
   * @param url     ATS服务URL
   * @return 文件输入流
   * @throws IOException         当ATS服务异常时抛出，用于触发降级到Stone服务
   * @throws FileServerException 当文件不存在或访问失败时抛出
   */
  public InputStream call(Headers headers, String url, AcFileInfo info)
      throws IOException, FileServerException {
    Request request = new Request.Builder().url(url).headers(headers).build();

    return (InputStream) okHttpClient.syncExecute(request, new SyncCallback() {
      @Override
      public Object response(Response response) throws Exception {

        if (response.isSuccessful() && response.body() != null) {
          return response.body().byteStream();
        }

        if (response.body() != null) {
          // 处理错误响应
          String message = response.body().string();
          response.body().close();  // 错误情况下关闭响应体

          // catch 头像文件
          if (missMeta(message)) {
            InputStream avatarFileStream = getAvatarFile(info);
            if (avatarFileStream != null) {
              return avatarFileStream;
            }
          }

          // ATS异常时 自动降级请求到STONE
          // 仅匹配500以上错误码认为ATS异常,防止错误请求 重复请求到STONE服务
          if (response.code() > 500) {
            throw new IOException(
                "call ats error,code:" + response.code() + "message:" + response.message() + "url:"
                    + url + "info:" + info);
          }

          // 检查是否为文件不存在或已过期
          if (ignore(response.code(), message)) {
            throw new FileServerException(MODULE, ErInfo.ACCESS_EXPIRED_OR_NOT_EXIST_FILE, url,
                message);
          }
        }

        // 其他错误情况
        throw new FileServerException(MODULE, ErInfo.ACCESS_FILE_FAIL_BY_ATS, url);
      }
    });
  }

  private boolean missMeta(String errStr) {
    return IgnoreErrorUtil.isMiseMeta(errStr);
  }

  public InputStream getAvatarFile(AcFileInfo info){
    String ea = info.getUserInfo().getFileOwnerEnterpriseAccount();
    return crossCloudService.getAvatarFile(ea, info.getPath(),
        info.getExtension());
  }

  private boolean ignore(int errCode, String errStr) {
    if (errCode == 400) {
      return true;
    }

    if (errStr == null || errStr.isEmpty()) {
      return false;
    }

    return errStr.equals("400") || IgnoreErrorUtil.isIgnore(errStr);
  }

}
