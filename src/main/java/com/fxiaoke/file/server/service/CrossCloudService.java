package com.fxiaoke.file.server.service;

import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.file.server.config.CmsPropertiesConfig;
import java.io.InputStream;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

@Service
@Slf4j(topic = "CrossCloudService")
public class CrossCloudService {

  private final CmsPropertiesConfig config;
  private final OkHttpSupport okHttpClient;

  public CrossCloudService(CmsPropertiesConfig cmsPropertiesConfig,
      @Qualifier("crossCloudOkHttpClient") OkHttpSupport crossCloudOkHttpClient) {
    this.config = cmsPropertiesConfig;
    this.okHttpClient = crossCloudOkHttpClient;
  }

  private final Headers headers = new Headers.Builder().add("Connection", "close").build();

  public InputStream getAvatarFile(String ea, String path, String ext) {
    String context = String.format("/FSC/EM/Avatar/GetAvatar?path=%s.%s&ea=%s", path, ext, ea);
    String url = config.getFsImageServerHost() + context;
    Request request = new Request.Builder().url(url).headers(headers).build();
    log.debug("CrossCloudService getAvatarFile start url:{}", url);
    try {
      return (InputStream) okHttpClient.syncExecute(request, new SyncCallback() {
        @Override
        public Object response(Response response) {

          if (response.isSuccessful() && response.body() != null) {
            log.info("CrossCloudService getAvatarFile success url:{}", url);
            return response.body().byteStream();
          }

          if (response.body() != null) {
            // 错误情况下关闭响应体
            response.body().close();
          }

          return null;
        }
      });
    } catch (Exception e) {
      log.error("CrossCloudService getAvatarFile error url:{}", url, e);
      return null;
    }
  }


}
