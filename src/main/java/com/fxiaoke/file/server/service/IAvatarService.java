package com.fxiaoke.file.server.service;

import com.fxiaoke.file.server.domain.entity.AvatarFileMeta;
import com.fxiaoke.file.server.domain.exception.FileServerException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Optional;

/**
 * 头像文件服务相关接口 支持查询头像文件元数据、保存头像文件元数据 支持获取头像文件
 */
public interface IAvatarService {

  /**
   * 根据path查询AvatarFileMeta 当查询不到时返回Optional.empty()
   *
   * @param path 文件路径
   * @return AvatarFileMeta
   * @throws FileServerException 如果数据库异常
   */
  Optional<AvatarFileMeta> findMetaByPath(String path);


  /**
   * 仅保存AvatarFileMeta到数据库
   *
   * @param avatarFileMeta AvatarFileMeta
   * @throws FileServerException 如果数据库异常
   */
  void saveMeta(AvatarFileMeta avatarFileMeta);

  /**
   * 根据头像文件路径获取头像文件
   *
   * @param path 头像文件路径
   * @return 头像文件 如果不存在则返回默认头像
   * @throws FileServerException FastDFS文件下载异常
   */
  InputStream getAvatarFileByPath(String path,boolean isDefaultAvatar);

  void downloadAvatarToOutStream(String path, OutputStream outputStream);
}
