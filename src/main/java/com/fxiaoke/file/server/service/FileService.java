package com.fxiaoke.file.server.service;

import cn.hutool.core.codec.Base64;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.file.server.config.CmsPropertiesConfig;
import com.fxiaoke.file.server.domain.constants.ErInfo;
import com.fxiaoke.file.server.domain.entity.NFileMeta;
import com.fxiaoke.file.server.domain.exception.FileServerException;
import com.fxiaoke.file.server.domain.model.api.*;
import com.fxiaoke.file.server.domain.model.api.request.*;
import com.fxiaoke.file.server.domain.model.imaginary.ImageDimension;
import com.fxiaoke.file.server.domain.vo.request.ChunkUploadCompleteRequest;
import com.fxiaoke.file.server.domain.vo.request.PartInfo;
import com.fxiaoke.file.server.domain.vo.request.S3UploadRequest;
import com.fxiaoke.file.server.domain.vo.response.S3UploadResponse;
import com.fxiaoke.file.server.help.GrayHelper;
import com.fxiaoke.file.server.utils.*;
import com.fxiaoke.stone.commons.domain.model.ASKOmit;
import com.fxiaoke.stone.commons.domain.utils.SignatureUtil;
import com.fxiaoke.stone.commons.domain.utils.Strings;
import com.fxiaoke.stone.commons.help.SecureCryptHelp;
import com.google.common.base.Splitter;

import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.google.gson.Gson;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import com.fxiaoke.file.server.help.BigFileHelper;
import software.amazon.awssdk.services.s3.model.CompleteMultipartUploadResponse;
import software.amazon.awssdk.services.s3.model.CompletedPart;

@Service
@Slf4j(topic = "FileService")
public class FileService {

  private static final String MODULE = "VisitFileService";
  private final StoneAuthService stoneAuthService;
  private final AsmService asmService;
  private final GrayHelper grayHelper;
  private final FileMetaService fileMetaService;
  private final StoneStorageService stoneStorageService;
  private final OkHttpSupport httpSupport;
  private final CmsPropertiesConfig config;
  private final BigFileHelper bigFileHelper;

  @Resource
  private S3FileService s3FileService;
  @Resource
  private FileDownloadLimiterUtil fileDownloadLimiterUtil;

  public FileService(StoneAuthService stoneAuthService, AsmService asmService,
                     GrayHelper grayHelper, CmsPropertiesConfig cmsPropertiesConfig, FileMetaService fileMetaService,
                     StoneStorageService stoneStorageService,
                     @Qualifier("httpSupport") OkHttpSupport httpSupport,
                     BigFileHelper bigFileHelper) {
    this.stoneAuthService = stoneAuthService;
    this.asmService = asmService;
    this.grayHelper = grayHelper;
    this.config = cmsPropertiesConfig;
    this.fileMetaService = fileMetaService;
    this.stoneStorageService = stoneStorageService;
    this.httpSupport = httpSupport;
    this.bigFileHelper = bigFileHelper;
  }

  /**
   * 根据签名获取文件
   *
   * @param nFileAcRequest 请求参数
   * @return 文件流
   */
  public InputStream getFileBySign(NFileAcRequest nFileAcRequest) {
    // 校验签名是否过期
    CodingUtil.assertSignNotExpire(nFileAcRequest.getEts(), nFileAcRequest);
    return getFile(nFileAcRequest);
  }

  /**
   * 根据上游企业Cookie获取文件
   *
   * @param nFileAcRequest 请求参数
   * @param authXC         上游企业Cookie
   * @return 文件流
   */
  public InputStream getFileByAuthXC(NFileAcRequest nFileAcRequest, String authXC) {
    assertAuthXCValid(nFileAcRequest, authXC);
    return getFile(nFileAcRequest);
  }

  /**
   * 根据下游企业Cookie获取文件
   *
   * @param nFileAcRequest 请求参数
   * @param erInfo         下游企业Cookie
   * @return 文件流
   */
  public InputStream getFileByEm6(NFileAcRequest nFileAcRequest, String erInfo) {
    assertEm6Valid(nFileAcRequest, erInfo);
    return getFile(nFileAcRequest);
  }

  /**
   * 根据Cookie获取文件 鉴权优先级:上游Cookie>下游Cookie
   *
   * @param nFileAcRequest 请求参数
   * @param authXC         上游企业Cookie
   * @param erInfo         下游企业Cookie
   * @return 文件流
   */
  public InputStream getFileByCookie(NFileAcRequest nFileAcRequest, String authXC, String erInfo) {

    if (isAuthXCValidByEi(nFileAcRequest, authXC)) {
      return getFile(nFileAcRequest);
    }

    if (isEm6ValidByUpstreamEi(nFileAcRequest, erInfo)) {
      return getFile(nFileAcRequest);
    }

    throw new FileServerException(MODULE, ErInfo.AUTH_FAIL, nFileAcRequest);
  }

  /**
   * 根据签名与上游企业Cookie获取文件 鉴权优先级:签名>Cookie
   *
   * @param nFileAcRequest 请求参数
   * @param authXC         上游企业Cookie
   * @return 文件流
   */
  public InputStream getFileByConditionAuthXC(NFileAcRequest nFileAcRequest, String authXC) {
    // 校验签名是否过期
    if (CodingUtil.assertSignExpire(nFileAcRequest.getEts())) {
      Optional<AuthInfo> authInfoOptional = asmService.getAuthXC(authXC);
      if (authInfoOptional.isPresent()) {
        AuthInfo authInfo = authInfoOptional.get();
        // 验证签名Acid与Cookie身份是否匹配
        if (!authInfo.getAcid().equals(nFileAcRequest.getAcid())) {
          throw new FileServerException(MODULE, ErInfo.AUTH_USER_INFO_NO_MATCH, nFileAcRequest,
              authInfo);
        }
        log.info("Get NFile condition authXC success,authInfo:{}", authInfo);
      } else {
        throw new FileServerException(MODULE, ErInfo.AUTH_FAIL, nFileAcRequest);
      }
    }
    return getFile(nFileAcRequest);
  }

  /**
   * 根据签名与下游企业Cookie获取文件 鉴权优先级:签名>Cookie
   *
   * @param nFileAcRequest 请求参数
   * @param erInfo         下游企业Cookie
   * @return 文件流
   */
  public InputStream getFileByConditionEm6(NFileAcRequest nFileAcRequest, String erInfo) {
    // 校验签名是否过期
    if (CodingUtil.assertSignExpire(nFileAcRequest.getEts())) {
      String acid = nFileAcRequest.getAcid();
      Optional<AuthInfo> authInfoOptional = asmService.getEm6(erInfo);
      if (authInfoOptional.isPresent()) {
        AuthInfo authInfo = authInfoOptional.get();
        // 验证签名Acid中下游企业身份与Cookie身份是否匹配
        if (!acid.endsWith(authInfo.getDownstreamAcid())) {
          throw new FileServerException(MODULE, ErInfo.AUTH_USER_INFO_NO_MATCH, nFileAcRequest,
              erInfo);
        }
        log.info("Get NFile condition erInfo success,authInfo:{}", authInfo);
      } else {
        throw new FileServerException(MODULE, ErInfo.AUTH_EXPIRED_SIGN, nFileAcRequest);
      }
    }
    return getFile(nFileAcRequest);
  }

  /**
   * 获取C文件
   *
   * @param cFileAcRequest 请求参数
   * @return 文件流
   */
  public InputStream getCFile(CFileAcRequest cFileAcRequest) {
    // 验证请求参数是否被篡改
    CodingUtil.assertMessageDigest(cFileAcRequest.getDsSignRaw(), cFileAcRequest.getDs());
    // 获取文件信息
    AcFileInfo acFileInfo = getAcFileInfoByCFileAcRequest(cFileAcRequest);
    log.info("getCFile acFileInfo:{}", acFileInfo);
    return stoneStorageService.getFile(acFileInfo);
  }

  /**
   * 获取头像文件
   *
   * @param avatarRequest 头像请求参数
   * @param outputStream  输出流 （用于底层回调写入文件流）
   */
  public void getAvatarFile(AvatarRequest avatarRequest, OutputStream outputStream) {
    // 验证消息签名
    String raw = SignatureUtil.generatorRaw(avatarRequest.getFid(), avatarRequest.getAcid(),
        avatarRequest.getFn());
    CodingUtil.assertMessageDigest(raw, avatarRequest.getDs());
    String path = avatarRequest.getFid();
    log.info("getAvatarFile path:{}", path);
    stoneStorageService.getAvatarFile(path, outputStream);
  }

  /**
   * 验证上游企业Cookie是否有效并比较身份与Acid是否匹配
   *
   * @param nFileAcRequest 请求参数
   * @param authXC         上游企业Cookie
   * @throws FileServerException Cookie无效
   */
  private void assertAuthXCValid(NFileAcRequest nFileAcRequest, String authXC) {
    if (authXC != null && !authXC.isEmpty()) {
      Optional<AuthInfo> authInfoOptional = asmService.getAuthXC(authXC);
      if (authInfoOptional.isPresent()) {
        AuthInfo authInfo = authInfoOptional.get();
        if (!authInfo.getAcid().equals(nFileAcRequest.getAcid())) {
          // 验证签名Acid与Cookie身份是否匹配
          throw new FileServerException(MODULE, ErInfo.AUTH_USER_INFO_NO_MATCH, nFileAcRequest,
              authInfo);
        }
      } else {
        // 是否是有效的Cookie
        throw new FileServerException(MODULE, ErInfo.AUTH_FAIL, nFileAcRequest);
      }
    } else {
      // 缺少用户信息
      throw new FileServerException(MODULE, ErInfo.AUTH_MISS_USER_INFO, nFileAcRequest);
    }
  }

  /**
   * 验证下游企业Cookie是否有效并比较身份与Acid是否匹配
   *
   * @param nFileAcRequest 请求参数
   * @param erInfo         下游企业Cookie
   * @throws FileServerException Cookie无效
   */
  private void assertEm6Valid(NFileAcRequest nFileAcRequest, String erInfo) {
    if (erInfo != null && !erInfo.isEmpty()) {
      Optional<AuthInfo> authInfoOptional = asmService.getEm6(erInfo);
      if (authInfoOptional.isPresent()) {
        AuthInfo authInfo = authInfoOptional.get();
        if (!nFileAcRequest.getAcid().endsWith(authInfo.getDownstreamAcid())) {
          throw new FileServerException(MODULE, ErInfo.AUTH_USER_INFO_NO_MATCH, nFileAcRequest,
              authInfo);
        }
      } else {
        // 是否是有效的Cookie
        throw new FileServerException(MODULE, ErInfo.AUTH_FAIL, nFileAcRequest);
      }
    } else {
      // 缺少用户信息
      throw new FileServerException(MODULE, ErInfo.AUTH_MISS_USER_INFO, nFileAcRequest);
    }
  }

  /**
   * 根据上游企业Cookie验证用户身份是否有效,并比较Ei与Acid是否匹配
   *
   * @param nFileAcRequest 请求参数
   * @param authXC         上游企业Cookie
   * @return 是否有效
   */
  private boolean isAuthXCValidByEi(NFileAcRequest nFileAcRequest, String authXC) {
    if (authXC != null && !authXC.isEmpty()) {
      Optional<AuthInfo> authInfoOptional = asmService.getAuthXC(authXC);
      if (authInfoOptional.isPresent()) {
        AuthInfo authInfo = authInfoOptional.get();
        // 仅比较企业EI是否一致 (Cookie请求参数上不携带完整Acid)
        return authInfo.getEi().equals(nFileAcRequest.getAcid());
      }
    }
    return false;
  }

  /**
   * 根据下游企业Cookie验证用户身份是否有效,并比较Ei与Acid是否匹配
   *
   * @param nFileAcRequest 请求参数
   * @param erInfo         下游企业Cookie
   * @return 是否有效
   */
  private boolean isEm6ValidByUpstreamEi(NFileAcRequest nFileAcRequest, String erInfo) {
    if (erInfo != null && !erInfo.isEmpty()) {
      Optional<AuthInfo> authInfoOptional = asmService.getEm6(erInfo);
      if (authInfoOptional.isPresent()) {
        AuthInfo authInfo = authInfoOptional.get();
        if (Strings.isNotNullOrEmpty(authInfo.getUpstreamEa())) {
          // 上游企业Ea换Ei
          String eid = String.valueOf(asmService.getEid(authInfo.getUpstreamEa()));
          return eid.equals(nFileAcRequest.getAcid());
        }
      }
    }
    return false;
  }

  private InputStream getFile(NFileAcRequest nFileAcRequest) {
    // 验证请求参数是否被篡改
    CodingUtil.assertMessageDigest(nFileAcRequest.getDsSignRaw(), nFileAcRequest.getDs());
    // 验证访问签名
    String acSk = stoneAuthService.getSkByAccessKey(nFileAcRequest.getAk());
    CodingUtil.assertAcSignConsist(acSk, nFileAcRequest.getAcSignRaw(), nFileAcRequest.getSig());
    // 获取文件基础信息
    FidInfo fidInfo = getFidInfoByFidStr(nFileAcRequest.getFid());
    // 获取访问者信息
    AcidInfo acidInfo = getAcidInfoByAcidStr(nFileAcRequest.getAcid());
    // 获取文件信息
    AcFileInfo acFileInfo = getAcFileInfoBySignAcRequest(nFileAcRequest, fidInfo, acidInfo);
    // A|TA|G文件的元数据签名可能不在本环境,因此不进行元数据签名校验 仅校验访问签名即可
    // 用户自签名访问没有元数据签名,因此不校验元数据签名,仅校验访问签名
    if (nFileAcRequest.isVerifyMetaSign() && acFileInfo.isMetaVerifySign()) {
      // 根据文件元数据签名获取SK
      String fileSK = stoneAuthService.getSkByAccessKey(fidInfo.getAccessKey());
      // 验证元数据签名
      CodingUtil.assertMetaDataSignConsist(fileSK, acFileInfo.getMetaSignRaw(),
          fidInfo.getSignature());
    }

    // 判断是否命中强制压缩原图查看的灰度
    if (isCompressOriginal(nFileAcRequest, acFileInfo)) {
      modifyAcFileInfoByRes(acFileInfo);
    }

    log.info("Get NFile acFileInfo:{}", acFileInfo);
    return stoneStorageService.getFile(acFileInfo);
  }

  private boolean isCompressOriginal(NFileAcRequest nFileAcRequest, AcFileInfo acFileInfo) {

    // 图片查看效果最终呈现需要 以访问企业的灰度配置 为准
    UserInfo userInfo = acFileInfo.getUserInfo();
    if (userInfo != null) {
      String fileAcEnterpriseAccount = userInfo.getFileAcEnterpriseAccount();
      boolean forceCompressOriginalImage = grayHelper.isForceCompressOriginalImage(
          fileAcEnterpriseAccount);
      // 必须为内联查看、图片类型且获取原图、并且为NFile类型
      return forceCompressOriginalImage &&
          // 由于图片类特别是缩略图类请求占大比例请求因此先行判断宽高值
          acFileInfo.isGetOriginal() && acFileInfo.isImage()
          && nFileAcRequest.isInline()
          && acFileInfo.isEnterpriseFile();
    }

    return false;
  }

  private boolean isImageByNFileMeta(NFileMeta nFileMeta){

    String mimeType = nFileMeta.getMimeType();
    if (StrUtils.notBlank(mimeType) && FileInfoUtil.isSupportImageByMimeType(mimeType)) {
      return true;
    }

    String extensionName = nFileMeta.getExtension();
    return StrUtils.notBlank(extensionName) && FileInfoUtil.isSupportImageByExtension(
        extensionName);
  }

  private void modifyAcFileInfoByRes(AcFileInfo acFileInfo){
    // 修改acFileInfo从而强制压缩原图
    String path = acFileInfo.getPath();
    String enterpriseAccount = acFileInfo.getUserInfo().getFileOwnerEnterpriseAccount();
    try {
      NFileMeta nFileMeta = fileMetaService.find(enterpriseAccount, path);
      boolean isImg = isImageByNFileMeta(nFileMeta);
      boolean needsResize = config.getMaxImageSize() < nFileMeta.getSize();
      int res = nFileMeta.getWidth() * nFileMeta.getHeight();
      if (isImg && needsResize && res > 0) {
        ImageDimension compressingWH = ImageBaseUtils.getCompressingWH(nFileMeta.getWidth(),
            nFileMeta.getHeight(), config.getMaxCompressRes());
        acFileInfo.setWidth(compressingWH.getWidth());
        acFileInfo.setHeight(compressingWH.getHeight());
        log.info("force compress original image,modify acFileInfo:{}", acFileInfo);
      }
      // 发生任何异常都不影响文件访问,仅记录日志
    }catch (Exception e) {
      log.warn("force compress original image,find file meta fail:",e);
    }
  }

  /**
   * 解密Fid并从中获取文件相关的信息,支持签名非签名,最低要求包含包含文件所有者信息
   *
   * @param fidStr 加密后的文件信息字符串(相同的信息每次加密获取的Fid是不同的)
   * @return 文件信息
   */
  private FidInfo getFidInfoByFidStr(String fidStr) {
    return FidInfo.of(fidStr);
  }

  /**
   * 解密Acid并从中获取访问者信息
   *
   * @param acidStr 访问者信息调用链 格式一: acEI.acEmployeeId 格式二:
   *                acEI.acEmployeeId.acOutEnterpriseId.acOutEmployeeId
   * @return 访问者信息
   */
  private AcidInfo getAcidInfoByAcidStr(String acidStr) {
    return AcidInfo.of(acidStr);
  }

  /**
   * 根据文件信息和访问者信息获取用户信息
   *
   * @param acidInfo 访问者信息
   * @param fidInfo  文件信息(包含文件所有企业信息)
   * @return 用户信息
   */
  private UserInfo getUserInfoByAcidAndFid(AcidInfo acidInfo, FidInfo fidInfo) {
    UserInfo userInfo = new UserInfo();
    // 设置文件所有者信息
    userInfo.setFileOwnerTenantId(fidInfo.getFileOwnerTenantId());
    userInfo.setFileOwnerEnterpriseAccount(asmService.getEa(fidInfo.getFileOwnerTenantId()));
    // 设置文件访问者信息
    userInfo.setFileAcTenantId(acidInfo.getFileAcTenantId());
    userInfo.setFileAcEnterpriseAccount(asmService.getEa(acidInfo.getFileAcTenantId()));
    userInfo.setFileAcEmployeeId(acidInfo.getFileAcEmployeeId());
    userInfo.setFileAcOutTenantId(acidInfo.getFileAcOutTenantId());
    userInfo.setFileAcOutEmployeeId(acidInfo.getFileAcOutEmployeeId());
    return userInfo;
  }

  private AcFileInfo getAcFileInfoBySignAcRequest(NFileAcRequest nFileAcRequest, FidInfo fidInfo,
      AcidInfo acidInfo) {
    AcFileInfo acFileInfo = new AcFileInfo();
    acFileInfo.setPath(fidInfo.getPath());
    // 提取文件信息与访问者信息组合成所需的用户信息
    acFileInfo.setUserInfo(getUserInfoByAcidAndFid(acidInfo, fidInfo));
    acFileInfo.setFilename(nFileAcRequest.getFn());
    acFileInfo.setExtension(nFileAcRequest.getExt());
    acFileInfo.setWH(nFileAcRequest.getSize());
    acFileInfo.setBiz(nFileAcRequest.getBiz());
    return acFileInfo;
  }

  private AcFileInfo getAcFileInfoByCFileAcRequest(CFileAcRequest cFileAcRequest) {
    AcFileInfo acFileInfo = new AcFileInfo();
    acFileInfo.setPath(cFileAcRequest.getCid());
    UserInfo userInfo = new UserInfo();
    String[] acidChainInfo = cFileAcRequest.getAcid().split("\\.");
    userInfo.setFileOwnerTenantId(Integer.valueOf(acidChainInfo[0]));
    userInfo.setFileOwnerEnterpriseAccount(asmService.getEa(userInfo.getFileOwnerTenantId()));
    userInfo.setFileAcTenantId(Integer.valueOf(acidChainInfo[1]));
    userInfo.setFileAcEnterpriseAccount(asmService.getEa(userInfo.getFileAcTenantId()));
    userInfo.setFileAcEmployeeId(Integer.valueOf(acidChainInfo[2]));
    acFileInfo.setUserInfo(userInfo);
    acFileInfo.setFilename(cFileAcRequest.getFn());
    acFileInfo.setExtension(cFileAcRequest.getExt());
    acFileInfo.setWH(cFileAcRequest.getSize());
    acFileInfo.setBiz(cFileAcRequest.getBiz());
    return acFileInfo;
  }

  /**
   * 上传文件
   *
   * @param singleFileInfo 上传入参
   * @param stream         文件流
   * @return 文件路径
   */
  public String uploadFile(SignFileUpRequest singleFileInfo, InputStream stream) {
    // 与过期时间戳比较
    CodingUtil.assertSignNotExpire(singleFileInfo.getExpiry(), singleFileInfo);
    // 解码获取原始文件名
    String fileName = URLDecoder.decode(singleFileInfo.getFileName(), StandardCharsets.UTF_8);
    singleFileInfo.setFileName(fileName);
    // 验证消息完整性
    CodingUtil.assertMessageDigest(singleFileInfo.getMsgRaw(), singleFileInfo.getDigest());
    // 验证签名
    String acSk = stoneAuthService.getSkByAccessKey(singleFileInfo.getAk());
    CodingUtil.assertAcSignConsist(acSk, singleFileInfo.getSignRaw(), singleFileInfo.getSign());
    // 验证Acid格式获取企业账号和员工ID
    List<String> userInfoList = Splitter.on('.').splitToList(singleFileInfo.getAcid());
    CodingUtil.assertAcidFormat(userInfoList);
    String ea = asmService.getEa(Integer.parseInt(userInfoList.get(0)));
    Integer employeeId = Integer.parseInt(userInfoList.get(1));
    // 构建上传文件信息
    UpFileInfo upFileInfo = UpFileInfo.of(ea, employeeId, singleFileInfo.getResource(),
        singleFileInfo.getFileName(),
        singleFileInfo.getExtension(), singleFileInfo.getSize());
    return stoneStorageService.uploadSingleFile(upFileInfo, stream);
  }

  /**
   * 初始化分片上传
   *
   * @param s3UploadRequest 分片上传请求参数
   * @param authInfo        认证信息
   * @return 分片上传响应
   */
  public S3UploadResponse initChunkUpload(S3UploadRequest s3UploadRequest, AuthInfo authInfo) {
    String ea = authInfo.getEnterpriseAccount();

    FilePathRecord filePathRecord = bigFileHelper.getFilePathRecord(s3UploadRequest.getFilePath());
    if (filePathRecord == null) {
      throw new FileServerException(MODULE,"文件不存在");
    }

    // 初始化分片上传
    String uploadId = s3FileService.initMutiPartUpload(ea, filePathRecord.getObjectKey());
    return S3UploadResponse.builder()
        .objectKey(filePathRecord.getObjectKey())
        .uploadId(uploadId)
        .build();
  }

  /**
   * 上传单个分片
   *
   * @param key         object key
   * @param uploadId    上传ID
   * @param chunkIndex  分片索引
   * @param chunkSize   分片大小
   * @param inputStream 文件输入流
   * @param authInfo    认证信息
   * @return 分片信息
   */
  public PartInfo uploadChunk(String key, String uploadId, int chunkIndex, long chunkSize,
                              InputStream inputStream, AuthInfo authInfo) {
    String ea = authInfo.getEnterpriseAccount();
    try {
      // 同步执行上传
      String eTag = s3FileService.uploadPart(ea, key, uploadId, chunkIndex, chunkSize, inputStream);

      // 验证上传结果
      if (eTag == null || eTag.isEmpty()) {
        log.error("分片 {} 上传失败，ETag为空", chunkIndex);
        throw new FileServerException(MODULE, "分片上传失败，ETag无效");
      }

      log.info("分片 {} 上传成功，ETag: {}", chunkIndex, eTag);
      return PartInfo.builder().partNum(chunkIndex).etag(eTag).build();
    } catch (Exception e) {
      log.error("分片 {} 上传异常", chunkIndex, e);
      throw new FileServerException(MODULE,"分片上传失败", e);
    }
  }

  /**
   * 完成分片上传
   *
   * @param chunkUploadCompleteRequest 完成分片上传请求
   * @param uploadId                   上传ID
   * @param authInfo                   认证信息
   * @return 是否成功
   */
  public CompleteMultipartUploadResponse completeChunkUpload(ChunkUploadCompleteRequest chunkUploadCompleteRequest,
                                                             String uploadId,
                                                             AuthInfo authInfo) {
    String ea = authInfo.getEnterpriseAccount();

    FilePathRecord metaData = bigFileHelper.getFilePathRecord(chunkUploadCompleteRequest.getPath());
    if (metaData == null) {
      throw new FileServerException(MODULE,"file not exist");
    }

    return s3FileService.finishMultipartUpload(ea, metaData.getObjectKey(), uploadId,
        chunkUploadCompleteRequest.getPartInfos().stream()
            .map(partInfo -> CompletedPart.builder()
                .partNumber(partInfo.getPartNum())
                .eTag(partInfo.getEtag())
                .build())
            .collect(Collectors.toList()));
  }

  /**
   * 取消分片上传
   *
   * @param filePath 文件路径
   * @param uploadId 上传ID
   * @param authInfo 认证信息
   * @return 是否成功
   */
  public Boolean abortMultipartUpload(String filePath, String uploadId, AuthInfo authInfo) {
    String ea = authInfo.getEnterpriseAccount();

    FilePathRecord metaData = bigFileHelper.getFilePathRecord(filePath);
    if (metaData == null) {
      throw new FileServerException(MODULE, "文件不存在");
    }
    log.info("取消分片上传: ea={}, objectKey={}, uploadId={}", ea, metaData.getObjectKey(), uploadId);
    return s3FileService.abortMultipartUpload(ea, metaData.getObjectKey(), uploadId);
  }

  /**
   * 生成大文件下载URL
   *
   * @param filePath   文件路径
   * @param fileName   文件名
   * @param expireTime 过期时间（秒）
   * @param authInfo   认证信息
   * @return 下载URL
   */
  public String generateBigFileDownloadUrl(String filePath, String fileName, int expireTime, AuthInfo authInfo) {
    FileMeta metaData = bigFileHelper.getMetaData(filePath);
    if (metaData == null) {
      throw new FileServerException(MODULE,"文件不存在");
    }
    if (StringUtils.isNotBlank(fileName)) {
      if (StringUtils.isBlank(FilenameUtils.getExtension(fileName))) {
        fileName = fileName + "." + metaData.getFileExt();
      }
    } else {
      fileName = filePath + "." + metaData.getFileExt();
    }
    String ea = authInfo.getEnterpriseAccount();
    String acid = ea + "." + authInfo.getEmployeeId();
    // 这里拼过期时间的时间戳
    boolean isPublic = grayHelper.cloudAllowPublicInternet(ea);
    return isPublic ? s3FileService.generateDownloadUrl(ea, metaData.getObjectKey(), fileName, expireTime) : generateSignedDownloadUrl(filePath, acid, expireTime, fileName);
  }

  /**
   * 生成带签名的S3大文件下载URL
   *
   * @param filePath      文件路径
   * @param acid          访问者信息, eg: enterpriseId.employeeId
   * @param expireSeconds 过期时间(秒)
   * @return 带签名的下载URL
   */
  public String generateSignedDownloadUrl(String filePath, String acid, long expireSeconds, String fileName) {
    String expireTime = String.valueOf(System.currentTimeMillis() + TimeUnit.SECONDS.toMillis(expireSeconds));
    List<String> acList = Splitter.on(".").splitToList(acid);
    ASKOmit credential = stoneAuthService.getCredential(acList.getFirst());
    String accessKey = SecureCryptHelp.encry(credential.getAccessKey());
    String signdPath = SecureCryptHelp.encry(filePath);
    String raw = SignatureUtil.generatorRaw(expireTime, signdPath);
    String signature = SignatureUtil.getSignatureWithHmacSha1(credential.getSecretKey(), raw);
    return String.format(config.getDownloadBigBigFileUrl(), accessKey, acid, signdPath, expireTime,
        signature, fileName);
  }

  /**
   * 根据签名流式下载大文件
   *
   * @param request 带签名的下载请求
   * @return 文件输入流
   */
  public InputStream downloadBigFileByStream(SignedDownloadRequest request) {
    CodingUtil.assertSignNotExpire(Long.parseLong(request.getEts()));
    String raw = SignatureUtil.generatorRaw(request.getEts(), request.getPath());
    String ak = SecureCryptHelp.decry(request.getAk());
    String sk = stoneAuthService.getSkByAccessKey(ak);
    CodingUtil.assertAcSignConsist(sk, raw, request.getSign());

    String filePath = SecureCryptHelp.decry(request.getPath());

    List<String> acidParts = Splitter.on(".").splitToList(request.getAcid());
    AuthInfo authInfo = new AuthInfo();
    authInfo.setEnterpriseAccount(acidParts.get(0));
    authInfo.setEmployeeId(Long.parseLong(acidParts.get(1)));

    InputStream rawInputStream = getRawBigFileInputStream(filePath, authInfo);

    return fileDownloadLimiterUtil.wrapWithSpeedLimit(rawInputStream, authInfo.getEnterpriseAccount(),
        String.valueOf(authInfo.getEmployeeId()), filePath);
  }

  public long getObjectContentLength(SignedDownloadRequest request) {
    String filePath = SecureCryptHelp.decry(request.getPath());
    FileMeta metaData = bigFileHelper.getMetaData(filePath);
    if (metaData == null) {
      throw new FileServerException(MODULE, "文件不存在");
    }
    List<String> acidParts = Splitter.on(".").splitToList(request.getAcid());
    return s3FileService.headObject(acidParts.getFirst(), metaData.getObjectKey()).contentLength();
  }

  /**
   * 流式下载大文件
   *
   * @param filePath 文件路径
   * @param authInfo 认证信息
   * @return 文件输入流
   */
  private InputStream getRawBigFileInputStream(String filePath, AuthInfo authInfo) {
    String ea = authInfo.getEnterpriseAccount();

    FileMeta metaData = bigFileHelper.getMetaData(filePath);
    if (metaData == null) {
      throw new FileServerException(MODULE, "文件不存在");
    }

    return s3FileService.getObject(ea, metaData.getObjectKey());
  }

  public ResponseEntity<String> callback(String callbackUrl, String filePath, String etag, String objectKey,
                                         Long fileSize, String fileExt) {
    callbackUrl = Base64.decodeStr(callbackUrl);
    Map<String, String> requestData = new HashMap<>();
    requestData.put("filePath", filePath);
    requestData.put("etag", etag);
    requestData.put("objectKey", objectKey);
    requestData.put("fileSize", String.valueOf(fileSize));
    requestData.put("mimeType", fileExt);

    Gson gson = new Gson();
    RequestBody requestBody = RequestBody.create(gson.toJson(requestData), MediaType.parse("application/json"));
    Request request = new Request.Builder().url(callbackUrl).post(requestBody).build();

    Object result = httpSupport.syncExecute(request, new SyncCallback() {
      @Override
      public Object response(Response response) {
        try {
          if (response.isSuccessful() && response.body() != null) {
            return ResponseEntity.ok(response.body().string());
          } else {
            return ResponseEntity.status(response.code()).body("Request failed");
          }
        } catch (Exception e) {
          log.error("处理回调响应时发生错误", e);
          return ResponseEntity.internalServerError().body("Internal server error");
        }
      }
    });

    return (ResponseEntity<String>) result;
  }

}