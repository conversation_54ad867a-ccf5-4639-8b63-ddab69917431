package com.fxiaoke.file.server.service.impl;

import com.fxiaoke.file.server.dao.mongo.NFileMetaDao;
import com.fxiaoke.file.server.domain.constants.ErInfo;
import com.fxiaoke.file.server.domain.constants.PathType;
import com.fxiaoke.file.server.domain.entity.NFileMeta;
import com.fxiaoke.file.server.domain.exception.FileServerException;
import com.fxiaoke.file.server.domain.model.S3ObjectMetadata;
import com.fxiaoke.file.server.domain.model.api.response.FileBaseMetadataResponse;
import com.fxiaoke.file.server.manager.DataBaseManager;
import com.fxiaoke.file.server.service.FileMetaService;
import com.fxiaoke.file.server.service.S3Service;
import com.fxiaoke.file.server.utils.PathStrUtil;
import com.fxiaoke.file.server.utils.StrUtils;
import com.github.mongo.support.DatastoreExt;
import java.util.Date;
import java.util.Optional;
import org.springframework.stereotype.Service;

@Service
public class FileMetaServiceImpl implements FileMetaService {

  private static final String MODULE = "FileMetaServiceImpl";

  private final S3Service s3Service;
  private final NFileMetaDao fileMetaDao;
  private final DataBaseManager dataBaseManager;

  public FileMetaServiceImpl(S3Service s3Service, NFileMetaDao fileMetaDao,
      DataBaseManager dataBaseManager) {
    this.s3Service = s3Service;
    this.fileMetaDao = fileMetaDao;
    this.dataBaseManager = dataBaseManager;
  }

  private Optional<NFileMeta> findNFileMeta(String ea, String path) {
    PathType pathType = PathStrUtil.getPathType(path);
    if (!PathStrUtil.isEnterpriseFile(pathType)) {
      throw new FileServerException(MODULE, ErInfo.FILE_META_SERVICE_PATH_TYPE_NOT_SUPPORT, ea,
          pathType);
    }
    DatastoreExt readDataStore = dataBaseManager.getReadDataStore(ea, pathType);
    // 将含逻辑的path转换为干净的数据存储path
    String basePath = PathStrUtil.getBasePath(pathType, path);
    // 查询文件元数据
    return fileMetaDao.find(readDataStore, pathType, ea, basePath);
  }

  @Override
  public NFileMeta find(String ea, String path) {
    Optional<NFileMeta> nFileMetaOpt = findNFileMeta(ea, path);
    if (nFileMetaOpt.isEmpty()) {
      throw new FileServerException(MODULE, ErInfo.FILE_META_FILE_NOT_FOUND, ea, path);
    }
    return nFileMetaOpt.get();
  }

  @Override
  public FileBaseMetadataResponse getFileBaseMetadata(String ea, String path) {
    Optional<NFileMeta> nFileMetaOpt = findNFileMeta(ea, path);
    if (nFileMetaOpt.isEmpty()) {
      throw new FileServerException(MODULE, ErInfo.FILE_META_FILE_NOT_FOUND, ea, path);
    }

    NFileMeta nFileMeta = nFileMetaOpt.get();
    String bucket = nFileMeta.getBucket();
    String objectKey = nFileMeta.getObjectKey();
    if (StrUtils.isBlank(bucket) || StrUtils.isBlank(objectKey)) {
      throw new FileServerException(MODULE, ErInfo.FILE_NOT_SUPPORT_GET_METADATA, ea, path);
    }

    S3ObjectMetadata objectMetaInfo = s3Service.getS3ObjectMetaInfo(ea, bucket, objectKey);
    Date createTime = nFileMeta.getCreateDate();
    if (createTime == null) {
      createTime = objectMetaInfo.getLastModified();
    }

    return new FileBaseMetadataResponse(
        ea,
        nFileMeta.getName(),
        nFileMeta.getExtension(),
        createTime,
        path,
        objectMetaInfo.getETag(),
        objectMetaInfo.getContentLength(),
        objectMetaInfo.getContentType()
    );

  }
}
