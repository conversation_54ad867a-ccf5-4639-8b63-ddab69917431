package com.fxiaoke.file.server.config;

import com.facishare.warehouse.fastdfs.client.WarehouseClientConfigCenterSupport;
import com.fxiaoke.file.server.config.condition.FacishareOrPrivateClusterCondition;
import com.fxiaoke.file.server.dao.mongo.AvatarFileMetaDao;
import com.fxiaoke.file.server.dao.mongo.impl.AvatarFileMetaDaoImpl;
import com.fxiaoke.file.server.service.IAvatarService;
import com.fxiaoke.file.server.service.impl.AvatarService;
import com.github.mongo.support.DatastoreExt;
import com.github.mongo.support.MongoDataStoreFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ClusterBindBeanConfig {

  // avatar mongo 只在fstest环境与foneshare环境下使用
  @Bean
  @Conditional(FacishareOrPrivateClusterCondition.class)
  public MongoDataStoreFactoryBean avatarDataStore(CmsPropertiesConfig config) {
    MongoDataStoreFactoryBean mongoDataStoreFactoryBean = new MongoDataStoreFactoryBean();
    mongoDataStoreFactoryBean.setConfigName(config.getAvatarMongoConfigName());
    return mongoDataStoreFactoryBean;
  }

  @Bean
  @Conditional(FacishareOrPrivateClusterCondition.class)
  public IAvatarService avatarService(CmsPropertiesConfig config, DatastoreExt avatarDataStore) {
    AvatarFileMetaDao avatarFileMetaDao = new AvatarFileMetaDaoImpl(avatarDataStore);
    WarehouseClientConfigCenterSupport warehouseClientConfigCenterSupport = new WarehouseClientConfigCenterSupport();
    warehouseClientConfigCenterSupport.setConfigName(config.getAvatarFastDFSConfigName());
    warehouseClientConfigCenterSupport.init();
    return new AvatarService(config.getAvatarFileMetaCacheSize(),avatarFileMetaDao,warehouseClientConfigCenterSupport);
  }

}
