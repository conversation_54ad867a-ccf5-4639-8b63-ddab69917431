package com.fxiaoke.file.server.config.condition;

import com.fxiaoke.file.server.utils.ClusterEnvUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

public class FacishareOrPrivateClusterCondition implements Condition {
  @Override
  public boolean matches(@NotNull ConditionContext context, @NotNull AnnotatedTypeMetadata metadata) {
    return ClusterEnvUtils.isFacishareOrPrivateCluster();
  }
}
