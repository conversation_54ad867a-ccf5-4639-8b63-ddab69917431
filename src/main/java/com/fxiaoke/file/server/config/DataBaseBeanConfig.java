package com.fxiaoke.file.server.config;

import com.github.mongo.support.MongoDataStoreFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class DataBaseBeanConfig {

  @Bean
  public MongoDataStoreFactoryBean warehouseDataStore(CmsPropertiesConfig config) {
    MongoDataStoreFactoryBean mongoDataStoreFactoryBean = new MongoDataStoreFactoryBean();
    mongoDataStoreFactoryBean.setConfigName(config.getWarehouseMongoConfigName());
    return mongoDataStoreFactoryBean;
  }


  @Bean
  public MongoDataStoreFactoryBean nDataStore(CmsPropertiesConfig config) {
    MongoDataStoreFactoryBean mongoDataStoreFactoryBean = new MongoDataStoreFactoryBean();
    mongoDataStoreFactoryBean.setConfigName(config.getNMongoConfigName());
    return mongoDataStoreFactoryBean;
  }

  @Bean
  public MongoDataStoreFactoryBean n1DataStore(CmsPropertiesConfig config) {
    MongoDataStoreFactoryBean mongoDataStoreFactoryBean = new MongoDataStoreFactoryBean();
    mongoDataStoreFactoryBean.setConfigName(config.getN1MongoConfigName());
    return mongoDataStoreFactoryBean;
  }

  @Bean
  public MongoDataStoreFactoryBean n2DataStore(CmsPropertiesConfig config) {
    MongoDataStoreFactoryBean mongoDataStoreFactoryBean = new MongoDataStoreFactoryBean();
    mongoDataStoreFactoryBean.setConfigName(config.getN2MongoConfigName());
    return mongoDataStoreFactoryBean;
  }

  @Bean
  public MongoDataStoreFactoryBean shardDataStore(CmsPropertiesConfig config) {
    MongoDataStoreFactoryBean mongoDataStoreFactoryBean = new MongoDataStoreFactoryBean();
    mongoDataStoreFactoryBean.setConfigName(config.getShardMongoConfigName());
    return mongoDataStoreFactoryBean;
  }

}
