package com.fxiaoke.file.server.config;

import com.alibaba.dubbo.config.ApplicationConfig;
import com.alibaba.dubbo.config.RegistryConfig;
import com.alibaba.dubbo.config.spring.ReferenceBean;
import com.facishare.asm.api.service.ActiveSessionAuthorizeService;
import com.fxiaoke.common.http.spring.HttpSupportFactoryBean;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.enterpriserelation2.service.AuthService;
import com.fxiaoke.retrofitspring.RetrofitSpringFactoryBean;
import com.fxiaoke.retrofitspring.fxiaoke.ConfigRetrofitSpringFactory;
import java.util.HashMap;
import java.util.Map;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ImportResource;
import org.springframework.core.env.Environment;

@Configuration
@ImportResource(locations = {
    "classpath*:spring/ei-ea-converter.xml",
    "classpath:fs-uc-cache.xml",
    "classpath*:enterpriserelation2/enterpriserelation.xml",
})
public class RemoteServerBeanConfig {

  @Bean
  public ApplicationConfig applicationConfig(Environment env) {
    ApplicationConfig applicationConfig = new ApplicationConfig();
    applicationConfig.setName(env.getProperty("spring.application.name"));
    return applicationConfig;
  }

  @Bean
  public RegistryConfig registryConfig(CmsPropertiesConfig config) {
    RegistryConfig registryConfig = new RegistryConfig();
    registryConfig.setAddress(config.getZookeeper());
    return registryConfig;
  }

  @Bean
  public ReferenceBean<ActiveSessionAuthorizeService> activeSessionAuthorizeService() {
    ReferenceBean<ActiveSessionAuthorizeService> referenceBean = new ReferenceBean<>();
    referenceBean.setInterface(ActiveSessionAuthorizeService.class);
    referenceBean.setProtocol("dubbo");
    referenceBean.setRetries(0);
    referenceBean.setLazy(true);
    referenceBean.setTimeout(5000);
    referenceBean.setCheck(false);
    return referenceBean;
  }

  @Bean(initMethod = "init")
  public HttpSupportFactoryBean erOkHttpSupport() {
    HttpSupportFactoryBean factoryBean = new HttpSupportFactoryBean();
    factoryBean.setConfigName("fs-rest-api-http-support");
    return factoryBean;
  }

  @Bean(initMethod = "init")
  public ConfigRetrofitSpringFactory erRetrofitFactory(@Qualifier("erOkHttpSupport") OkHttpSupport erOkHttpSupport){
    ConfigRetrofitSpringFactory factory = new ConfigRetrofitSpringFactory();
    Map<String, String> headers = new HashMap<>();
    headers.put("x-eip-appid", "defaultApp");
    factory.setHeaders(headers);
    factory.setConfigNames("fs-enterpriserelation-rest-api");
    factory.setOkHttpSupport(erOkHttpSupport);
    return factory;
  }

  @Bean
  public RetrofitSpringFactoryBean<AuthService> authServiceFactoryBean(ConfigRetrofitSpringFactory erRetrofitFactory) {
    RetrofitSpringFactoryBean<AuthService> factoryBean = new RetrofitSpringFactoryBean<>();
    factoryBean.setFactory(erRetrofitFactory);
    return factoryBean;
  }
}
