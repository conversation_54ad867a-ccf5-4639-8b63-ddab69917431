package com.fxiaoke.file.server.config.condition;

import com.fxiaoke.file.server.config.CmsPropertiesConfig;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

/**
 * AcRecordFilter的条件验证
 * 只有在完整配置有效时才创建Filter Bean
 */
public class AcRecordFilterCondition implements Condition {

  @Override
  public boolean matches(@NotNull ConditionContext context, @NotNull AnnotatedTypeMetadata metadata) {
    try {
      // 使用Binder直接创建并绑定CmsPropertiesConfig实例
      Binder binder = Binder.get(context.getEnvironment());
      CmsPropertiesConfig config = binder.bind("cms.file.server", CmsPropertiesConfig.class)
          .orElse(new CmsPropertiesConfig());
      
      return isValidFilterConfiguration(config);
    } catch (Exception e) {
      return false;
    }
  }

  /**
   * 检查Filter配置是否有效
   * 复用与WebConfig中相同的验证逻辑
   */
  private boolean isValidFilterConfiguration(CmsPropertiesConfig config) {
    // 检查是否启用了日志记录
    if (!config.isEnabledFilterLogRecord()) {
      return false;
    }
    
    // 检查是否配置了有效的路径
    if (config.getFilterRecordPaths() == null || config.getFilterRecordPaths().isEmpty()) {
      return false;
    }
    
    // 检查是否配置了IP记录
    if (config.isFilterRecordIp()) {
      return true;
    }
    
    // 检查是否配置了Cookie记录
    if (config.getFilterRecordCookies() != null && !config.getFilterRecordCookies().isEmpty()) {
      return true;
    }
    
    // 检查是否配置了Header记录
    return config.getFilterRecordHeaders() != null && !config.getFilterRecordHeaders().isEmpty();
  }
} 