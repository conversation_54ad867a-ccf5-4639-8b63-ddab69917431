package com.fxiaoke.file.server.config;

import com.facishare.dubbo.plugin.client.DubboRestFactoryBean;
import com.facishare.fsi.proxy.FsiServiceProxyFactory;
import com.facishare.fsi.proxy.FsiServiceProxyFactoryBean;
import com.facishare.fsi.proxy.FsiWarehouseProxyFactory;
import com.facishare.fsi.proxy.service.AFileStorageService;
import com.facishare.fsi.proxy.service.GFileStorageService;
import com.facishare.restful.client.FRestApiProxyFactoryBean;
import com.facishare.stone.sdk.StoneProxyApi;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.common.http.spring.HttpSupportFactoryBean;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.fxiaoke.stone.commons.StoneAuthClient;
import com.fxiaoke.stone.commons.impl.StoneAuthClientImpl;
import com.github.jedis.support.JedisFactoryBean;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class BeanConfig {

  private static final int CACHE_SIZE = 10000;

  @Bean
  public HttpSupportFactoryBean atsOkHttpClient(CmsPropertiesConfig config) {
    HttpSupportFactoryBean factoryBean = new HttpSupportFactoryBean();
    factoryBean.setConfigName(config.getAtsHttpClientConfigName());
    factoryBean.init();
    return factoryBean;
  }

  @Bean
  public OkHttpSupport httpSupport() {
    HttpSupportFactoryBean factoryBean = new HttpSupportFactoryBean();
    factoryBean.init();
    return factoryBean.getObject();
  }

  @Bean
  public StoneAuthClient stoneAuthClient(@Qualifier("httpSupport") OkHttpSupport httpSupport) {
    return new StoneAuthClientImpl(httpSupport,CACHE_SIZE);
  }

  @Bean
  public HttpSupportFactoryBean crossCloudOkHttpClient(CmsPropertiesConfig config) {
    HttpSupportFactoryBean factoryBean = new HttpSupportFactoryBean();
    factoryBean.setConfigName(config.getCrossCloudHttpClientConfigName());
    factoryBean.init();
    return factoryBean;
  }

  @Bean
  public FRestApiProxyFactoryBean<StoneProxyApi> stoneProxyApi() {
    FRestApiProxyFactoryBean<StoneProxyApi> factoryBean = new FRestApiProxyFactoryBean<>();
    factoryBean.setType(StoneProxyApi.class);
    return factoryBean;
  }


  @Bean(initMethod = "init")
  public FsiServiceProxyFactory fsiServiceProxyFactory(CmsPropertiesConfig config) {
    FsiServiceProxyFactory factory = new FsiServiceProxyFactory();
    factory.setConfigKey(config.getFsiProxyConfigName());
    return factory;
  }

  @Bean(initMethod = "init")
  public FsiWarehouseProxyFactory fsiWarehouseProxyFactory(CmsPropertiesConfig config, DubboRestFactoryBean<EnterpriseEditionService> enterpriseEditionService) {
    FsiWarehouseProxyFactory factory = new FsiWarehouseProxyFactory();
    factory.setConfigKey(config.getFsiProxyConfigName());
    factory.setEnterpriseEditionService(enterpriseEditionService.getObject());
    return factory;
  }

  @Bean
  public FsiServiceProxyFactoryBean<GFileStorageService> gFileStorageService(@Qualifier("fsiServiceProxyFactory") FsiServiceProxyFactory fsiServiceProxyFactory) {
    FsiServiceProxyFactoryBean<GFileStorageService> factoryBean = new FsiServiceProxyFactoryBean<>();
    factoryBean.setType(GFileStorageService.class);
    factoryBean.setFactory(fsiServiceProxyFactory);
    return factoryBean;
  }

  @Bean
  public FsiServiceProxyFactoryBean<AFileStorageService> aFileStorageService(@Qualifier("fsiServiceProxyFactory") FsiServiceProxyFactory fsiServiceProxyFactory) {
    FsiServiceProxyFactoryBean<AFileStorageService> factoryBean = new FsiServiceProxyFactoryBean<>();
    factoryBean.setType(AFileStorageService.class);
    factoryBean.setFactory(fsiServiceProxyFactory);
    return factoryBean;
  }

  @Bean
  public JedisFactoryBean stoneJedisCmd(CmsPropertiesConfig config) {
    JedisFactoryBean jedisFactoryBean = new JedisFactoryBean();
    jedisFactoryBean.setConfigName(config.getStoneRedisConfigName());
    return jedisFactoryBean;
  }
}
