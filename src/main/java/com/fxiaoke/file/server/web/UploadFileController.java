package com.fxiaoke.file.server.web;

import com.fxiaoke.file.server.domain.model.api.CookieInfo;
import com.fxiaoke.file.server.domain.model.api.request.SignFileUpRequest;
import com.fxiaoke.file.server.domain.vo.request.AbortUploadRequest;
import com.fxiaoke.file.server.domain.vo.request.ChunkUploadCompleteRequest;
import com.fxiaoke.file.server.domain.vo.request.ChunkUploadInfo;
import com.fxiaoke.file.server.domain.vo.request.PartInfo;
import com.fxiaoke.file.server.domain.vo.request.S3UploadRequest;
import com.fxiaoke.file.server.domain.vo.response.AbortUploadResponse;
import com.fxiaoke.file.server.domain.vo.response.ChunkUploadCompleteResponse;
import com.fxiaoke.file.server.domain.vo.response.S3UploadResponse;
import com.fxiaoke.file.server.service.FileService;
import com.fxiaoke.stone.commons.domain.R;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@RestController
@RequestMapping({ "/FilesOne", "/FilesOne/" })
public class UploadFileController extends FileController {

  private static final String MODULE = "UploadFileController";

  public UploadFileController(FileService fileService) {
    super(fileService);
  }

  @PostMapping(consumes = "multipart/form-data")
  public R<String> uploadSingleFileWithForm(SignFileUpRequest signFileUpRequest,
      @RequestPart(name = "facishareFile") MultipartFile file) {
    return toR(signFileUpRequest, file);
  }

  @PostMapping(consumes = "application/octet-stream")
  public R<String> uploadSingleFileWithStream(SignFileUpRequest signFileUpRequest,
      HttpServletRequest request) {
    return toR(signFileUpRequest, request);
  }

  @PostMapping("/chunkUploadStart")
  @ResponseBody
  public S3UploadResponse chunkUploadStart(S3UploadRequest s3UploadRequest) {
    CookieInfo cookieInfo = s3UploadRequest.getCookieInfo();
    return toR(s3UploadRequest, s3UploadRequest.getCredential(), 
              cookieInfo != null ? cookieInfo.getFsAuthXC() : "", 
              cookieInfo != null ? cookieInfo.getErInfo() : "");
  }

  @PostMapping("/chunkUploadByStream")
  @ResponseBody
  public PartInfo chunkUploadByStream(HttpServletRequest request,
      ChunkUploadInfo chunkUploadInfo) {
    CookieInfo cookieInfo = chunkUploadInfo.getCookieInfo();
    return toR(request, chunkUploadInfo, 
              cookieInfo != null ? cookieInfo.getFsAuthXC() : "", 
              cookieInfo != null ? cookieInfo.getErInfo() : "");
  }

  @PostMapping("/chunkUploadComplete")
  @ResponseBody
  public ResponseEntity<ChunkUploadCompleteResponse> chunkUploadComplete(
      ChunkUploadCompleteRequest chunkUploadCompleteRequest) {
    CookieInfo cookieInfo = chunkUploadCompleteRequest.getCookieInfo();
    return toR(chunkUploadCompleteRequest, chunkUploadCompleteRequest.getCredential(), 
              cookieInfo != null ? cookieInfo.getFsAuthXC() : "", 
              cookieInfo != null ? cookieInfo.getErInfo() : "");
  }

  @PostMapping("/abortMultipartUpload")
  @ResponseBody
  public ResponseEntity<AbortUploadResponse> abortMultipartUpload(
      AbortUploadRequest abortUploadRequest,
      @RequestHeader(name = "x-fs-uploadId") String uploadId) {
    CookieInfo cookieInfo = abortUploadRequest.getCookieInfo();
    return toR(abortUploadRequest, abortUploadRequest.getCredential(), uploadId, 
              cookieInfo != null ? cookieInfo.getFsAuthXC() : "", 
              cookieInfo != null ? cookieInfo.getErInfo() : "");
  }


}