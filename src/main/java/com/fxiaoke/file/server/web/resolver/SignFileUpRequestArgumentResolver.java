package com.fxiaoke.file.server.web.resolver;

import com.fxiaoke.file.server.domain.model.api.request.SignFileUpRequest;
import jakarta.servlet.http.HttpServletRequest;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.MissingRequestCookieException;
import org.springframework.web.bind.MissingRequestHeaderException;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

@Component
public class SignFileUpRequestArgumentResolver extends BaseArgumentResolver implements HandlerMethodArgumentResolver {

  @Override
  public boolean supportsParameter(MethodParameter parameter) {
    return SignFileUpRequest.class.equals(parameter.getParameterType());
  }

  @Override
  public Object resolveArgument(@NotNull MethodParameter parameter,
      ModelAndViewContainer mavContainer,
      @NotNull NativeWebRequest webRequest,
      WebDataBinderFactory binderFactory) throws Exception {

    HttpServletRequest httpServletRequest = webRequest.getNativeRequest(HttpServletRequest.class);
    if (httpServletRequest == null) {
      // 这种情况非常罕见，如果发生，说明服务器或框架配置有问题，是一个内部错误
      throw new IllegalStateException("Could not get HttpServletRequest from NativeWebRequest");
    }
    return from(httpServletRequest, parameter);
  }

  public SignFileUpRequest from(HttpServletRequest request, MethodParameter parameter)
      throws MissingRequestCookieException, MissingRequestHeaderException {

    SignFileUpRequest signFileUpRequest = new SignFileUpRequest();

    signFileUpRequest.setAcid(getRequiredHeader(request, parameter, "acid"));
    signFileUpRequest.setResource(getRequiredHeader(request, parameter, "resource"));
    signFileUpRequest.setAk(getRequiredHeader(request, parameter, "ak"));
    signFileUpRequest.setSign(getRequiredHeader(request, parameter, "sign"));
    signFileUpRequest.setFileName(getRequiredHeader(request, parameter, "filename"));
    signFileUpRequest.setDigest(getRequiredHeader(request, parameter, "digest"));
    signFileUpRequest.setUserAgent(getRequiredHeader(request, parameter, "User-Agent"));

    signFileUpRequest.setExpiry(parseLongHeader(getRequiredHeader(request, parameter, "expiry"), "expiry"));
    signFileUpRequest.setSize(parseIntHeader(getRequiredHeader(request, parameter, "size"), "size"));

    return  signFileUpRequest;
  }

}

