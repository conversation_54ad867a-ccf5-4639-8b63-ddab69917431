package com.fxiaoke.file.server.web;

import com.fxiaoke.file.server.domain.model.api.request.NoSignCFileAcRequest;
import com.fxiaoke.file.server.service.FileService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;

@Slf4j
@Controller
@RequestMapping({ "/ImagesOne","/ImagesOne/"})
public class CFileVisitController extends FileController{

  public CFileVisitController(FileService fileService) {
    super(fileService);
  }

  /**
   * 获取C类型文件 无文件元数据签名匿名访问
   * @param userAgent 设备信息
   * @param noSignCFileAcRequest
   *        cFileAcRequest.Cid  明文文件ID path
   *        cFileAcRequest.Acid 访问者身份链 fileTenantId.accessTenantId.userId
   *        cFileAcRequest.Ext 文件类型 jpg|jpeg|png|gif|bmp|webp
   *        cFileAcRequest.Ds   消息摘要
   *        cFileAcRequest.size 图片大小 200*200
   */
  @GetMapping(params = {"Cid", "Acid", "Ext","Bn","Ds"})
  public void visitFile(
      @RequestHeader("User-Agent") String userAgent,
      @ModelAttribute @Validated NoSignCFileAcRequest noSignCFileAcRequest,
      HttpServletResponse response) throws Exception {
    toR(noSignCFileAcRequest, userAgent,response);
  }
}
