package com.fxiaoke.file.server.web;

import com.fxiaoke.file.server.domain.entity.NFileMeta;
import com.fxiaoke.file.server.domain.model.api.response.FileBaseMetadataResponse;
import com.fxiaoke.file.server.service.FileMetaService;
import com.fxiaoke.stone.commons.domain.R;
import jakarta.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/FilesMeta")
@Slf4j(topic = "FileMetaController")
public class FileMetaController {

  private final FileMetaService fileMetaService;

  public FileMetaController(FileMetaService fileMetaService) {
    this.fileMetaService = fileMetaService;
  }

  @GetMapping("/Find")
  public R<NFileMeta> find(String ea, String path) {
    NFileMeta fileMeta = fileMetaService.find(ea, path);
    return R.ok(fileMeta);
  }

  @GetMapping("/FindFileBaseMetadata")
  public R<FileBaseMetadataResponse> getFileBaseMetadata(
      @NotBlank(message = "企业账号不能为空") String ea,
      @NotBlank(message = "文件path 不能为空")
      @Pattern(
          regexp = "^(N_|TN_|C_|TC_).{0,60}$",
          message = "Invalid path"
      )
      String path) {
    log.info("getFileBaseMetadata ea={},path={}", ea, path);
    FileBaseMetadataResponse response = fileMetaService.getFileBaseMetadata(ea, path);
    log.info("getFileBaseMetadata response={}", response);
    return R.ok(response);
  }

}
