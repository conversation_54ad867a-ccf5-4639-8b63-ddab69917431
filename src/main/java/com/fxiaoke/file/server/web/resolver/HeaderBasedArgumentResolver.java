package com.fxiaoke.file.server.web.resolver;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.file.server.domain.model.api.CookieInfo;
import com.fxiaoke.file.server.domain.model.api.request.StsCredential;
import com.fxiaoke.file.server.domain.vo.request.ChunkUploadInfo;
import com.fxiaoke.file.server.domain.vo.request.ChunkUploadCompleteRequest;
import com.fxiaoke.file.server.domain.vo.request.AbortUploadRequest;
import com.fxiaoke.file.server.domain.vo.request.S3UploadRequest;
import com.fxiaoke.file.server.domain.vo.request.BigFileDownloadRequest;
import com.fxiaoke.file.server.utils.HttpHeaderUtils;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Slf4j
@Component
public class HeaderBasedArgumentResolver implements HandlerMethodArgumentResolver {

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        Class<?> parameterType = parameter.getParameterType();
        return parameterType.equals(ChunkUploadInfo.class) || 
               parameterType.equals(ChunkUploadCompleteRequest.class) ||
               parameterType.equals(AbortUploadRequest.class) ||
               parameterType.equals(S3UploadRequest.class) ||
               parameterType.equals(BigFileDownloadRequest.class);
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer,
            NativeWebRequest webRequest, WebDataBinderFactory binderFactory) {
        
        HttpServletRequest request = webRequest.getNativeRequest(HttpServletRequest.class);
        if (request == null) {
            return createDefaultInstance(parameter.getParameterType());
        }

        Class<?> parameterType = parameter.getParameterType();
        
        try {
            // 根据参数类型分别处理
            if (parameterType.equals(ChunkUploadInfo.class)) {
                return resolveChunkUploadInfo(request);
            }
            
            if (parameterType.equals(S3UploadRequest.class)) {
                return resolveS3UploadRequest(request);
            }
            
            if (parameterType.equals(ChunkUploadCompleteRequest.class)) {
                return resolveChunkUploadCompleteRequest(request);
            }
            
            if (parameterType.equals(AbortUploadRequest.class)) {
                return resolveAbortUploadRequest(request);
            }
            
            if (parameterType.equals(BigFileDownloadRequest.class)) {
                return resolveBigFileDownloadRequest(request);
            }
            
        } catch (Exception e) {
            log.error("Failed to resolve argument for type: {}", parameterType.getName(), e);
        }
        
        // 如果解析失败，返回默认实例
        return createDefaultInstance(parameterType);
    }

    /**
     * 解析分块上传信息
     * 从HTTP Header中获取分块上传相关的参数
     */
    private ChunkUploadInfo resolveChunkUploadInfo(HttpServletRequest request) {
        ChunkUploadInfo chunkUploadInfo = new ChunkUploadInfo();
        
        // 从Header中获取分块上传信息
        String key = HttpHeaderUtils.getHeader(request, "x-fs-key");
        String uploadId = HttpHeaderUtils.getHeader(request, "x-fs-uploadId");
        String chunkIndexStr = HttpHeaderUtils.getHeader(request, "x-fs-chunkIndex");
        String chunkSizeStr = HttpHeaderUtils.getHeader(request, "x-fs-chunkSize");

        chunkUploadInfo.setKey(key);
        chunkUploadInfo.setUploadId(uploadId);
        
        // 解析分块索引
        if (chunkIndexStr != null && !chunkIndexStr.isEmpty()) {
            try {
                chunkUploadInfo.setChunkIndex(Integer.parseInt(chunkIndexStr));
            } catch (NumberFormatException e) {
                log.warn("Invalid chunkIndex: {}", chunkIndexStr);
                chunkUploadInfo.setChunkIndex(0);
            }
        }

        // 解析分块大小
        if (chunkSizeStr != null && !chunkSizeStr.isEmpty()) {
            try {
                long chunkSize = Long.parseLong(chunkSizeStr);
                // 如果header中的chunkSize与实际content length不符，优先使用实际的content length
                long contentLength = request.getContentLengthLong();
                if (contentLength > 0 && chunkSize != contentLength) {
                    log.debug("ChunkSize in header ({}) differs from contentLength ({}), using contentLength", chunkSize, contentLength);
                    chunkSize = contentLength;
                }
                chunkUploadInfo.setChunkSize(chunkSize);
            } catch (NumberFormatException e) {
                log.warn("Invalid chunkSize: {}", chunkSizeStr);
                chunkUploadInfo.setChunkSize(request.getContentLengthLong());
            }
        } else {
            // 如果没有提供chunkSize，使用contentLength
            chunkUploadInfo.setChunkSize(request.getContentLengthLong());
        }

        // 注入STS凭证信息
        chunkUploadInfo.setCredential(resolveStsCredential(request));
        
        // 注入Cookie信息
        chunkUploadInfo.setCookieInfo(resolveCookieInfo(request));

        return chunkUploadInfo;
    }

    /**
     * 解析STS凭证信息
     * 从HTTP Header中获取STS相关的凭证参数
     */
    private StsCredential resolveStsCredential(HttpServletRequest request) {
        StsCredential credential = new StsCredential();
        
        credential.setStsAk(HttpHeaderUtils.getHeader(request, "x-fs-sts-ak"));
        credential.setStsSk(HttpHeaderUtils.getHeader(request, "x-fs-sts-sk"));
        credential.setStsToken(HttpHeaderUtils.getHeader(request, "x-fs-sts-token"));
        
        return credential;
    }

    /**
     * 解析Cookie信息
     * 从HTTP请求中获取Cookie相关的参数
     */
    private CookieInfo resolveCookieInfo(HttpServletRequest request) {
        CookieInfo cookieInfo = new CookieInfo();
        
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if ("FSAuthXC".equals(cookie.getName())) {
                    cookieInfo.setFsAuthXC(cookie.getValue());
                } else if ("ERInfo".equals(cookie.getName())) {
                    cookieInfo.setErInfo(cookie.getValue());
                }
            }
        }
        
        return cookieInfo;
    }

    /**
     * 解析S3上传请求
     * 从RequestBody中解析JSON数据，同时从Header中注入STS凭证
     */
    private S3UploadRequest resolveS3UploadRequest(HttpServletRequest request) throws IOException {
        String requestBody = getRequestBody(request);
        
        S3UploadRequest s3UploadRequest;
        if (requestBody != null && !requestBody.trim().isEmpty()) {
            try {
                s3UploadRequest = JSON.parseObject(requestBody, S3UploadRequest.class);
            } catch (Exception e) {
                log.warn("Failed to parse S3UploadRequest from JSON: {}", requestBody, e);
                s3UploadRequest = new S3UploadRequest();
            }
        } else {
            s3UploadRequest = new S3UploadRequest();
        }
        
        // 注入STS凭证信息
        s3UploadRequest.setCredential(resolveStsCredential(request));
        
        // 注入Cookie信息
        s3UploadRequest.setCookieInfo(resolveCookieInfo(request));
        
        return s3UploadRequest;
    }

    /**
     * 解析分块上传完成请求
     * 从RequestBody中解析JSON数据，同时从Header中注入STS凭证
     */
    private ChunkUploadCompleteRequest resolveChunkUploadCompleteRequest(HttpServletRequest request) throws IOException {
        String requestBody = getRequestBody(request);
        
        ChunkUploadCompleteRequest completeRequest;
        if (requestBody != null && !requestBody.trim().isEmpty()) {
            try {
                completeRequest = JSON.parseObject(requestBody, ChunkUploadCompleteRequest.class);
            } catch (Exception e) {
                log.warn("Failed to parse ChunkUploadCompleteRequest from JSON: {}", requestBody, e);
                completeRequest = new ChunkUploadCompleteRequest();
            }
        } else {
            completeRequest = new ChunkUploadCompleteRequest();
        }
        
        // 注入STS凭证信息
        completeRequest.setCredential(resolveStsCredential(request));
        
        // 注入Cookie信息
        completeRequest.setCookieInfo(resolveCookieInfo(request));
        
        return completeRequest;
    }

    /**
     * 解析终止上传请求
     * 从RequestBody中解析JSON数据，同时从Header中注入STS凭证
     */
    private AbortUploadRequest resolveAbortUploadRequest(HttpServletRequest request) throws IOException {
        String requestBody = getRequestBody(request);
        
        AbortUploadRequest abortRequest;
        if (requestBody != null && !requestBody.trim().isEmpty()) {
            try {
                abortRequest = JSON.parseObject(requestBody, AbortUploadRequest.class);
            } catch (Exception e) {
                log.warn("Failed to parse AbortUploadRequest from JSON: {}", requestBody, e);
                abortRequest = new AbortUploadRequest();
            }
        } else {
            abortRequest = new AbortUploadRequest();
        }
        
        // 注入STS凭证信息
        abortRequest.setCredential(resolveStsCredential(request));
        
        // 注入Cookie信息
        abortRequest.setCookieInfo(resolveCookieInfo(request));
        
        return abortRequest;
    }

    /**
     * 解析大文件下载请求
     * 从RequestParam中解析参数信息
     */
    private BigFileDownloadRequest resolveBigFileDownloadRequest(HttpServletRequest request) {
        BigFileDownloadRequest downloadRequest = new BigFileDownloadRequest();
        
        // 从请求参数中获取值
        String ea = request.getParameter("ea");
        String employeeIdStr = request.getParameter("employeeId");
        String filePath = request.getParameter("filePath");
        String fileName = request.getParameter("fileName");
        String expireTimeStr = request.getParameter("expireTime");
        
        downloadRequest.setEa(ea);
        
        // 解析employeeId
        if (employeeIdStr != null && !employeeIdStr.isEmpty()) {
            try {
                downloadRequest.setEmployeeId(Integer.parseInt(employeeIdStr));
            } catch (NumberFormatException e) {
                log.warn("Invalid employeeId: {}", employeeIdStr);
            }
        }
        
        downloadRequest.setFilePath(filePath);
        downloadRequest.setFileName(fileName != null ? fileName : "");
        
        // 解析expireTime，默认3600秒
        if (expireTimeStr != null && !expireTimeStr.isEmpty()) {
            try {
                downloadRequest.setExpireTime(Integer.parseInt(expireTimeStr));
            } catch (NumberFormatException e) {
                log.warn("Invalid expireTime: {}, using default 3600", expireTimeStr);
                downloadRequest.setExpireTime(3600);
            }
        } else {
            downloadRequest.setExpireTime(3600);
        }
        
        return downloadRequest;
    }

    /**
     * 从HttpServletRequest中读取请求体内容
     */
    private String getRequestBody(HttpServletRequest request) throws IOException {
        try {
            byte[] bodyBytes = StreamUtils.copyToByteArray(request.getInputStream());
            return new String(bodyBytes, StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error("Failed to read request body", e);
            throw e;
        }
    }

    /**
     * 创建默认实例
     */
    private Object createDefaultInstance(Class<?> clazz) {
        try {
            return clazz.getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            log.error("Failed to create default instance for {}", clazz.getName(), e);
            throw new RuntimeException("Failed to create default instance for " + clazz.getName(), e);
        }
    }
}