package com.fxiaoke.file.server.web.resolver;

import com.fxiaoke.file.server.domain.model.api.CookieInfo;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.BadRequestException;
import java.util.Optional;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.MissingRequestHeaderException;

public class BaseArgumentResolver {

  /**
   * 从请求中查找认证相关的 Cookie。 这个逻辑现在被恰当地封装在这里，而不是暴露在外面。
   */
  protected  Optional<CookieInfo> findCookieInfo(HttpServletRequest request) {
    Cookie[] cookies = request.getCookies();
    if (cookies == null) {
      return Optional.empty();
    }
    for (Cookie cookie : cookies) {
      if ("FSAuthXC".equals(cookie.getName())) {
        return Optional.of(CookieInfo.ofFsAuthXC(cookie.getValue()));
      } else if ("ERInfo".equals(cookie.getName())) {
        return Optional.of(CookieInfo.ofErInfo(cookie.getValue()));
      }
    }
    return Optional.empty();
  }

  /**
   * 获取一个必需的请求头。如果不存在或为空，则立即抛出异常。
   */
  protected String getRequiredHeader(HttpServletRequest request, MethodParameter parameter,
      String name) throws MissingRequestHeaderException {
    String value = request.getHeader(name);
    if (value == null || value.isBlank()) {
      throw new MissingRequestHeaderException(name, parameter);
    }
    return value;
  }

  /**
   * 将字符串解析为整数。如果失败，抛出包含头名称的特定异常。
   */
  protected  int parseIntHeader(String value, String headerName) {
    try {
      return Integer.parseInt(value);
    } catch (NumberFormatException e) {
      throw new BadRequestException(
          "Invalid integer format for header '" + headerName + "': " + value);
    }
  }

  /**
   * 将字符串解析为长整数。如果失败，抛出包含头名称的特定异常。
   */
  protected  long parseLongHeader(String value, String headerName) {
    try {
      return Long.parseLong(value);
    } catch (NumberFormatException e) {
      throw new BadRequestException(
          "Invalid long format for header '" + headerName + "': " + value);
    }
  }
}
