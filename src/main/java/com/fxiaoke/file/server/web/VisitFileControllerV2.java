package com.fxiaoke.file.server.web;

import com.fxiaoke.file.server.domain.model.api.request.CookieNFileAcRequest;
import com.fxiaoke.file.server.domain.model.api.request.NoSignNFileAcRequest;
import com.fxiaoke.file.server.domain.model.api.request.SignNFileAcRequest;
import com.fxiaoke.file.server.service.FileService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CookieValue;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;

@Slf4j
@Controller
@RequestMapping("/FilesOne")
public class VisitFileControllerV2 extends FileController {

  public VisitFileControllerV2(FileService fileService) {
    super(fileService);
  }

  /**
   * 获取文件 文件元数据签名访问+企业签名
   * @param userAgent 设备信息
   * @param signAcRequest {@link SignNFileAcRequest}
   *        noSignAcRequest.Fid  文件加密信息
   *        noSignAcRequest.Acid 访问者身份链
   *        noSignAcRequest.Ets 过期时间戳
   *        noSignAcRequest.Ak 访问密钥
   *        noSignAcRequest.Fn 文件名
   *        noSignAcRequest.Sig 访问签名
   *        noSignAcRequest.Ds 消息摘要
   *        noSignAcRequest.acModel 附件/内联/预览模式
   *        noSignAcRequest.size 图片大小
   *        noSignAcRequest.traceId 调用链ID
   */
  @GetMapping(path = {"/Sign","/Sign/"}, params = {"Fid", "Acid", "Ets", "Ak", "Fn", "Sig", "Ds"})
  public void visitFileBySign(@RequestHeader("User-Agent") String userAgent,
      @ModelAttribute @Validated SignNFileAcRequest signAcRequest,
      HttpServletResponse response) throws Exception {
    toROnlySign(signAcRequest, userAgent, response);
  }

  /**
   * 获取文件 文件元数据签名访问+企业签名+AuthXC-cookie访问
   * @param userAgent 设备信息
   * @param signAcRequest {@link SignNFileAcRequest}
   *        noSignAcRequest.Fid  文件加密信息
   *        noSignAcRequest.Acid 访问者身份链
   *        noSignAcRequest.Ets 过期时间戳
   *        noSignAcRequest.Ak 访问密钥
   *        noSignAcRequest.Fn 文件名
   *        noSignAcRequest.Sig 访问签名
   *        noSignAcRequest.Ds 消息摘要
   *        noSignAcRequest.acModel 附件/内联/预览模式
   *        noSignAcRequest.size 图片大小
   *        noSignAcRequest.traceId 调用链ID
   */
  @GetMapping(path = {"/Sign/AuthXC","/Sign/AuthXC/"}, params = {"Fid", "Acid", "Ets", "Ak", "Fn", "Sig", "Ds"})
  public void visitFileBySignAndAuthX(@RequestHeader("User-Agent") String userAgent,
      @CookieValue(name = "FSAuthXC",defaultValue = "") String authXC,
      @ModelAttribute @Validated SignNFileAcRequest signAcRequest,
      HttpServletResponse response) throws Exception {
    toRCandidateAuthX(signAcRequest, userAgent, authXC,response);
  }

  /**
   * 获取文件 文件元数据签名访问+企业签名+Em6-cookie访问
   * @param userAgent 设备信息
   * @param signAcRequest {@link SignNFileAcRequest}
   *        noSignAcRequest.Fid  文件加密信息
   *        noSignAcRequest.Acid 访问者身份链
   *        noSignAcRequest.Ets 过期时间戳
   *        noSignAcRequest.Ak 访问密钥
   *        noSignAcRequest.Fn 文件名
   *        noSignAcRequest.Sig 访问签名
   *        noSignAcRequest.Ds 消息摘要
   *        noSignAcRequest.acModel 附件/内联/预览模式
   *        noSignAcRequest.size 图片大小
   *        noSignAcRequest.traceId 调用链ID
   */
  @GetMapping(path = {"/Sign/Em6","/Sign/Em6/"}, params = {"Fid", "Acid", "Ets", "Ak", "Fn", "Sig", "Ds"})
  public void visitFileBySignAndEm6(@RequestHeader("User-Agent") String userAgent,
      @CookieValue(name = "ERInfo",defaultValue = "") String erInfo,
      @ModelAttribute @Validated SignNFileAcRequest signAcRequest,
      HttpServletResponse response) throws Exception {
    toRCandidateEm6(signAcRequest, userAgent,erInfo,response);
  }


  /**
   * 获取文件 文件元数据签名访问+cookie访问
   * @param userAgent 设备信息
   * @param cookieNFileAcRequest {@link CookieNFileAcRequest}
   *        cookieNFileAcRequest.Fid  文件加密信息
   *        cookieNFileAcRequest.Acid 访问者身份链
   *        cookieNFileAcRequest.Ak 访问密钥
   *        cookieNFileAcRequest.Fn 文件名
   *        cookieNFileAcRequest.Sig 访问签名
   *        cookieNFileAcRequest.Ds 消息摘要
   *        cookieNFileAcRequest.acModel 附件/内联/预览模式
   *        cookieNFileAcRequest.size 图片大小
   *        cookieNFileAcRequest.traceId 调用链ID
   */
  @GetMapping(path = {"/Cookie","/Cookie/"}, params = {"Fid", "Acid","Ak", "Fn", "Sig", "Ds"})
  public void visitFileByCookie(@RequestHeader("User-Agent") String userAgent,
      @CookieValue(name = "FSAuthXC",defaultValue = "") String authXC,
      @CookieValue(name = "ERInfo",defaultValue = "") String erInfo,
      @ModelAttribute @Validated CookieNFileAcRequest cookieNFileAcRequest,
      HttpServletResponse response) throws Exception {
    toROnlyCookie(cookieNFileAcRequest, userAgent, authXC, erInfo, response);
  }

  /**
   * 获取文件 文件元数据签名访问+AuthX-cookie访问
   * @param userAgent 设备信息
   * @param cookieNFileAcRequest {@link CookieNFileAcRequest}
   *        cookieNFileAcRequest.Fid  文件加密信息
   *        cookieNFileAcRequest.Acid 访问者身份链
   *        cookieNFileAcRequest.Ak 访问密钥
   *        cookieNFileAcRequest.Fn 文件名
   *        cookieNFileAcRequest.Sig 访问签名
   *        cookieNFileAcRequest.Ds 消息摘要
   *        cookieNFileAcRequest.acModel 附件/内联/预览模式
   *        cookieNFileAcRequest.size 图片大小
   *        cookieNFileAcRequest.traceId 调用链ID
   */
  @GetMapping(path = {"/Cookie/AuthXC","/Cookie/AuthXC/"}, params = {"Fid", "Acid","Ak", "Fn", "Sig", "Ds"})
  public void visitFileByAuthX(@RequestHeader("User-Agent") String userAgent,
      @CookieValue(name = "FSAuthXC",defaultValue = "") String authXC,
      @ModelAttribute @Validated CookieNFileAcRequest cookieNFileAcRequest,
      HttpServletResponse response) throws Exception {
    toROnlyAuthXC(cookieNFileAcRequest, userAgent,authXC, response);
  }

  /**
   * 获取文件 文件元数据签名访问+Em6-cookie访问
   * @param userAgent 设备信息
   * @param cookieNFileAcRequest {@link CookieNFileAcRequest}
   *        cookieNFileAcRequest.Fid  文件加密信息
   *        cookieNFileAcRequest.Acid 访问者身份链
   *        cookieNFileAcRequest.Ak 访问密钥
   *        cookieNFileAcRequest.Fn 文件名
   *        cookieNFileAcRequest.Sig 访问签名
   *        cookieNFileAcRequest.Ds 消息摘要
   *        cookieNFileAcRequest.acModel 附件/内联/预览模式
   *        cookieNFileAcRequest.size 图片大小
   *        cookieNFileAcRequest.traceId 调用链ID
   */
  @GetMapping(path = {"/Cookie/Em6","/Cookie/Em6/"}, params = {"Fid", "Acid","Ak", "Fn", "Sig", "Ds"})
  public void visitFileByEm6(@RequestHeader("User-Agent") String userAgent,
      @CookieValue(name = "ERInfo",defaultValue = "") String erInfo,
      @ModelAttribute @Validated CookieNFileAcRequest cookieNFileAcRequest,
      HttpServletResponse response) throws Exception {
    toROnlyEm6(cookieNFileAcRequest, userAgent, erInfo,response);
  }

  /**
   * 获取文件 无文件元数据签名匿名访问
   * @param userAgent 设备信息
   * @param noSignAcRequest {@link NoSignNFileAcRequest}
   *        noSignAcRequest.Fid  文件加密信息
   *        noSignAcRequest.Acid 访问者身份链
   *        noSignAcRequest.Ets 过期时间戳
   *        noSignAcRequest.Ak 访问密钥
   *        noSignAcRequest.Fn 文件名
   *        noSignAcRequest.Bn 业务名
   *        noSignAcRequest.Sig 访问签名
   *        noSignAcRequest.Ds 消息摘要
   *        noSignAcRequest.acModel 附件/内联/预览模式
   *        noSignAcRequest.size 图片大小
   *        noSignAcRequest.traceId 调用链ID
   */
  @GetMapping(path = {"/Anonymity/Sign","/Anonymity/Sign/"}, params = {"Fid", "Acid", "Ets", "Ak", "Fn","Bn", "Sig", "Ds"})
  public void visitFileBySelfSign(
      @RequestHeader("User-Agent") String userAgent,
      @ModelAttribute @Validated NoSignNFileAcRequest noSignAcRequest,
      HttpServletResponse response) throws Exception {
    toROnlySign(noSignAcRequest, userAgent,response);
  }

  @GetMapping(path = {"/Anonymity/Sign/Base64", "/Anonymity/Sign/Base64/"}, params = {"Fid", "Acid", "Ets", "Ak", "Fn", "Bn", "Sig", "Ds"})
  public void visitFileBase64BySelfSign(
      @ModelAttribute @Validated NoSignNFileAcRequest noSignAcRequest,
      HttpServletResponse response) throws Exception {
    toBase64ROnlySign(noSignAcRequest, response);
  }

}
