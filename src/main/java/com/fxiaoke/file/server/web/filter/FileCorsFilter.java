package com.fxiaoke.file.server.web.filter;

import com.fxiaoke.file.server.config.CmsPropertiesConfig;
import com.google.common.base.Splitter;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * creator: liuys
 * CreateTime: 2025-08-12
 */
@Component
public class FileCorsFilter extends OncePerRequestFilter {

  @Resource
  CmsPropertiesConfig config;
  
  private Set<String> allowedOrigins;

  @PostConstruct
  private void initAllowedOrigins() {
    String allowRegions = config.getAllowRegions();
    if (StringUtils.isNotBlank(allowRegions)) {
      List<String> origins = Splitter.on(",").trimResults().splitToList(allowRegions);
      allowedOrigins = new HashSet<>(origins);
    } else {
      allowedOrigins = new HashSet<>();
    }
  }
  @Override
  protected void doFilterInternal(@NotNull HttpServletRequest request,
      @NotNull HttpServletResponse response, @NotNull FilterChain filterChain)
      throws ServletException, IOException {

    // 处理CORS头信息
    handleCorsHeaders(request, response);

    // 对于OPTIONS预检请求，直接返回200状态码
    if ("OPTIONS".equalsIgnoreCase(request.getMethod())) {
      response.setStatus(HttpServletResponse.SC_OK);
      return;
    }
    
    // 继续处理其他请求
    filterChain.doFilter(request, response);
  }
  
  /**
   * 处理CORS相关的响应头
   * 优化：使用预处理的HashSet进行快速查找，避免重复的字符串操作
   */
  private void handleCorsHeaders(HttpServletRequest request, HttpServletResponse response) {
    String originalOrigin = request.getHeader("Origin");

    // 检查并设置允许的Origin
    if (originalOrigin != null && isOriginAllowed(originalOrigin)) {
      response.setHeader("Access-Control-Allow-Origin", originalOrigin);
    }

    // 设置其他CORS响应头
    response.setHeader("Access-Control-Allow-Credentials", "true");
    response.setHeader("Access-Control-Allow-Methods", "POST, GET, PATCH, DELETE, PUT, OPTIONS");
    response.setHeader("Access-Control-Max-Age", "3600");
    
    // 处理请求头
    String requestHeaders = request.getHeader("Access-Control-Request-Headers");
    if (StringUtils.isNotBlank(requestHeaders)) {
      response.setHeader("Access-Control-Allow-Headers", requestHeaders);
    } else {
      response.setHeader("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept, Authorization");
    }
  }
  
  private boolean isOriginAllowed(String originalOrigin) {
    if (allowedOrigins == null || allowedOrigins.isEmpty()) {
      return false;
    }
    String originWithoutProtocol = removeProtocolPrefix(originalOrigin);
    return allowedOrigins.contains(originWithoutProtocol);
  }
  
  /**
   * 移除URL的协议前缀
   */
  private String removeProtocolPrefix(String origin) {
    if (origin.startsWith("https://")) {
      return origin.substring("https://".length());
    } else if (origin.startsWith("http://")) {
      return origin.substring("http://".length());
    }
    return origin;
  }
}
