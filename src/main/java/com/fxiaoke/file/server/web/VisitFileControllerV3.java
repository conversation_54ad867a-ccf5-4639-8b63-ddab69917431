package com.fxiaoke.file.server.web;

import com.fxiaoke.file.server.domain.model.api.request.SignNFileAcRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;

@Slf4j
@Controller
@RequestMapping("/FilesOne")
public class VisitFileControllerV3 {

  /**
   * 获取文件 文件元数据签名访问+企业签名
   * @param userAgent 设备信息
   * @param signAcRequest {@link SignNFileAcRequest}
   *        noSignAcRequest.Fid  文件加密信息
   *        noSignAcRequest.Acid 访问者身份链
   *        noSignAcRequest.Ets 过期时间戳
   *        noSignAcRequest.Ak 访问密钥
   *        noSignAcRequest.Fn 文件名
   *        noSignAcRequest.Sig 访问签名
   *        noSignAcRequest.Ds 消息摘要
   *        noSignAcRequest.acModel 附件/内联/预览模式
   *        noSignAcRequest.size 图片大小
   *        noSignAcRequest.traceId 调用链ID
   */
  @GetMapping(path = {"/Sign","/Sign/"}, params = {"Fid", "Acid", "Ets", "Ak", "Fn", "Sig", "Ds"})
  public void visitFileBySign(@RequestHeader("User-Agent") String userAgent,
      @ModelAttribute @Validated SignNFileAcRequest signAcRequest,
      HttpServletResponse response) throws Exception {
    toROnlySign(signAcRequest, userAgent, response);
  }

}
