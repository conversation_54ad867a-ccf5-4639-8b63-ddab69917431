package com.fxiaoke.file.server.web;

import com.fxiaoke.file.server.domain.model.api.request.PathToCdnFileReq;
import com.fxiaoke.file.server.service.CdnFileService;
import com.fxiaoke.stone.commons.domain.R;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class CdnFileController extends FilesController{

  private final CdnFileService cdnFileService;

  public CdnFileController(CdnFileService cdnFileService) {
    this.cdnFileService = cdnFileService;
  }

  @PostMapping("/pathToCdnFile")
  public R<String> pathToCdnFile(@Valid @RequestBody PathToCdnFileReq req) {
    String cdnReqPath = cdnFileService.pathToCdnFile(req);
    return R.ok(cdnReqPath);
  }
}
