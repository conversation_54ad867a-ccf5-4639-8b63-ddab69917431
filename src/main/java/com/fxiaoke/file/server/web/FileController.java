package com.fxiaoke.file.server.web;
import com.fxiaoke.file.server.domain.constants.ErInfo;
import com.fxiaoke.file.server.domain.exception.FileServerException;
import com.fxiaoke.file.server.domain.model.api.AuthInfo;
import com.fxiaoke.file.server.domain.model.api.request.*;
import com.fxiaoke.file.server.domain.vo.request.*;
import com.fxiaoke.file.server.domain.vo.response.AbortUploadResponse;
import com.fxiaoke.file.server.domain.vo.response.DownloadResponse;
import com.fxiaoke.file.server.domain.vo.response.S3UploadResponse;
import com.fxiaoke.file.server.domain.vo.response.ChunkUploadCompleteResponse;
import org.springframework.http.ResponseEntity;
import software.amazon.awssdk.services.s3.model.CompleteMultipartUploadResponse;
import com.fxiaoke.file.server.service.AsmService;
import com.fxiaoke.file.server.service.FileService;
import com.fxiaoke.file.server.utils.BrowserUtils;
import com.fxiaoke.file.server.utils.DataUtil;
import com.fxiaoke.file.server.utils.FileInfoUtil;
import com.fxiaoke.file.server.utils.ImageFormatDetector;
import com.fxiaoke.stone.commons.StoneAuthClient;
import com.fxiaoke.stone.commons.domain.R;
import com.fxiaoke.stone.commons.domain.model.ASKOmit;
import com.fxiaoke.stone.commons.domain.utils.FilenameUtil;
import com.fxiaoke.stone.commons.domain.utils.SignatureUtil;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.web.multipart.MultipartFile;

@Slf4j(topic = "FileController")
public class FileController {
  private static final String MODULE = "FileController";
  protected final FileService fileService;

  @Resource
  protected AsmService asmService;
  
  @Resource
  protected StoneAuthClient stoneAuthClient;

  public FileController(FileService fileService) {
    this.fileService = fileService;
  }

  protected void toROnlySign(NFileAcRequest nFileAcRequest, String userAgent,HttpServletResponse response) throws Exception {
    logger("Download file form sign start, arg={}",nFileAcRequest);
    try (InputStream stream = fileService.getFileBySign(nFileAcRequest)) {
      BrowserUtils.setHead(response, nFileAcRequest.getAcModel(), nFileAcRequest.getExt(), userAgent, nFileAcRequest.getFn());
      int size = IOUtils.copy(stream, response.getOutputStream());
      logger("Download file form sign end, arg={},size={}", nFileAcRequest,size);
    }
  }

  protected void toBase64ROnlySign(NFileAcRequest nFileAcRequest, HttpServletResponse response) throws Exception {
    logger("Download file from sign start, arg={}", nFileAcRequest);
    // 确保 InputStream 的正确管理
    try (InputStream stream = new BufferedInputStream(fileService.getFileBySign(nFileAcRequest))) {
      // 设置响应头以指示返回的内容类型
      BrowserUtils.setBase64Head(response);
      String prefix = getBase64Prefix(nFileAcRequest.getExt(), stream);
      try (OutputStream outputStream = response.getOutputStream()) {
        // 写入文件Base64前缀
        outputStream.write(prefix.getBytes(StandardCharsets.UTF_8));
        outputStream.flush();
        
        // 写入数据Base64 编码
        try (OutputStream wrapStream = Base64.getEncoder().wrap(outputStream)) {
          // 复制输入流到输出流，并进行 Base64 编码
          int size = IOUtils.copy(stream, wrapStream);
          logger("Download file from sign end, arg={}, size={}", nFileAcRequest, size);
        }
      }
    }
  }

  private String getBase64Prefix(String ext,InputStream stream) throws IOException{
    // 设置响应的内容类型为 Base64 编码的文件类型
    String mimeType = FileInfoUtil.getMimeTypeByExtension(ext);
    String prefix = "data:" + mimeType + ";base64,";
    if (FileInfoUtil.isSupportImageByExtension(ext)) {
      String detectImageFormat = ImageFormatDetector.detectImageMimeType(stream);
      if (detectImageFormat != null) {
        prefix = "data:" + detectImageFormat + ";base64,";
      }
    }
    return prefix;
  }

  protected void toRCandidateAuthX(NFileAcRequest nFileAcRequest, String userAgent, String authXC, HttpServletResponse response) throws Exception {
    logger("Download file form candidate authXC start, arg={}", nFileAcRequest);
    try (InputStream stream = fileService.getFileByConditionAuthXC(nFileAcRequest, authXC)) {
      BrowserUtils.setHead(response, nFileAcRequest.getAcModel(), nFileAcRequest.getExt(), userAgent, nFileAcRequest.getFn());
      int size = IOUtils.copy(stream, response.getOutputStream());
      logger("Download file form candidate authXC end, arg={},size={}", nFileAcRequest,size);
    }
   }

  protected void toRCandidateEm6(NFileAcRequest nFileAcRequest, String userAgent,String erInfo, HttpServletResponse response) throws Exception {
    logger("Download file form candidate em6 start, arg={}",nFileAcRequest);
    // 校验签名是否过期
    try (InputStream stream = fileService.getFileByConditionEm6(nFileAcRequest,erInfo)) {
      BrowserUtils.setHead(response, nFileAcRequest.getAcModel(), nFileAcRequest.getExt(), userAgent, nFileAcRequest.getFn());
      int size = IOUtils.copy(stream, response.getOutputStream());
      logger("Download file form candidate em6 end, arg={},size={}", nFileAcRequest,size);
    }
  }

  protected void toROnlyCookie(NFileAcRequest nFileAcRequest, String userAgent, String authXC,String erInfo, HttpServletResponse response) throws Exception {
    logger("Download file form cookie start, arg={}",nFileAcRequest);
    try (InputStream stream = fileService.getFileByCookie(nFileAcRequest, authXC,erInfo)) {
      BrowserUtils.setHead(response, nFileAcRequest.getAcModel(), nFileAcRequest.getExt(), userAgent, nFileAcRequest.getFn());
      int size = IOUtils.copy(stream, response.getOutputStream());
      logger("Download file form cookie end, arg={},size={}", nFileAcRequest,size);
    }
  }

  protected void toROnlyAuthXC(NFileAcRequest nFileAcRequest, String userAgent, String authXC,HttpServletResponse response) throws Exception {
    logger("Download file form authXC start, arg={}",nFileAcRequest);
    try (InputStream stream = fileService.getFileByAuthXC(nFileAcRequest, authXC)) {
      BrowserUtils.setHead(response, nFileAcRequest.getAcModel(), nFileAcRequest.getExt(), userAgent, nFileAcRequest.getFn());
      int size = IOUtils.copy(stream, response.getOutputStream());
      logger("Download file form authXC end, arg={},size={}", nFileAcRequest,size);
    }
  }

  protected void toROnlyEm6(NFileAcRequest nFileAcRequest, String userAgent, String erInfo,HttpServletResponse response) throws Exception {
    logger("Download file form erInfo start, arg={}",nFileAcRequest);
    try (InputStream stream = fileService.getFileByEm6(nFileAcRequest, erInfo)) {
      BrowserUtils.setHead(response, nFileAcRequest.getAcModel(), nFileAcRequest.getExt(), userAgent, nFileAcRequest.getFn());
      int size = IOUtils.copy(stream, response.getOutputStream());
      logger("Download file form erInfo end, arg={},size={}", nFileAcRequest,size);
    }
  }

  protected void toR(CFileAcRequest cFileAcRequest, String userAgent, HttpServletResponse response) throws Exception {
    logger("Download cFile start, arg={}", cFileAcRequest);
    try (InputStream stream = fileService.getCFile(cFileAcRequest)) {
      BrowserUtils.setHead(response, cFileAcRequest.getAcModel(), cFileAcRequest.getExt(), userAgent, cFileAcRequest.getFn());
      int size = IOUtils.copy(stream, response.getOutputStream());
      logger("Download cFile end, arg={},size={}", cFileAcRequest,size);
    }
  }

  protected void toR(AvatarRequest avatarRequest, String userAgent, HttpServletResponse response) throws Exception {
    logger("Download avatarFile start, arg={}", avatarRequest);
    String ext = FilenameUtil.getExtension(avatarRequest.getFn());
    BrowserUtils.setHead(response, avatarRequest.getAcModel(), ext, userAgent, avatarRequest.getFn());
    fileService.getAvatarFile(avatarRequest, response.getOutputStream());
    logger("Download avatarFile end, arg={}",avatarRequest);
  }

  protected R<String> toR(SignFileUpRequest request, HttpServletRequest httpRequest) {
    logger("Upload file start, arg={}", request);

    try (var input = new BufferedInputStream(httpRequest.getInputStream())) {
      return processUpload(request, input);
    } catch (IOException e) {
      throw new FileServerException(e, MODULE, ErInfo.USER_UPLOAD_EOF, request);
    }
  }

  protected R<String> toR(SignFileUpRequest request, MultipartFile file) {
    logger("Upload file start, arg={}", request);
    validateMultipartFile(request, file);

    try (var input = new BufferedInputStream(file.getInputStream())) {
      return processUpload(request, input);
    } catch (IOException e) {
      throw new FileServerException(e, MODULE, ErInfo.USER_UPLOAD_EOF, request);
    }
  }

  private void validateMultipartFile(SignFileUpRequest request, MultipartFile file) {
    if (file == null || file.isEmpty()) {
      throw new FileServerException(MODULE, ErInfo.UPLOAD_MISS_FORM_ITEM, request);
    }

    if (!FileInfoUtil.isExtensionMatchingMimeType(request.getExtension(), file.getContentType())) {
      throw new FileServerException(MODULE, ErInfo.UPLOAD_FILE_TYPE_NOT_MATCH, request);
    }

    if (file.getSize() != request.getSize()) {
      throw new FileServerException(MODULE, ErInfo.UPLOAD_FILE_SIZE_NOT_MATCH, request);
    }
  }

  private R<String> processUpload(SignFileUpRequest request, InputStream input) {
    String path = fileService.uploadFile(request, input);
    logger("Upload file end, result={}", path);
    return R.ok(path);
  }

  protected S3UploadResponse toR(S3UploadRequest s3UploadRequest, StsCredential credential, String authXC, String erInfo) {
    logger("Chunk upload start, arg={}", s3UploadRequest);
    AuthInfo info = asmService.getAuthInfo(authXC, erInfo);
    // 鉴权
    validateAuth(info, credential, s3UploadRequest.getExpireTime());
    
    S3UploadResponse result = fileService.initChunkUpload(s3UploadRequest, info);
    logger("Chunk upload start end, result={}", result);
    return result;
  }

  protected PartInfo toR(HttpServletRequest request, ChunkUploadInfo chunkUploadInfo, String authXC, String erInfo) {
    logger("Chunk upload by stream start, arg={}", chunkUploadInfo);
    AuthInfo info = asmService.getAuthInfo(authXC, erInfo);

    try {
      PartInfo result = fileService.uploadChunk(chunkUploadInfo.getKey(), chunkUploadInfo.getUploadId(),
          chunkUploadInfo.getChunkIndex(), chunkUploadInfo.getChunkSize(),
          request.getInputStream(), info);
      logger("Chunk upload by stream end, result={}", result);
      return result;
    } catch (IOException e) {
      log.error("获取请求输入流失败", e);
      throw new FileServerException(MODULE,"获取请求输入流失败", e);
    }
  }

  protected ResponseEntity<ChunkUploadCompleteResponse> toR(ChunkUploadCompleteRequest chunkUploadCompleteRequest,
                                       StsCredential credential,
                                       String authXC,
                                       String erInfo) {
    try {
      logger("Chunk upload complete, arg={}", chunkUploadCompleteRequest);
      AuthInfo info = asmService.getAuthInfo(authXC, erInfo);
      // 鉴权
      validateAuth(info, credential, chunkUploadCompleteRequest.getExpireTime());

      CompleteMultipartUploadResponse response = fileService.completeChunkUpload(
          chunkUploadCompleteRequest,
          chunkUploadCompleteRequest.getUploadId(),
          info);

      String callback = chunkUploadCompleteRequest.getCallback();
      if (callback != null && !callback.isEmpty()) {
        ResponseEntity<String> responseEntity = fileService.callback(callback, chunkUploadCompleteRequest.getPath(),
            response.eTag(), response.key(), chunkUploadCompleteRequest.getFileSize(),
            chunkUploadCompleteRequest.getFileExt());
        if (responseEntity.getStatusCode().is2xxSuccessful()) {
          ChunkUploadCompleteResponse result = ChunkUploadCompleteResponse.builder()
              .code(200)
              .message("successful")
              .build();
          return ResponseEntity.ok(result);
        }
        if (responseEntity.getStatusCode().is4xxClientError()) {
          ChunkUploadCompleteResponse result = ChunkUploadCompleteResponse.builder()
              .code(400)
              .message("Param Error")
              .build();
          return ResponseEntity.badRequest().body(result);
        }
      }
      ChunkUploadCompleteResponse result = ChunkUploadCompleteResponse.builder()
          .code(500)
          .message("callback error")
          .build();
      return ResponseEntity.internalServerError().body(result);
    } catch (Exception e) {
      log.error("chunkUploadComplete error", e);
      ChunkUploadCompleteResponse result = ChunkUploadCompleteResponse.builder()
          .code(500)
          .message("internal server error")
          .build();
      return ResponseEntity.internalServerError().body(result);
    }
  }
  protected ResponseEntity<AbortUploadResponse> toR(
      AbortUploadRequest abortUploadRequest,
      StsCredential credential,
      String uploadId,
      String authXC,
      String erInfo) {
    logger("Abort multipart upload, arg={}", abortUploadRequest);
    AuthInfo info = asmService.getAuthInfo(authXC, erInfo);
    // 鉴权
    validateAuth(info, credential, abortUploadRequest.getExpireTime());
    
    boolean successful = fileService.abortMultipartUpload(abortUploadRequest.getFilePath(), uploadId, info);
    logger("Abort multipart upload end, result={}", successful);
    
    return ResponseEntity.ok(
        AbortUploadResponse.builder()
            .successful(successful)
            .build());
  }
  
  protected ResponseEntity<DownloadResponse> toR(
      String ea,
      Integer employeeId,
      String filePath,
      String fileName,
      int expireTime) {
    logger("Get big file download url, ea={}, filePath={}", ea, filePath);
    AuthInfo info = new AuthInfo();
    info.setEnterpriseAccount(ea);
    info.setEmployeeId(employeeId);
    
    String downloadUrl = fileService.generateBigFileDownloadUrl(filePath, fileName, expireTime, info);
    logger("Get big file download url end, result={}", downloadUrl);
    
    return ResponseEntity.ok(
        DownloadResponse.builder()
            .downloadUrl(downloadUrl)
            .build());
  }
  
  protected void toR(
      SignedDownloadRequest downloadRequest,
      HttpServletRequest request,
      HttpServletResponse response) throws IOException {
    logger("Download big file by stream, path={}", downloadRequest.getPath());
    try (InputStream is = fileService.downloadBigFileByStream(downloadRequest)) {
      response.setContentType("application/octet-stream");
      long contentLen = fileService.getObjectContentLength(downloadRequest);
      response.setContentLengthLong(contentLen);
      
      String fileName = downloadRequest.getFilename();
      if (fileName != null && !fileName.isEmpty()) {
        response.setHeader("Content-Disposition", 
            com.fxiaoke.file.server.utils.HttpHeaderUtils.getFullDisposition(request, fileName));
      }
      
      is.transferTo(response.getOutputStream());
      response.getOutputStream().flush();
      logger("Download big file by stream end, path={}", downloadRequest.getPath());
    } catch (IOException e) {
      log.error("download file failed, filePath:{}", downloadRequest.getPath(), e);
      throw new FileServerException(MODULE, "下载文件失败");
    }
  }

  protected void validateAuth(AuthInfo info, StsCredential credential, String expireTime) {
    String ea = info.getEnterpriseAccount();
    long employeeId = info.getEmployeeId();
    ASKOmit omit = stoneAuthClient.getSkBySts(ea);
    String encAk = SignatureUtil.getSignatureWithHmacSha1(ea, SignatureUtil.messageDigest(omit.getAccessKey()));
    String encSk = SignatureUtil.getSignatureWithHmacSha1(ea, SignatureUtil.messageDigest(omit.getSecretKey()));
    String encToken = SignatureUtil.getSignatureWithHmacSha1(ea,
        SignatureUtil.messageDigest(SignatureUtil.generatorRaw(ea,
            employeeId, omit.getAccessKey(), omit.getSecretKey(), expireTime)));

    if (DataUtil.isExpired(expireTime)) {
      throw new FileServerException(MODULE, "Authentication failed, expireTime is expired");
    }
    if (!encAk.equals(credential.getStsAk()) && !encSk.equals(credential.getStsSk())
        && !encToken.equals(credential.getStsToken())) {
      throw new FileServerException(MODULE, "Authentication failed, illegal Ak, SK");
    }
  }

  private void logger(String template,Object arg) {
    log.info(template,arg);
  }
  
  private void logger(String template,Object arg,Object arg2) {
    log.info(template,arg,arg2);
  }
}
