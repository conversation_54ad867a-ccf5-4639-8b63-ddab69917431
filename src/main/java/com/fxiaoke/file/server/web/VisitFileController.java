package com.fxiaoke.file.server.web;

import com.fxiaoke.file.server.domain.model.api.request.AvatarRequest;
import com.fxiaoke.file.server.domain.model.api.request.NoSignNFileAcRequest;
import com.fxiaoke.file.server.domain.model.api.request.SignNFileAcRequest;
import com.fxiaoke.file.server.service.FileService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;

@Slf4j
@Controller
@RequestMapping({ "/FilesOne","/FilesOne/"})
public class VisitFileController extends FileController{

  public VisitFileController(FileService fileService) {
    super(fileService);
  }

  /**
   * 获取文件 元数据签名访问
   * @param userAgent 设备信息
   * @param signAcRequest {@link SignNFileAcRequest}
   *        noSignAcRequest.Fid  文件加密信息
   *        noSignAcRequest.Acid 访问者身份链
   *        noSignAcRequest.Ets 过期时间戳
   *        noSignAcRequest.Ak 访问密钥
   *        noSignAcRequest.Fn 文件名
   *        noSignAcRequest.Sig 访问签名
   *        noSignAcRequest.Ds 消息摘要
   *        noSignAcRequest.acModel 附件/内联/预览模式
   *        noSignAcRequest.size 图片大小
   *        noSignAcRequest.traceId 调用链ID
   */
  @GetMapping(params = {"Fid", "Acid", "Ets", "Ak", "Fn", "Sig", "Ds"})
  public void visitFile(
      @RequestHeader("User-Agent") String userAgent,
      @ModelAttribute @Validated SignNFileAcRequest signAcRequest,
      HttpServletResponse response) throws Exception {
    toROnlySign(signAcRequest, userAgent, response);
  }

  /**
   * 获取文件 无文件元数据签名匿名访问
   * @param userAgent 设备信息
   * @param noSignAcRequest {@link NoSignNFileAcRequest}
   *        noSignAcRequest.Fid  文件加密信息
   *        noSignAcRequest.Acid 访问者身份链
   *        noSignAcRequest.Ets 过期时间戳
   *        noSignAcRequest.Ak 访问密钥
   *        noSignAcRequest.Fn 文件名
   *        noSignAcRequest.Bn 业务名
   *        noSignAcRequest.Sig 访问签名
   *        noSignAcRequest.Ds 消息摘要
   *        noSignAcRequest.acModel 附件/内联/预览模式
   *        noSignAcRequest.size 图片大小
   *        noSignAcRequest.traceId 调用链ID
   */
  @GetMapping(params = {"Fid", "Acid", "Ets", "Ak", "Fn","Bn", "Sig", "Ds"})
  public void visitFile(
      @RequestHeader("User-Agent") String userAgent,
      @ModelAttribute @Validated NoSignNFileAcRequest noSignAcRequest,
      HttpServletResponse response) throws Exception {
    toROnlySign(noSignAcRequest, userAgent,response);
  }

  /**
   * 获取头像 头像没有隐私属性所以无需进行严格的权限校验
   * @param userAgent 浏览器信息
   * @param avatarRequest
   *        avatarRequest.Fid 文件加密信息
   *        avatarRequest.Acid 访问者身份链
   *        avatarRequest.Fn 文件名
   *        avatarRequest.Ds 消息摘要
   *        avatarRequest.acModel 附件/内联/预览模式
   *        avatarRequest.size 图片尺寸
   *        avatarRequest.traceId 调用链ID
   */
  @GetMapping(params = {"Fid", "Acid","Fn","Ds"})
  public void visitAvatarFile(
      @RequestHeader("User-Agent") String userAgent,
      @ModelAttribute @Validated AvatarRequest avatarRequest,
      HttpServletResponse response) throws Exception {
    toR(avatarRequest, userAgent,response);
  }

}
