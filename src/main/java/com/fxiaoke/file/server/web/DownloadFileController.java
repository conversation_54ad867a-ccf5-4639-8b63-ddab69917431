package com.fxiaoke.file.server.web;

import com.fxiaoke.file.server.domain.exception.FileServerException;
import com.fxiaoke.file.server.domain.model.api.request.SignedDownloadRequest;
import com.fxiaoke.file.server.domain.vo.request.BigFileDownloadRequest;
import com.fxiaoke.file.server.domain.vo.response.DownloadResponse;
import com.fxiaoke.file.server.service.FileService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

@RestController
@RequestMapping({ "/FilesOne", "/FilesOne/" })
@Slf4j
public class DownloadFileController extends FileController {

  private static final String MODULE = "DownloadFileController";


  public DownloadFileController(FileService fileService) {
    super(fileService);
  }

  @GetMapping("/getBigFileDownloadUrl")
  @ResponseBody
  public ResponseEntity<DownloadResponse> getBigFileDownloadUrl(BigFileDownloadRequest downloadRequest) {
    return toR(downloadRequest.getEa(), downloadRequest.getEmployeeId(), downloadRequest.getFilePath(), 
              downloadRequest.getFileName(), downloadRequest.getExpireTime());
  }

  @GetMapping("/downloadBigFileByStream")
  public void downloadBigFileByStream(
      SignedDownloadRequest downloadRequest,
      HttpServletRequest request,
      HttpServletResponse response) {
    try {
      toR(downloadRequest, request, response);
    } catch (IOException e) {
      log.error("download file failed, filePath:{}", downloadRequest.getPath(), e);
      throw new FileServerException(MODULE, "下载文件失败");
    }
  }

}
