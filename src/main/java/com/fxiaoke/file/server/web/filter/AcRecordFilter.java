package com.fxiaoke.file.server.web.filter;

import com.fxiaoke.file.server.config.CmsPropertiesConfig;
import com.fxiaoke.file.server.utils.TraceSupplementUtil;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.filter.OncePerRequestFilter;
import java.io.IOException;
import java.util.Set;

/**
 * 访问日志记录器
 */
@Slf4j(topic = "AcRecordFilter")
public class AcRecordFilter extends OncePerRequestFilter {

  private static final String ROUTE_COOKIE = "fsRoutes";
  private static final String SEPARATOR = " | ";
  private static final int ESTIMATED_LOG_SIZE = 512;

  private final CmsPropertiesConfig config;

  public AcRecordFilter(CmsPropertiesConfig config) {
    this.config = config;
  }

  @Override
  protected void doFilterInternal(
      @NotNull HttpServletRequest request,
      @NotNull HttpServletResponse response,
      @NotNull FilterChain filterChain) throws IOException, ServletException {

    final StringBuilder sb = new StringBuilder(ESTIMATED_LOG_SIZE);

    sb.append("AcRecord: ")
        .append(request.getMethod())
        .append(' ')
        .append(request.getRequestURI());

    if (config.isFilterRecordIp()) {
      sb.append(SEPARATOR)
          .append("IP: ")
          .append(getClientIp(request));
    }

    // 动态 Headers 记录处理逻辑
    final Optional<Map<String, String>> headersOpt = getExistHeaderStr(request,
        config.getFilterRecordHeaders());
    headersOpt.ifPresent(headers -> headers.forEach((name, value) ->
        sb.append(SEPARATOR)
            .append("Header[")
            .append(name)
            .append('=')
            .append(value)
            .append(']')));

    // 动态 Cookies 记录处理逻辑
    final Optional<Map<String, String>> cookiesOpt = getExistCookieStr(request,
        config.getFilterRecordCookies());
    if (cookiesOpt.isPresent()) {
      Map<String, String> cookies = cookiesOpt.get();
      cookies.forEach((name, value) ->
          sb.append(SEPARATOR)
              .append("Cookie[")
              .append(name)
              .append('=')
              .append(value)
              .append(']'));
      String fsRoute = cookies.get(ROUTE_COOKIE);
      TraceSupplementUtil.supplementTrace(fsRoute);
    }

    // 记录访问日志
    log.info(sb.toString());

    filterChain.doFilter(request, response);
  }

  /**
   * 获取客户端IP地址( 必须开启Tomcat 的 RemoteIpValve 支持 )
   *
   * @param request 请求对象
   * @return 客户端IP地址
   */
  private String getClientIp(HttpServletRequest request) {
    String ip = request.getRemoteAddr();
    return ip != null ? ip : "N/A";
  }

  private Optional<Map<String, String>> getExistHeaderStr(HttpServletRequest request,
      Set<String> headerNames) {
    Map<String, String> existHeaders = new HashMap<>();
    for (String name : headerNames) {
      String value = request.getHeader(name);
      if (value != null && !value.isBlank()) {
        existHeaders.put(name, value);
      }
    }
    return existHeaders.isEmpty() ? Optional.empty() : Optional.of(existHeaders);
  }

  private Optional<Map<String, String>> getExistCookieStr(HttpServletRequest request,
      Set<String> headerNames) {
    Cookie[] cookies = request.getCookies();
    Map<String, String> existCookies = new HashMap<>();
    if (cookies != null) {
      for (Cookie cookie : cookies) {
        if (headerNames.contains(cookie.getName())) {
          String value = cookie.getValue();
          if (value != null && !value.isBlank()) {
            existCookies.put(cookie.getName(), value);
          }
        }
      }
    }
    return existCookies.isEmpty() ? Optional.empty() : Optional.of(existCookies);
  }
}