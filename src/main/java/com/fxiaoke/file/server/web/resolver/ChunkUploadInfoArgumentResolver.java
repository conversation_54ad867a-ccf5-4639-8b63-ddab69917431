package com.fxiaoke.file.server.web.resolver;

import com.fxiaoke.file.server.domain.model.api.request.StsCredential;
import com.fxiaoke.file.server.domain.vo.request.ChunkUploadInfo;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.ws.rs.BadRequestException;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.MissingRequestCookieException;
import org.springframework.web.bind.MissingRequestHeaderException;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

/**
 * Spring MVC 参数解析器，用于处理 ChunkUploadInfo 类型的参数。
 * <p>
 * 这个类的唯一职责是充当 Spring 框架和我们领域对象之间的【适配器】。
 * <p>
 */
@Slf4j
@Component
public class ChunkUploadInfoArgumentResolverFilesOne extends FilesOneBaseArgumentResolver implements
    HandlerMethodArgumentResolver {

  @Override
  public boolean supportsParameter(MethodParameter parameter) {
    return ChunkUploadInfo.class.equals(parameter.getParameterType());
  }

  @Override
  public Object resolveArgument(@NotNull MethodParameter parameter,
      ModelAndViewContainer mavContainer, @NotNull NativeWebRequest webRequest,
      WebDataBinderFactory binderFactory) throws Exception {

    HttpServletRequest httpServletRequest = webRequest.getNativeRequest(HttpServletRequest.class);
    if (httpServletRequest == null) {
      // 这种情况非常罕见，如果发生，说明服务器或框架配置有问题，是一个内部错误
      throw new IllegalStateException("Could not get HttpServletRequest from NativeWebRequest");
    }

    return from(httpServletRequest, parameter);
  }

  /**
   * 静态工厂方法。 这是从 HttpServletRequest 创建并验证 ChunkUploadInfo 实例的【唯一入口】
   *
   * @param request HTTP 请求
   * @return 一个经过完全验证的 ChunkUploadInfo 实例
   * @throws MissingRequestCookieException 如果必需的 Cookie 缺失
   * @throws MissingRequestHeaderException 如果必需的 Header 缺失
   * @throws BadRequestException           如果参数格式错误或业务校验失败
   */
  public ChunkUploadInfo from(HttpServletRequest request, MethodParameter parameter)
      throws MissingRequestCookieException, MissingRequestHeaderException {
    ChunkUploadInfo info = new ChunkUploadInfo();

    // 步骤 1: 解析 Cookie - 贯彻“快速失败”(Fail-Fast)原则
    info.setCookieInfo(findCookieInfo(request).orElseThrow(
        () -> new MissingRequestCookieException("FSAuthXC or ERInfo", parameter)));

    // 步骤 2: 解析 Headers - 同样，快速失败
    info.setKey(getRequiredHeader(request, parameter, "x-fs-key"));
    info.setUploadId(getRequiredHeader(request, parameter, "x-fs-uploadId"));
    String chunkIndexStr = getRequiredHeader(request, parameter, "x-fs-chunkIndex");
    String chunkSizeStr = getRequiredHeader(request, parameter, "x-fs-chunkSize");

    // 步骤 3: 解析具体值并进行业务校验
    info.setChunkIndex(parseIntHeader(chunkIndexStr, "x-fs-chunkIndex"));
    info.setChunkSize(parseLongHeader(chunkSizeStr, "x-fs-chunkSize"));

    long contentLength = request.getContentLengthLong();
    if (info.getChunkSize() != contentLength) {
      // 业务规则校验：分片大小必须与 Content-Length 一致
      throw new BadRequestException(
          "Header 'x-fs-chunkSize' (" + info.getChunkSize() + ") does not match Content-Length ("
              + contentLength + ")");
    }

    // 步骤 4: 组装凭证对象
    StsCredential credential = new StsCredential();
    credential.setStsAk(getRequiredHeader(request, parameter, "x-fs-sts-ak"));
    credential.setStsSk(getRequiredHeader(request, parameter, "x-fs-sts-sk"));
    credential.setStsToken(getRequiredHeader(request, parameter, "x-fs-sts-token"));
    info.setCredential(credential);

    return info;
  }
}
