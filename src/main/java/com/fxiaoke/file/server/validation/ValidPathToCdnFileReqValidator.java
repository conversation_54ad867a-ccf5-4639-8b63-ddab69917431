package com.fxiaoke.file.server.validation;

import com.fxiaoke.file.server.config.CmsPropertiesConfig;
import com.fxiaoke.file.server.domain.model.api.request.PathToCdnFileReq;
import com.fxiaoke.file.server.utils.FileInfoUtil;
import com.google.common.base.Strings;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.regex.Pattern;

/**
 * PathToCdnFileReq 自定义校验器
 * 校验规则：
 * 1. employeeId 校验：必须为正整数或系统调用标识 -10000
 * 2. path 校验：必须是有效的企业文件路径格式（N_、TN_、C_、TC_）
 * 3. extension 校验：必须是支持的图片格式（jpg、webp、png、jpeg、bmp）
 * 4. tags 校验：每个标签必须符合格式要求（小写字母、数字、下划线、连字符，长度不超过20个字符）
 * 5. business 校验：必须在配置的业务线白名单中
 * 6. 业务一致性校验：确保各字段之间的逻辑一致性
 */
@Component
@RefreshScope
public class ValidPathToCdnFileReqValidator implements
    ConstraintValidator<ValidPathToCdnFileReq, PathToCdnFileReq> {

  private final CmsPropertiesConfig config;

  public ValidPathToCdnFileReqValidator(CmsPropertiesConfig cmsPropertiesConfig) {
    this.config = cmsPropertiesConfig;
  }

  /**
   * 系统调用的特殊 employeeId
   */
  private static final Long SYSTEM_EMPLOYEE_ID = -10000L;


  /**
   * 标签格式校验正则：小写字母、数字、下划线、连字符
   */
  private static final Pattern TAG_PATTERN = Pattern.compile("^[a-z0-9_-]+$");

  /**
   * 标签最大长度
   */
  private static final int MAX_TAG_LENGTH = 20;

  @Override
  public boolean isValid(PathToCdnFileReq value, ConstraintValidatorContext context) {
    if (value == null) {
      return true; // null 值由 @NotNull 注解处理
    }

    // 禁用默认的约束违规消息
    context.disableDefaultConstraintViolation();

    boolean isValid = true;

    // 1. 校验 employeeId
    if (!isValidEmployeeId(value.getEmployeeId())) {
      context.buildConstraintViolationWithTemplate(
          "employeeId 必须为正整数或系统调用标识 -10000")
          .addPropertyNode("employeeId")
          .addConstraintViolation();
      isValid = false;
    }

    // 2. 校验 path 格式
    if (!isValidPathFormat(value.getPath())) {
      context.buildConstraintViolationWithTemplate(
          "path 必须是有效的企业文件路径格式，仅支持 N_、TN_、C_、TC_ 开头的路径")
          .addPropertyNode("path")
          .addConstraintViolation();
      isValid = false;
    }

    // 3. 校验 extension 是否为支持的图片格式
    if (!isValidImageExtension(value.getExtension())) {
      context.buildConstraintViolationWithTemplate(
          "extension 必须是支持的图片格式：jpg、webp、png、jpeg、bmp")
          .addPropertyNode("extension")
          .addConstraintViolation();
      isValid = false;
    }

    // 4. 校验 tags 格式
    if (!isValidTags(value.getTags())) {
      context.buildConstraintViolationWithTemplate(
          "tags 中每个标签必须由小写字母、数字、下划线、连字符组成，长度不超过20个字符")
          .addPropertyNode("tags")
          .addConstraintViolation();
      isValid = false;
    }

    // 5. 校验 business 是否在白名单中
    if (!isValidBusiness(value.getBusiness())) {
      context.buildConstraintViolationWithTemplate(
          "business 必须在配置的业务线白名单中")
          .addPropertyNode("business")
          .addConstraintViolation();
      isValid = false;
    }

    // 6. 校验业务一致性
    if (!isBusinessConsistent(value)) {
      context.buildConstraintViolationWithTemplate(
          "业务参数存在不一致性，请检查 tenantId、employeeId、business、path 等字段的关联性")
          .addConstraintViolation();
      isValid = false;
    }

    return isValid;
  }

  /**
   * 校验 employeeId 是否有效
   *
   * @param employeeId 员工ID
   * @return 是否有效
   */
  private boolean isValidEmployeeId(Long employeeId) {
    if (employeeId == null) {
      return false;
    }
    // 必须为正整数或系统调用标识 -10000
    return employeeId > 0 || employeeId.equals(SYSTEM_EMPLOYEE_ID);
  }

  /**
   * 校验路径格式是否有效
   *
   * @param path 文件路径前缀（如 N_、TN_、C_、TC_）
   * @return 是否有效
   */
  private boolean isValidPathFormat(String path) {
    if (Strings.isNullOrEmpty(path)) {
      return false;
    }

    // path 字段实际上是路径类型前缀，不是完整路径
    // 直接检查是否为支持的企业文件路径前缀


    return path.startsWith("N_") || path.startsWith("TN_") ||
           path.startsWith("C_") || path.startsWith("TC_");
  }

  /**
   * 校验图片扩展名是否支持
   *
   * @param extension 文件扩展名
   * @return 是否支持
   */
  private boolean isValidImageExtension(String extension) {
    if (Strings.isNullOrEmpty(extension)) {
      return false;
    }

    String normalizedExtension = extension.toLowerCase().trim();

    // 使用系统已有的图片扩展名校验方法
    return FileInfoUtil.isSupportImageByExtension(normalizedExtension) &&
           config.getCdnFileTypeWhitelist().contains(normalizedExtension);
  }

  /**
   * 校验标签列表格式
   *
   * @param tags 标签列表
   * @return 是否有效
   */
  private boolean isValidTags(List<String> tags) {
    if (tags == null || tags.isEmpty()) {
      return true; // 标签是可选的
    }

    for (String tag : tags) {
      if (!isValidTag(tag)) {
        return false;
      }
    }
    return true;
  }

  /**
   * 校验单个标签格式
   *
   * @param tag 标签
   * @return 是否有效
   */
  private boolean isValidTag(String tag) {
    if (Strings.isNullOrEmpty(tag)) {
      return false;
    }

    // 长度不超过20个字符
    if (tag.length() > MAX_TAG_LENGTH) {
      return false;
    }

    // 必须由小写字母、数字、下划线、连字符组成
    return TAG_PATTERN.matcher(tag).matches();
  }

  /**
   * 校验业务线标识是否在白名单中
   *
   * @param business 业务线标识
   * @return 是否有效
   */
  private boolean isValidBusiness(String business) {
    if (Strings.isNullOrEmpty(business)) {
      return false;
    }

    // 如果配置未注入（如在单元测试中），则跳过业务线白名单校验
    if (config == null) {
      return true; // 向后兼容，允许所有业务线
    }

    // 获取配置的业务线白名单
    List<String> whitelist = config.getCdnBusinessWhitelist();

    // 如果白名单为空或未配置，则允许所有业务线（向后兼容）
    if (whitelist == null || whitelist.isEmpty()) {
      return true;
    }

    // 检查业务线是否在白名单中
    return whitelist.contains(business);
  }

  /**
   * 校验业务一致性
   *
   * @param request 请求对象
   * @return 是否一致
   */
  private boolean isBusinessConsistent(PathToCdnFileReq request) {
    // 基本的业务一致性校验

    // 1. tenantId 必须大于 0
    if (request.getTenantId() == null || request.getTenantId() <= 0) {
      return false;
    }

    // 2. 如果是系统调用（employeeId = -10000），business 不能为空
    if (SYSTEM_EMPLOYEE_ID.equals(request.getEmployeeId())) {
      if (Strings.isNullOrEmpty(request.getBusiness())) {
        return false;
      }
    }

    // 3. 如果提供了 hashCode，不能为空字符串
    if (request.getHashCode() != null && request.getHashCode().trim().isEmpty()) {
      return false;
    }

    // 4. 如果提供了 name，不能为空字符串
    return request.getName() == null || !request.getName().trim().isEmpty();
  }
}
