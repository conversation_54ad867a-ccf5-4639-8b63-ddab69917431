package com.fxiaoke.file.server.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自定义约束注解，用于校验 PathToCdnFileReq 的复杂业务规则。
 */
@Documented
@Constraint(validatedBy = ValidPathToCdnFileReqValidator.class)
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidPathToCdnFileReq {

  String message() default "pathToCdnFileReq 参数校验失败";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}