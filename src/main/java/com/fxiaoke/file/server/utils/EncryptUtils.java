package com.fxiaoke.file.server.utils;

import com.fxiaoke.common.Guard;
import lombok.SneakyThrows;

/**
 * 验证token
 */
public final class EncryptUtils {
  private static final String key = "F~@$^*)+_(&%#!~S";
  private static final Guard encrypt = new Guard(key);

  private EncryptUtils() {
  }

  @SneakyThrows
  public static String encode(String raw) {
    return encrypt.encode(raw);
  }

  @SneakyThrows
  public static String decode(String token) {
    return encrypt.decode(token);
  }
}