package com.fxiaoke.file.server.utils;

import com.fxiaoke.common.TaskScheduler;
import com.fxiaoke.file.server.help.GrayHelper;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.fxiaoke.file.server.config.CmsPropertiesConfig;
import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.RateLimiter;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 文件下载限速工具类
 */
@Slf4j
@Component
public class FileDownloadLimiterUtil {
    private int maxBytesByEnterprisePerSecond;
    private int maxBytesByUserPerSecond;
    private int maxBytesByPathPerSecond;

    private Map<String, Integer> vipEaMaxBytes = Maps.newHashMap();
    private Map<String, RateLimiter> eaLimiters = Maps.newConcurrentMap();
    private Map<String, RateLimiter> userLimiters = Maps.newConcurrentMap();
    private Map<String, RateLimiter> pathLimiters = Maps.newConcurrentMap();

    @Resource
    GrayHelper grayHelper;
    @Resource
    private CmsPropertiesConfig cmsPropertiesConfig;

    private boolean currentCloudRateLimit;

    @PostConstruct
    void init() {
        currentCloudRateLimit = cmsPropertiesConfig.isCurrentCloudRateLimit();
        // 每个企业限速20M
        maxBytesByEnterprisePerSecond = cmsPropertiesConfig.getMaxBytesByEnterprisePerSecond();
        // 每个人限速5M
        maxBytesByUserPerSecond = cmsPropertiesConfig.getMaxBytesByUserPerSecond();
        // 每个文件限速1M
        maxBytesByPathPerSecond = cmsPropertiesConfig.getMaxBytesByPathPerSecond();
        // 有些vip企业，默认的针对单个企业的限制，对于他们来讲太小，可以指定单独的配额
        String parameters = cmsPropertiesConfig.getVipEnterpriseMaxBytesPerSecond();
        if (parameters != null && !parameters.isEmpty()) {
            Map<String, Integer> settings = Maps.newHashMap();
            Splitter.on('&').withKeyValueSeparator('=').split(parameters)
                    .forEach((ea, bytes) -> settings.put(ea, Integer.parseInt(bytes)));
            if (!settings.isEmpty()) {
                vipEaMaxBytes = settings;
            }
        }

        // 每分钟重置限速器，避免长时间占据内存，以及新配置不生效问题
        TaskScheduler.system().scheduleAtFixedRate(() -> {
            // 保存旧的限速器
            Map<String, RateLimiter> limiter01 = eaLimiters;
            Map<String, RateLimiter> limiter02 = userLimiters;
            Map<String, RateLimiter> limiter03 = pathLimiters;
            // 创建新的限速器
            eaLimiters = Maps.newConcurrentMap();
            userLimiters = Maps.newConcurrentMap();
            pathLimiters = Maps.newConcurrentMap();
            // 清空旧的限速器
            Arrays.asList(limiter01, limiter02, limiter03).forEach(Map::clear);
        }, 30, 30, TimeUnit.SECONDS);
    }

    private boolean noSpeedLimit(String ea) {
        return !currentCloudRateLimit || grayHelper.downloadSpeedLimit(ea);
    }

    private RateLimiter createEaLimiter(String ea) {
        int maxBytes = vipEaMaxBytes.getOrDefault(ea, maxBytesByEnterprisePerSecond);
        return RateLimiter.create(maxBytes);
    }

    public InputStream wrapWithSpeedLimit(InputStream in, String ea, String user, String path) {
        if (noSpeedLimit(ea)) {
            return in;
        }
        List<RateLimiter> limiters = new ArrayList<>();
        limiters.add(eaLimiters.computeIfAbsent(ea, this::createEaLimiter));
        if (user != null && !user.isEmpty()) {
            limiters.add(
                    userLimiters.computeIfAbsent(ea + ":" + user, k -> RateLimiter.create(maxBytesByUserPerSecond)));
        }
        limiters.add(pathLimiters.computeIfAbsent(path, k -> RateLimiter.create(maxBytesByPathPerSecond)));

        return new InputStreamLimiter(ea, path, in, limiters, null);
    }
}