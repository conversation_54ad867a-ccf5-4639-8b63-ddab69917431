package com.fxiaoke.file.server.utils;

import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

@Slf4j(topic = "TraceSupplementUtil")
public final class TraceSupplementUtil {

  private TraceSupplementUtil() {
    throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
  }

  private static final String TRACE_ID_PREFIX = "FLS.";
  private static final String APP_NAME_OR_PREFIX = Optional
      .ofNullable(System.getProperty("app.name"))
      .filter(name -> !name.isEmpty())
      .or(() -> Optional
          .ofNullable(System.getenv("K8S_APP"))
          .filter(name -> !name.isEmpty()))
      .orElse("fs-file-server");

  private static final int TRACE_ID_RANDOM_PART_LENGTH = 8;
  private static final String RANDOM_CHARS = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  // Base62 编码所需的字符集
  private static final String BASE62_CHARS = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
  // 创建一个查找映射表，用于快速解码。
  private static final Map<Character, Integer> BASE62_MAP =
      IntStream.range(0, BASE62_CHARS.length())
          .boxed()
          .collect(Collectors.toUnmodifiableMap(
              BASE62_CHARS::charAt,
              i -> i
          ));

  public static void supplementTrace(String fsRoutes) {
    try {
      TraceContext traceContext = TraceContext.get();
      String traceId = traceContext.getTraceId();
      if (StrUtils.notBlank(fsRoutes) && shouldGenerateNewTraceId(traceId)) {
        Optional<String> userIdOpt = EncDecUtil.decryptFsRoute(fsRoutes);
        userIdOpt.ifPresent(TraceSupplementUtil::supplementTraceByUserId);
      }
    } catch (Exception e) {
      log.error("Failed to supplement trace context", e);
    }
  }

  private static void supplementTraceByUserId(String userId) {
    // 检查userId是否为空
    if (Strings.isNullOrEmpty(userId)) {
      log.warn("UserId is null or empty");
      return;
    }

    // 从userId中提取企业账号和员工ID
    String[] parts = userId.split("\\.", 3); // 限制分割次数
    if (parts.length != 3) {
      log.warn("Invalid userId format, userId: {}", userId);
      return;
    }

    String enterpriseAccount = parts[0];
    String ei = parts[1];
    String employeeId = parts[2];
    // 验证解析结果
    if (Strings.isNullOrEmpty(enterpriseAccount) || Strings.isNullOrEmpty(employeeId)) {
      return;
    }

    // 补充上下文身份信息
    MDC.put("ea", enterpriseAccount);
    MDC.put("ei", ei);
    MDC.put("employeeId", employeeId);
    MDC.put("userId", userId);
    // 补充TraceId
    String traceId = generateTraceId(userId);
    MDC.put("traceId", traceId);
  }

  private static boolean shouldGenerateNewTraceId(String traceId) {
    // 简单的验证逻辑，可以根据实际需求调整
    return Strings.isNullOrEmpty(traceId) || traceId.startsWith(APP_NAME_OR_PREFIX);
  }

  /**
   * 生成指定位数的随机字符串（字母+数字） 使用 ThreadLocalRandom 提高性能和线程安全性
   */
  private static String generateRandomString(final int length) {
    if (length <= 0) {
      return "";
    }

    StringBuilder sb = new StringBuilder(length);
    ThreadLocalRandom random = ThreadLocalRandom.current();

    for (int i = 0; i < length; i++) {
      int index = random.nextInt(RANDOM_CHARS.length());
      sb.append(RANDOM_CHARS.charAt(index));
    }
    return sb.toString();
  }

  /**
   * 将 long 类型数字编码为 Base62 字符串
   *
   * @param value 要编码的数字 (必须为正数)
   * @return Base62 编码后的字符串
   */
  private static String toBase62(long value) {
    if (value < 0) {
      value = -value; // 确保为正数
    }
    if (value == 0) {
      return "0";
    }
    StringBuilder sb = new StringBuilder();
    while (value > 0) {
      sb.append(BASE62_CHARS.charAt((int) (value % 62)));
      value /= 62;
    }
    return sb.reverse().toString();
  }

  private static String generateTraceId(String userId) {
    String timestampPart = toBase62(System.currentTimeMillis());
    String randomPart = generateRandomString(TRACE_ID_RANDOM_PART_LENGTH);
    return String.join("-", TRACE_ID_PREFIX + userId, timestampPart, randomPart);
  }

  /**
   * 将 Base62 字符串解码为 long 类型数字
   *
   * @param base62Str Base62 编码的字符串
   * @return 解码后的 long 值
   */
  public static long fromBase62(String base62Str) {
    if (base62Str == null || base62Str.isEmpty()) {
      throw new IllegalArgumentException("Base62 string cannot be null or empty");
    }

    long value = 0L;
    for (char c : base62Str.toCharArray()) {
      Integer charValue = BASE62_MAP.get(c);
      if (charValue == null) {
        throw new IllegalArgumentException("Invalid character in Base62 string: " + c);
      }
      value = value * 62 + charValue;
    }
    return value;
  }

}
