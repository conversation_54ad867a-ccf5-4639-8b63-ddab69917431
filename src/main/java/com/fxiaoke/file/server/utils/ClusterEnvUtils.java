package com.fxiaoke.file.server.utils;

import com.fxiaoke.k8s.support.util.SystemUtils;
import com.fxiaoke.k8s.support.util.SystemUtils.RuntimeEnv;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ClusterEnvUtils {

  // 是否是facishare集群 用以初始化环境相关逻辑
  @Getter
  private static boolean facishareOrPrivateCluster;
  // 是否存在头像文件系统 用以具体业务逻辑中判断是否存在头像文件系统
  @Getter
  private static boolean existsAvatarFileSystem;

  private ClusterEnvUtils() {
    throw new IllegalStateException("Utility class");
  }

  static {
    init(SystemUtils.getRuntimeEnv());
  }

  public static void init(RuntimeEnv runtimeEnv) {
    // 重置状态，确保每次初始化都从干净状态开始
    facishareOrPrivateCluster = false;
    existsAvatarFileSystem = false;
    
    if (runtimeEnv == RuntimeEnv.FIRSTSHARE || runtimeEnv == RuntimeEnv.FONESHARE || runtimeEnv == RuntimeEnv.PRIVATE_DEPLOY_CLOUD) {
      existsAvatarFileSystem = true;
      log.info("exists avatar file system");
      facishareOrPrivateCluster = true; 
      log.info("facishareCluster is true");
    }
    log.info("clusterName:{},cluster description {}", runtimeEnv.name(), runtimeEnv.getName());
  }

}
