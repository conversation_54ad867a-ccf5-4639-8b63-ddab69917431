package com.fxiaoke.file.server.utils;

import com.google.common.base.Strings;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * HTTP头处理工具类
 */
@Slf4j
public class HttpHeaderUtils {

    private HttpHeaderUtils() {
    }

    private static boolean isSafari(HttpServletRequest request) {
        if (request == null) {
            return false;
        }
        try {
            String ua = request.getHeader("User-Agent");
            if (Strings.isNullOrEmpty(ua)) {
                return false;
            }
            // Safari UA contains "Safari" but not "Chrome" (Chrome UA also has "Safari")
            return ua.toLowerCase().contains("safari") && !ua.toLowerCase().contains("chrome");
        } catch (Exception e) {
            log.error("Failed to parse User-Agent header", e);
            return false;
        }
    }

    /**
     * <pre>
     * 符合 RFC 3986 标准的"百分号URL编码"
     * 在这个方法里，空格会被编码成%20，而不是+
     * 和浏览器的encodeURIComponent行为一致
     * </pre>
     */
    private static String encodeURIComponent(String value) {
        try {
            return URLEncoder.encode(value, "UTF-8").replaceAll("\\+", "%20");
        } catch (UnsupportedEncodingException e) {
            log.error("encodeURIComponent exception:{}", value, e);
            // This should not happen with UTF-8
            return value;
        }
    }

    /**
     * Special encoding for Safari.
     * It seems to be a trick to encode UTF-8 bytes as ISO-8859-1 characters.
     */
    private static String encodeURIComponentForSafari(String value) {
        return new String(value.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
    }

    /**
     * <pre>
     * 浏览器下载文件时需要在服务端给出下载的文件名，当文件名是ASCII字符时没有问题
     * 当文件名有非ASCII字符时就有可能出现乱码
     *
     * 这里的实现方式参考这篇文章
     * http://blog.robotshell.org/2012/deal-with-http-header-encoding-for-file-download/
     *
     * 最终设置的response header是这样:
     *
     * Content-Disposition: attachment;
     *                      filename="encoded_text_for_legacy_or_safari";
     *                      filename*=utf-8''encoded_text_for_modern_browsers
     *
     * 其中encoded_text是经过RFC 3986的"百分号URL编码"规则处理过的文件名
     * </pre>
     */
    public static String getFullDisposition(HttpServletRequest request, String filename) {
        boolean isSafari = isSafari(request);
        String encodedFilenameForModern = encodeURIComponent(filename);
        String encodedFilenameForLegacy = isSafari ? encodeURIComponentForSafari(filename) : encodedFilenameForModern;

        return "attachment; filename=\"" + encodedFilenameForLegacy + "\"; filename*=utf-8''"
                + encodedFilenameForModern;
    }

    public static String getHeader(HttpServletRequest request, String name) {
        final java.util.Enumeration<String> headerNames = request.getHeaderNames();
        if (headerNames != null) {
            while (headerNames.hasMoreElements()) {
                final String headerName = headerNames.nextElement();
                if (headerName.equalsIgnoreCase(name)) {
                    return request.getHeader(headerName);
                }
            }
        }
        return null;
    }
}