package com.fxiaoke.file.server.utils;

import com.google.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.DecimalFormat;
import java.util.List;

import static org.springframework.util.FileCopyUtils.BUFFER_SIZE;

/**
 * 带有速率限制的输入流装饰器
 *
 */
@Slf4j
@SuppressWarnings("UnstableApiUsage")
public class InputStreamLimiter extends InputStream {

    private final InputStream inputStream;
    private final List<RateLimiter> limiters;
    private final Runnable endOfStreamHook;

    private final long startTime = System.currentTimeMillis();
    private long totalBytesRead = 0;
    private final String ea;
    private final String path;

    public InputStreamLimiter(String ea, String path, InputStream inputStream, List<RateLimiter> limiters,
            Runnable endOfStreamHook) {
        this.ea = ea;
        this.path = path;
        this.inputStream = inputStream;
        this.limiters = limiters;
        this.endOfStreamHook = endOfStreamHook;
    }

    @Override
    public int read() throws IOException {
        int chr = inputStream.read();
        limiters.forEach(RateLimiter::acquire);
        totalBytesRead++;
        return chr;
    }

    @Override
    public int read(byte[] b, int off, int len) throws IOException {
        int bytesRead = inputStream.read(b, off, len);
        if (bytesRead > 0) {
            for (RateLimiter limiter : limiters) {
                limiter.acquire(bytesRead);
            }
            totalBytesRead += bytesRead;
        } else {
            logSpeed();
            executeHook();
        }
        return bytesRead;
    }

    @Override
    public long transferTo(OutputStream out) throws IOException {
        long transferred = 0;
        byte[] buffer = new byte[BUFFER_SIZE];
        int read;
        while ((read = this.read(buffer, 0, BUFFER_SIZE)) >= 0) {
            out.write(buffer, 0, read);
            transferred += read;
        }
        if (transferred > 0) {
            logSpeed();
            executeHook();
        }
        return transferred;
    }

    @Override
    public void close() throws IOException {
        try {
            super.close();
        } finally {
            inputStream.close();
        }
    }

    private void logSpeed() {
        long cost = System.currentTimeMillis() - startTime;
        if (cost > 1000 || totalBytesRead > 1024 * 1024) {
            DecimalFormat decimalFormat = new DecimalFormat("#,###.00");
            log.info("thunk send file {}/{}, total size {} KB, within {} ms, network speed {}KB/s",
                    ea, path, decimalFormat.format(totalBytesRead / 1024D), cost,
                    decimalFormat.format(totalBytesRead * 1.0 / cost));
        }
    }

    private void executeHook() {
        if (endOfStreamHook != null) {
            try {
                endOfStreamHook.run();
            } catch (Exception e) {
                log.error("Error executing end-of-stream hook", e);
            }
        }
    }
}