package com.fxiaoke.file.server.utils;

import com.fxiaoke.common.Pair;
import com.google.common.base.Strings;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.commons.lang3.math.NumberUtils;

public class FileInfoUtil {
  private FileInfoUtil() {
  }

  private static final Map<String, String> Mappings = iniMimeTypeMapping();

  // 单元测试覆盖率->提取为方法
  public static Map<String, String> iniMimeTypeMapping() {
    Map<String, String> mimeTypeMapping = new HashMap<>();

    final String APPLICATION_POSTSCRIPT = "application/postscript";
    final String APPLICATION_XML = "application/xml";
    final String APPLICATION_VND_MS_EXCEL = "application/vnd.ms-excel";
    final String APPLICATION_X_DIRECTOR = "application/x-director";
    final String APPLICATION_X_FONT = "application/x-font";
    final String APPLICATION_X_GTAR = "application/x-gtar";
    final String APPLICATION_X_HTTPD_PHP = "application/x-httpd-php";
    final String APPLICATION_X_KOAN = "application/x-koan";
    final String AUDIO_MPEG = "audio/mpeg";
    final String APPLICATION_X_TROFF = "application/x-troff";
    final String APPLICATION_X_MAKER = "application/x-maker";
    final String APPLICATION_X_MSDOS_PROGRAM = "application/x-msdos-program";
    final String APPLICATION_X_QGIS = "application/x-qgis";
    final String APPLICATION_X_TRASH = "application/x-trash";
    final String AUDIO_OGG = "audio/ogg";
    final String AUDIO_X_AIFF = "audio/x-aiff";
    final String AUDIO_X_PN_REALAUDIO = "audio/x-pn-realaudio";
    final String CHEMICAL_X_CACTVS_BINARY = "chemical/x-cactvs-binary";
    final String CHEMICAL_X_GAMESS_INPUT = "chemical/x-gamess-input";
    final String CHEMICAL_X_GAUSSIAN_INPUT = "chemical/x-gaussian-input";
    final String CHEMICAL_X_MOPAC_INPUT = "chemical/x-mopac-input";
    final String IMAGE_JPEG = "image/jpeg";
    final String MODEL_MESH = "model/mesh";
    final String TEXT_HTML = "text/html";
    final String TEXT_PLAIN = "text/plain";
    final String TEXT_X_C_SRC = "text/x-c++src";
    final String TEXT_X_TEX = "text/x-tex";
    final String VIDEO_MPEG = "video/mpeg";
    final String TEXT_X_C_HDR = "text/x-c++hdr";
    final String AUDIO_MIDI = "audio/midi";


    mimeTypeMapping.put("ez", "application/andrew-inset");
    mimeTypeMapping.put("anx", "application/annodex");
    mimeTypeMapping.put("atom", "application/atom+xml");
    mimeTypeMapping.put("atomcat", "application/atomcat+xml");
    mimeTypeMapping.put("atomsrv", "application/atomserv+xml");
    mimeTypeMapping.put("lin", "application/bbolin");
    mimeTypeMapping.put("cap", "application/cap");
    mimeTypeMapping.put("pcap", "application/cap");
    mimeTypeMapping.put("cu", "application/cu-seeme");
    mimeTypeMapping.put("davmount", "application/davmount+xml");
    mimeTypeMapping.put("tsp", "application/dsptype");
    mimeTypeMapping.put("es", "application/ecmascript");
    mimeTypeMapping.put("spl", "application/futuresplash");
    mimeTypeMapping.put("hta", "application/hta");
    mimeTypeMapping.put("jar", "application/java-archive");
    mimeTypeMapping.put("ser", "application/java-serialized-object");
    mimeTypeMapping.put("class", "application/java-vm");
    mimeTypeMapping.put("js", "application/javascript");
    mimeTypeMapping.put("m3g", "application/m3g");
    mimeTypeMapping.put("hqx", "application/mac-binhex40");
    mimeTypeMapping.put("cpt", "application/mac-compactpro");
    mimeTypeMapping.put("nb", "application/mathematica");
    mimeTypeMapping.put("nbp", "application/mathematica");
    mimeTypeMapping.put("mdb", "application/msaccess");
    mimeTypeMapping.put("doc", "application/msword");
    mimeTypeMapping.put("dot", "application/msword");
    mimeTypeMapping.put("mxf", "application/mxf");
    mimeTypeMapping.put("bin", "application/octet-stream");
    mimeTypeMapping.put("oda", "application/oda");
    mimeTypeMapping.put("ogx", "application/ogg");
    mimeTypeMapping.put("pdf", "application/pdf");
    mimeTypeMapping.put("key", "application/pgp-keys");
    mimeTypeMapping.put("pgp", "application/pgp-signature");
    mimeTypeMapping.put("prf", "application/pics-rules");
    mimeTypeMapping.put("ps", APPLICATION_POSTSCRIPT);
    mimeTypeMapping.put("ai", APPLICATION_POSTSCRIPT);
    mimeTypeMapping.put("eps", APPLICATION_POSTSCRIPT);
    mimeTypeMapping.put("epsi", APPLICATION_POSTSCRIPT);
    mimeTypeMapping.put("epsf", APPLICATION_POSTSCRIPT);
    mimeTypeMapping.put("eps2", APPLICATION_POSTSCRIPT);
    mimeTypeMapping.put("eps3", APPLICATION_POSTSCRIPT);
    mimeTypeMapping.put("rar", "application/rar");
    mimeTypeMapping.put("rdf", "application/rdf+xml");
    mimeTypeMapping.put("rss", "application/rss+xml");
    mimeTypeMapping.put("rtf", "application/rtf");
    mimeTypeMapping.put("smi", "application/smil");
    mimeTypeMapping.put("smil", "application/smil");
    mimeTypeMapping.put("xhtml", "application/xhtml+xml");
    mimeTypeMapping.put("xht", "application/xhtml+xml");
    mimeTypeMapping.put("xml", APPLICATION_XML);
    mimeTypeMapping.put("xsl", APPLICATION_XML);
    mimeTypeMapping.put("xsd", APPLICATION_XML);
    mimeTypeMapping.put("xspf", "application/xspf+xml");
    mimeTypeMapping.put("zip", "application/zip");
    mimeTypeMapping.put("apk", "application/vnd.android.package-archive");
    mimeTypeMapping.put("cdy", "application/vnd.cinderella");
    mimeTypeMapping.put("kml", "application/vnd.google-earth.kml+xml");
    mimeTypeMapping.put("kmz", "application/vnd.google-earth.kmz");
    mimeTypeMapping.put("xul", "application/vnd.mozilla.xul+xml");
    mimeTypeMapping.put("xls", APPLICATION_VND_MS_EXCEL);
    mimeTypeMapping.put("xlb", APPLICATION_VND_MS_EXCEL);
    mimeTypeMapping.put("xlt", APPLICATION_VND_MS_EXCEL);
    mimeTypeMapping.put("cat", "application/vnd.ms-pki.seccat");
    mimeTypeMapping.put("stl", "application/vnd.ms-pki.stl");
    mimeTypeMapping.put("ppt", "application/vnd.ms-powerpoint");
    mimeTypeMapping.put("pps", "application/vnd.ms-powerpoint");
    mimeTypeMapping.put("odc", "application/vnd.oasis.opendocument.chart");
    mimeTypeMapping.put("odb", "application/vnd.oasis.opendocument.database");
    mimeTypeMapping.put("odf", "application/vnd.oasis.opendocument.formula");
    mimeTypeMapping.put("odg", "application/vnd.oasis.opendocument.graphics");
    mimeTypeMapping.put("otg", "application/vnd.oasis.opendocument.graphics-template");
    mimeTypeMapping.put("odi", "application/vnd.oasis.opendocument.image");
    mimeTypeMapping.put("odp", "application/vnd.oasis.opendocument.presentation");
    mimeTypeMapping.put("otp", "application/vnd.oasis.opendocument.presentation-template");
    mimeTypeMapping.put("ods", "application/vnd.oasis.opendocument.spreadsheet");
    mimeTypeMapping.put("ots", "application/vnd.oasis.opendocument.spreadsheet-template");
    mimeTypeMapping.put("odt", "application/vnd.oasis.opendocument.text");
    mimeTypeMapping.put("odm", "application/vnd.oasis.opendocument.text-master");
    mimeTypeMapping.put("ott", "application/vnd.oasis.opendocument.text-template");
    mimeTypeMapping.put("oth", "application/vnd.oasis.opendocument.text-web");
    mimeTypeMapping.put("xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
    mimeTypeMapping.put("xltx", "application/vnd.openxmlformats-officedocument.spreadsheetml.template");
    mimeTypeMapping.put("pptx",
        "application/vnd.openxmlformats-officedocument.presentationml.presentation");
    mimeTypeMapping.put("ppsx", "application/vnd.openxmlformats-officedocument.presentationml.slideshow");
    mimeTypeMapping.put("potx", "application/vnd.openxmlformats-officedocument.presentationml.template");
    mimeTypeMapping.put("docx",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
    mimeTypeMapping.put("dotx",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.template");
    mimeTypeMapping.put("cod", "application/vnd.rim.cod");
    mimeTypeMapping.put("mmf", "application/vnd.smaf");
    mimeTypeMapping.put("sdc", "application/vnd.stardivision.calc");
    mimeTypeMapping.put("sds", "application/vnd.stardivision.chart");
    mimeTypeMapping.put("sda", "application/vnd.stardivision.draw");
    mimeTypeMapping.put("sdd", "application/vnd.stardivision.impress");
    mimeTypeMapping.put("sdf", "application/vnd.stardivision.math");
    mimeTypeMapping.put("sdw", "application/vnd.stardivision.writer");
    mimeTypeMapping.put("sgl", "application/vnd.stardivision.writer-global");
    mimeTypeMapping.put("sxc", "application/vnd.sun.xml.calc");
    mimeTypeMapping.put("stc", "application/vnd.sun.xml.calc.template");
    mimeTypeMapping.put("sxd", "application/vnd.sun.xml.draw");
    mimeTypeMapping.put("std", "application/vnd.sun.xml.draw.template");
    mimeTypeMapping.put("sxi", "application/vnd.sun.xml.impress");
    mimeTypeMapping.put("sti", "application/vnd.sun.xml.impress.template");
    mimeTypeMapping.put("sxm", "application/vnd.sun.xml.math");
    mimeTypeMapping.put("sxw", "application/vnd.sun.xml.writer");
    mimeTypeMapping.put("sxg", "application/vnd.sun.xml.writer.global");
    mimeTypeMapping.put("stw", "application/vnd.sun.xml.writer.template");
    mimeTypeMapping.put("sis", "application/vnd.symbian.install");
    mimeTypeMapping.put("vsd", "application/vnd.visio");
    mimeTypeMapping.put("wbxml", "application/vnd.wap.wbxml");
    mimeTypeMapping.put("wmlc", "application/vnd.wap.wmlc");
    mimeTypeMapping.put("wmlsc", "application/vnd.wap.wmlscriptc");
    mimeTypeMapping.put("wpd", "application/vnd.wordperfect");
    mimeTypeMapping.put("wp5", "application/vnd.wordperfect5.1");
    mimeTypeMapping.put("wk", "application/x-123");
    mimeTypeMapping.put("7z", "application/x-7z-compressed");
    mimeTypeMapping.put("abw", "application/x-abiword");
    mimeTypeMapping.put("dmg", "application/x-apple-diskimage");
    mimeTypeMapping.put("bcpio", "application/x-bcpio");
    mimeTypeMapping.put("torrent", "application/x-bittorrent");
    mimeTypeMapping.put("cab", "application/x-cab");
    mimeTypeMapping.put("cbr", "application/x-cbr");
    mimeTypeMapping.put("cbz", "application/x-cbz");
    mimeTypeMapping.put("cdf", "application/x-cdf");
    mimeTypeMapping.put("cda", "application/x-cdf");
    mimeTypeMapping.put("vcd", "application/x-cdlink");
    mimeTypeMapping.put("pgn", "application/x-chess-pgn");
    mimeTypeMapping.put("cpio", "application/x-cpio");
    mimeTypeMapping.put("csh", "application/x-csh");
    mimeTypeMapping.put("deb", "application/x-debian-package");
    mimeTypeMapping.put("udeb", "application/x-debian-package");
    mimeTypeMapping.put("dcr", APPLICATION_X_DIRECTOR);
    mimeTypeMapping.put("dir", APPLICATION_X_DIRECTOR);
    mimeTypeMapping.put("dxr", APPLICATION_X_DIRECTOR);
    mimeTypeMapping.put("dms", "application/x-dms");
    mimeTypeMapping.put("wad", "application/x-doom");
    mimeTypeMapping.put("dvi", "application/x-dvi");
    mimeTypeMapping.put("rhtml", "application/x-httpd-eruby");
    mimeTypeMapping.put("pfa", APPLICATION_X_FONT);
    mimeTypeMapping.put("pfb", APPLICATION_X_FONT);
    mimeTypeMapping.put("gsf", APPLICATION_X_FONT);
    mimeTypeMapping.put("pcf", APPLICATION_X_FONT);
    mimeTypeMapping.put("pcf.Z", APPLICATION_X_FONT);
    mimeTypeMapping.put("mm", "application/x-freemind");
    mimeTypeMapping.put("gnumeric", "application/x-gnumeric");
    mimeTypeMapping.put("sgf", "application/x-go-sgf");
    mimeTypeMapping.put("gcf", "application/x-graphing-calculator");
    mimeTypeMapping.put("gtar", APPLICATION_X_GTAR);
    mimeTypeMapping.put("tgz", APPLICATION_X_GTAR);
    mimeTypeMapping.put("taz", APPLICATION_X_GTAR);
    mimeTypeMapping.put("hdf", "application/x-hdf");
    mimeTypeMapping.put("phtml", APPLICATION_X_HTTPD_PHP);
    mimeTypeMapping.put("pht", APPLICATION_X_HTTPD_PHP);
    mimeTypeMapping.put("php", APPLICATION_X_HTTPD_PHP);
    mimeTypeMapping.put("phps", "application/x-httpd-php-source");
    mimeTypeMapping.put("php3", "application/x-httpd-php3");
    mimeTypeMapping.put("php3p", "application/x-httpd-php3-preprocessed");
    mimeTypeMapping.put("php4", "application/x-httpd-php4");
    mimeTypeMapping.put("php5", "application/x-httpd-php5");
    mimeTypeMapping.put("ica", "application/x-ica");
    mimeTypeMapping.put("info", "application/x-info");
    mimeTypeMapping.put("ins", "application/x-internet-signup");
    mimeTypeMapping.put("isp", "application/x-internet-signup");
    mimeTypeMapping.put("iii", "application/x-iphone");
    mimeTypeMapping.put("iso", "application/x-iso9660-image");
    mimeTypeMapping.put("jam", "application/x-jam");
    mimeTypeMapping.put("jnlp", "application/x-java-jnlp-file");
    mimeTypeMapping.put("jmz", "application/x-jmol");
    mimeTypeMapping.put("chrt", "application/x-kchart");
    mimeTypeMapping.put("kil", "application/x-killustrator");
    mimeTypeMapping.put("skp", APPLICATION_X_KOAN);
    mimeTypeMapping.put("skd", APPLICATION_X_KOAN);
    mimeTypeMapping.put("skt", APPLICATION_X_KOAN);
    mimeTypeMapping.put("skm", APPLICATION_X_KOAN);
    mimeTypeMapping.put("kpr", "application/x-kpresenter");
    mimeTypeMapping.put("kpt", "application/x-kpresenter");
    mimeTypeMapping.put("ksp", "application/x-kspread");
    mimeTypeMapping.put("kwd", "application/x-kword");
    mimeTypeMapping.put("kwt", "application/x-kword");
    mimeTypeMapping.put("latex", "application/x-latex");
    mimeTypeMapping.put("lha", "application/x-lha");
    mimeTypeMapping.put("lyx", "application/x-lyx");
    mimeTypeMapping.put("lzh", "application/x-lzh");
    mimeTypeMapping.put("lzx", "application/x-lzx");
    mimeTypeMapping.put("frm", APPLICATION_X_MAKER);
    mimeTypeMapping.put("maker", APPLICATION_X_MAKER);
    mimeTypeMapping.put("frame", APPLICATION_X_MAKER);
    mimeTypeMapping.put("fm", APPLICATION_X_MAKER);
    mimeTypeMapping.put("fb", APPLICATION_X_MAKER);
    mimeTypeMapping.put("book", APPLICATION_X_MAKER);
    mimeTypeMapping.put("fbdoc", APPLICATION_X_MAKER);
    mimeTypeMapping.put("mif", "application/x-mif");
    mimeTypeMapping.put("wmd", "application/x-ms-wmd");
    mimeTypeMapping.put("wmz", "application/x-ms-wmz");
    mimeTypeMapping.put("com", APPLICATION_X_MSDOS_PROGRAM);
    mimeTypeMapping.put("exe", APPLICATION_X_MSDOS_PROGRAM);
    mimeTypeMapping.put("bat", APPLICATION_X_MSDOS_PROGRAM);
    mimeTypeMapping.put("dll", APPLICATION_X_MSDOS_PROGRAM);
    mimeTypeMapping.put("msi", "application/x-msi");
    mimeTypeMapping.put("nc", "application/x-netcdf");
    mimeTypeMapping.put("pac", "application/x-ns-proxy-autoconfig");
    mimeTypeMapping.put("dat", "application/x-ns-proxy-autoconfig");
    mimeTypeMapping.put("nwc", "application/x-nwc");
    mimeTypeMapping.put("o", "application/x-object");
    mimeTypeMapping.put("oza", "application/x-oz-application");
    mimeTypeMapping.put("p7r", "application/x-pkcs7-certreqresp");
    mimeTypeMapping.put("crl", "application/x-pkcs7-crl");
    mimeTypeMapping.put("pyc", "application/x-python-code");
    mimeTypeMapping.put("pyo", "application/x-python-code");
    mimeTypeMapping.put("qgs", APPLICATION_X_QGIS);
    mimeTypeMapping.put("shp", APPLICATION_X_QGIS);
    mimeTypeMapping.put("shx", APPLICATION_X_QGIS);
    mimeTypeMapping.put("qtl", "application/x-quicktimeplayer");
    mimeTypeMapping.put("rpm", "application/x-redhat-package-manager");
    mimeTypeMapping.put("rb", "application/x-ruby");
    mimeTypeMapping.put("sh", "application/x-sh");
    mimeTypeMapping.put("shar", "application/x-shar");
    mimeTypeMapping.put("swf", "application/x-shockwave-flash");
    mimeTypeMapping.put("swfl", "application/x-shockwave-flash");
    mimeTypeMapping.put("scr", "application/x-silverlight");
    mimeTypeMapping.put("sit", "application/x-stuffit");
    mimeTypeMapping.put("sitx", "application/x-stuffit");
    mimeTypeMapping.put("sv4cpio", "application/x-sv4cpio");
    mimeTypeMapping.put("sv4crc", "application/x-sv4crc");
    mimeTypeMapping.put("tar", "application/x-tar");
    mimeTypeMapping.put("tcl", "application/x-tcl");
    mimeTypeMapping.put("gf", "application/x-tex-gf");
    mimeTypeMapping.put("pk", "application/x-tex-pk");
    mimeTypeMapping.put("texinfo", "application/x-texinfo");
    mimeTypeMapping.put("texi", "application/x-texinfo");
    mimeTypeMapping.put("~", APPLICATION_X_TRASH);
    mimeTypeMapping.put("%", APPLICATION_X_TRASH);
    mimeTypeMapping.put("bak", APPLICATION_X_TRASH);
    mimeTypeMapping.put("old", APPLICATION_X_TRASH);
    mimeTypeMapping.put("sik", APPLICATION_X_TRASH);
    mimeTypeMapping.put("t", APPLICATION_X_TROFF);
    mimeTypeMapping.put("tr", APPLICATION_X_TROFF);
    mimeTypeMapping.put("roff", APPLICATION_X_TROFF);
    mimeTypeMapping.put("man", "application/x-troff-man");
    mimeTypeMapping.put("me", "application/x-troff-me");
    mimeTypeMapping.put("ms", "application/x-troff-ms");
    mimeTypeMapping.put("ustar", "application/x-ustar");
    mimeTypeMapping.put("src", "application/x-wais-source");
    mimeTypeMapping.put("wz", "application/x-wingz");
    mimeTypeMapping.put("crt", "application/x-x509-ca-cert");
    mimeTypeMapping.put("xcf", "application/x-xcf");
    mimeTypeMapping.put("fig", "application/x-xfig");
    mimeTypeMapping.put("xpi", "application/x-xpinstall");
    mimeTypeMapping.put("amr", "audio/amr");
    mimeTypeMapping.put("awb", "audio/amr-wb");
    mimeTypeMapping.put("axa", "audio/annodex");
    mimeTypeMapping.put("au", "audio/basic");
    mimeTypeMapping.put("snd", "audio/basic");
    mimeTypeMapping.put("flac", "audio/flac");
    mimeTypeMapping.put("mid", AUDIO_MIDI);
    mimeTypeMapping.put("midi", AUDIO_MIDI);
    mimeTypeMapping.put("kar", AUDIO_MIDI);
    mimeTypeMapping.put("mpga", AUDIO_MPEG);
    mimeTypeMapping.put("mpega", AUDIO_MPEG);
    mimeTypeMapping.put("mp2", AUDIO_MPEG);
    mimeTypeMapping.put("mp3", AUDIO_MPEG);
    mimeTypeMapping.put("m4a", AUDIO_MPEG);
    mimeTypeMapping.put("m3u", "audio/mpegurl");
    mimeTypeMapping.put("oga", AUDIO_OGG);
    mimeTypeMapping.put("ogg", AUDIO_OGG);
    mimeTypeMapping.put("spx", AUDIO_OGG);
    mimeTypeMapping.put("sid", "audio/prs.sid");
    mimeTypeMapping.put("aif", AUDIO_X_AIFF);
    mimeTypeMapping.put("aiff", AUDIO_X_AIFF);
    mimeTypeMapping.put("aifc", AUDIO_X_AIFF);
    mimeTypeMapping.put("gsm", "audio/x-gsm");
    mimeTypeMapping.put("wma", "audio/x-ms-wma");
    mimeTypeMapping.put("wax", "audio/x-ms-wax");
    mimeTypeMapping.put("ra", AUDIO_X_PN_REALAUDIO);
    mimeTypeMapping.put("rm", AUDIO_X_PN_REALAUDIO);
    mimeTypeMapping.put("ram", AUDIO_X_PN_REALAUDIO);
    mimeTypeMapping.put("pls", "audio/x-scpls");
    mimeTypeMapping.put("sd2", "audio/x-sd2");
    mimeTypeMapping.put("wav", "audio/x-wav");
    mimeTypeMapping.put("alc", "chemical/x-alchemy");
    mimeTypeMapping.put("cac", "chemical/x-cache");
    mimeTypeMapping.put("cache", "chemical/x-cache");
    mimeTypeMapping.put("csf", "chemical/x-cache-csf");
    mimeTypeMapping.put("cbin", CHEMICAL_X_CACTVS_BINARY);
    mimeTypeMapping.put("cascii", CHEMICAL_X_CACTVS_BINARY);
    mimeTypeMapping.put("ctab", CHEMICAL_X_CACTVS_BINARY);
    mimeTypeMapping.put("cdx", "chemical/x-cdx");
    mimeTypeMapping.put("cer", "chemical/x-cerius");
    mimeTypeMapping.put("c3d", "chemical/x-chem3d");
    mimeTypeMapping.put("chm", "chemical/x-chemdraw");
    mimeTypeMapping.put("cif", "chemical/x-cif");
    mimeTypeMapping.put("cmdf", "chemical/x-cmdf");
    mimeTypeMapping.put("cml", "chemical/x-cml");
    mimeTypeMapping.put("cpa", "chemical/x-compass");
    mimeTypeMapping.put("bsd", "chemical/x-crossfire");
    mimeTypeMapping.put("csml", "chemical/x-csml");
    mimeTypeMapping.put("csm", "chemical/x-csml");
    mimeTypeMapping.put("ctx", "chemical/x-ctx");
    mimeTypeMapping.put("cxf", "chemical/x-cxf");
    mimeTypeMapping.put("cef", "chemical/x-cxf");
    mimeTypeMapping.put("emb", "chemical/x-embl-dl-nucleotide");
    mimeTypeMapping.put("embl", "chemical/x-embl-dl-nucleotide");
    mimeTypeMapping.put("spc", "chemical/x-galactic-spc");
    mimeTypeMapping.put("inp", CHEMICAL_X_GAMESS_INPUT);
    mimeTypeMapping.put("gam", CHEMICAL_X_GAMESS_INPUT);
    mimeTypeMapping.put("gamin", CHEMICAL_X_GAMESS_INPUT);
    mimeTypeMapping.put("fch", "chemical/x-gaussian-checkpoint");
    mimeTypeMapping.put("fchk", "chemical/x-gaussian-checkpoint");
    mimeTypeMapping.put("cub", "chemical/x-gaussian-cube");
    mimeTypeMapping.put("gau", CHEMICAL_X_GAUSSIAN_INPUT);
    mimeTypeMapping.put("gjc", CHEMICAL_X_GAUSSIAN_INPUT);
    mimeTypeMapping.put("gjf", CHEMICAL_X_GAUSSIAN_INPUT);
    mimeTypeMapping.put("gal", "chemical/x-gaussian-log");
    mimeTypeMapping.put("gcg", "chemical/x-gcg8-sequence");
    mimeTypeMapping.put("gen", "chemical/x-genbank");
    mimeTypeMapping.put("hin", "chemical/x-hin");
    mimeTypeMapping.put("istr", "chemical/x-isostar");
    mimeTypeMapping.put("ist", "chemical/x-isostar");
    mimeTypeMapping.put("jdx", "chemical/x-jcamp-dx");
    mimeTypeMapping.put("dx", "chemical/x-jcamp-dx");
    mimeTypeMapping.put("kin", "chemical/x-kinemage");
    mimeTypeMapping.put("mcm", "chemical/x-macmolecule");
    mimeTypeMapping.put("mmd", "chemical/x-macromodel-input");
    mimeTypeMapping.put("mmod", "chemical/x-macromodel-input");
    mimeTypeMapping.put("mol", "chemical/x-mdl-molfile");
    mimeTypeMapping.put("rd", "chemical/x-mdl-rdfile");
    mimeTypeMapping.put("rxn", "chemical/x-mdl-rxnfile");
    mimeTypeMapping.put("sd", "chemical/x-mdl-sdfile");
    mimeTypeMapping.put("tgf", "chemical/x-mdl-tgf");
    mimeTypeMapping.put("mcif", "chemical/x-mmcif");
    mimeTypeMapping.put("mol2", "chemical/x-mol2");
    mimeTypeMapping.put("b", "chemical/x-molconn-Z");
    mimeTypeMapping.put("gpt", "chemical/x-mopac-graph");
    mimeTypeMapping.put("mop", CHEMICAL_X_MOPAC_INPUT);
    mimeTypeMapping.put("mopcrt", CHEMICAL_X_MOPAC_INPUT);
    mimeTypeMapping.put("mpc", CHEMICAL_X_MOPAC_INPUT);
    mimeTypeMapping.put("zmt", CHEMICAL_X_MOPAC_INPUT);
    mimeTypeMapping.put("moo", "chemical/x-mopac-out");
    mimeTypeMapping.put("mvb", "chemical/x-mopac-vib");
    mimeTypeMapping.put("asn", "chemical/x-ncbi-asn1");
    mimeTypeMapping.put("prt", "chemical/x-ncbi-asn1-ascii");
    mimeTypeMapping.put("ent", "chemical/x-ncbi-asn1-ascii");
    mimeTypeMapping.put("val", "chemical/x-ncbi-asn1-binary");
    mimeTypeMapping.put("aso", "chemical/x-ncbi-asn1-binary");
    mimeTypeMapping.put("pdb", "chemical/x-pdb");
    mimeTypeMapping.put("ros", "chemical/x-rosdal");
    mimeTypeMapping.put("sw", "chemical/x-swissprot");
    mimeTypeMapping.put("vms", "chemical/x-vamas-iso14976");
    mimeTypeMapping.put("vmd", "chemical/x-vmd");
    mimeTypeMapping.put("xtel", "chemical/x-xtel");
    mimeTypeMapping.put("xyz", "chemical/x-xyz");
    mimeTypeMapping.put("bmp", "image/bmp");
    mimeTypeMapping.put("gif", "image/gif");
    mimeTypeMapping.put("ief", "image/ief");
    mimeTypeMapping.put("jpeg", IMAGE_JPEG);
    mimeTypeMapping.put("jpg", IMAGE_JPEG);
    mimeTypeMapping.put("webp", "image/webp");
    mimeTypeMapping.put("jpe", IMAGE_JPEG);
    mimeTypeMapping.put("pcx", "image/pcx");
    mimeTypeMapping.put("png", "image/png");
    mimeTypeMapping.put("svg", "image/svg+xml");
    mimeTypeMapping.put("svgz", "image/svg+xml");
    mimeTypeMapping.put("tiff", "image/tiff");
    mimeTypeMapping.put("tif", "image/tiff");
    mimeTypeMapping.put("djvu", "image/vnd.djvu");
    mimeTypeMapping.put("djv", "image/vnd.djvu");
    mimeTypeMapping.put("wbmp", "image/vnd.wap.wbmp");
    mimeTypeMapping.put("cr2", "image/x-canon-cr2");
    mimeTypeMapping.put("crw", "image/x-canon-crw");
    mimeTypeMapping.put("ras", "image/x-cmu-raster");
    mimeTypeMapping.put("cdr", "image/x-coreldraw");
    mimeTypeMapping.put("pat", "image/x-coreldrawpattern");
    mimeTypeMapping.put("cdt", "image/x-coreldrawtemplate");
    mimeTypeMapping.put("erf", "image/x-epson-erf");
    mimeTypeMapping.put("ico", "image/x-icon");
    mimeTypeMapping.put("art", "image/x-jg");
    mimeTypeMapping.put("jng", "image/x-jng");
    mimeTypeMapping.put("nef", "image/x-nikon-nef");
    mimeTypeMapping.put("orf", "image/x-olympus-orf");
    mimeTypeMapping.put("psd", "image/x-photoshop");
    mimeTypeMapping.put("pnm", "image/x-portable-anymap");
    mimeTypeMapping.put("pbm", "image/x-portable-bitmap");
    mimeTypeMapping.put("pgm", "image/x-portable-graymap");
    mimeTypeMapping.put("ppm", "image/x-portable-pixmap");
    mimeTypeMapping.put("rgb", "image/x-rgb");
    mimeTypeMapping.put("xbm", "image/x-xbitmap");
    mimeTypeMapping.put("xpm", "image/x-xpixmap");
    mimeTypeMapping.put("xwd", "image/x-xwindowdump");
    mimeTypeMapping.put("eml", "message/rfc822");
    mimeTypeMapping.put("igs", "model/iges");
    mimeTypeMapping.put("iges", "model/iges");
    mimeTypeMapping.put("msh", MODEL_MESH);
    mimeTypeMapping.put("mesh", MODEL_MESH);
    mimeTypeMapping.put("silo", MODEL_MESH);
    mimeTypeMapping.put("wrl", "model/vrml");
    mimeTypeMapping.put("vrml", "model/vrml");
    mimeTypeMapping.put("x3dv", "model/x3d+vrml");
    mimeTypeMapping.put("x3d", "model/x3d+xml");
    mimeTypeMapping.put("x3db", "model/x3d+binary");
    mimeTypeMapping.put("manifest", "text/cache-manifest");
    mimeTypeMapping.put("ics", "text/calendar");
    mimeTypeMapping.put("icz", "text/calendar");
    mimeTypeMapping.put("css", "text/css");
    mimeTypeMapping.put("csv", "text/csv");
    mimeTypeMapping.put("323", "text/h323");
    mimeTypeMapping.put("html", TEXT_HTML);
    mimeTypeMapping.put("htm", TEXT_HTML);
    mimeTypeMapping.put("shtml", TEXT_HTML);
    mimeTypeMapping.put("uls", "text/iuls");
    mimeTypeMapping.put("mml", "text/mathml");
    mimeTypeMapping.put("asc", TEXT_PLAIN);
    mimeTypeMapping.put("txt", TEXT_PLAIN);
    mimeTypeMapping.put("text", TEXT_PLAIN);
    mimeTypeMapping.put("pot", TEXT_PLAIN);
    mimeTypeMapping.put("brf", TEXT_PLAIN);
    mimeTypeMapping.put("rtx", "text/richtext");
    mimeTypeMapping.put("sct", "text/scriptlet");
    mimeTypeMapping.put("wsc", "text/scriptlet");
    mimeTypeMapping.put("tm", "text/texmacs");
    mimeTypeMapping.put("ts", "text/texmacs");
    mimeTypeMapping.put("tsv", "text/tab-separated-values");
    mimeTypeMapping.put("jad", "text/vnd.sun.j2me.app-descriptor");
    mimeTypeMapping.put("wml", "text/vnd.wap.wml");
    mimeTypeMapping.put("wmls", "text/vnd.wap.wmlscript");
    mimeTypeMapping.put("bib", "text/x-bibtex");
    mimeTypeMapping.put("boo", "text/x-boo");
    mimeTypeMapping.put("h++", TEXT_X_C_HDR);
    mimeTypeMapping.put("hpp", TEXT_X_C_HDR);
    mimeTypeMapping.put("hxx", TEXT_X_C_HDR);
    mimeTypeMapping.put("hh", TEXT_X_C_HDR);
    mimeTypeMapping.put("c++", TEXT_X_C_SRC);
    mimeTypeMapping.put("cpp", TEXT_X_C_SRC);
    mimeTypeMapping.put("cxx", TEXT_X_C_SRC);
    mimeTypeMapping.put("cc", TEXT_X_C_SRC);
    mimeTypeMapping.put("h", "text/x-chdr");
    mimeTypeMapping.put("htc", "text/x-component");
    mimeTypeMapping.put("c", "text/x-csrc");
    mimeTypeMapping.put("d", "text/x-dsrc");
    mimeTypeMapping.put("diff", "text/x-diff");
    mimeTypeMapping.put("patch", "text/x-diff");
    mimeTypeMapping.put("hs", "text/x-haskell");
    mimeTypeMapping.put("java", "text/x-java");
    mimeTypeMapping.put("lhs", "text/x-literate-haskell");
    mimeTypeMapping.put("moc", "text/x-moc");
    mimeTypeMapping.put("p", "text/x-pascal");
    mimeTypeMapping.put("pas", "text/x-pascal");
    mimeTypeMapping.put("gcd", "text/x-pcs-gcd");
    mimeTypeMapping.put("pl", "text/x-perl");
    mimeTypeMapping.put("pm", "text/x-perl");
    mimeTypeMapping.put("py", "text/x-python");
    mimeTypeMapping.put("scala", "text/x-scala");
    mimeTypeMapping.put("etx", "text/x-setext");
    mimeTypeMapping.put("tk", "text/x-tcl");
    mimeTypeMapping.put("tex", TEXT_X_TEX);
    mimeTypeMapping.put("ltx", TEXT_X_TEX);
    mimeTypeMapping.put("sty", TEXT_X_TEX);
    mimeTypeMapping.put("cls", TEXT_X_TEX);
    mimeTypeMapping.put("vcs", "text/x-vcalendar");
    mimeTypeMapping.put("vcf", "text/x-vcard");
    mimeTypeMapping.put("3gp", "video/3gpp");
    mimeTypeMapping.put("axv", "video/annodex");
    mimeTypeMapping.put("dl", "video/dl");
    mimeTypeMapping.put("dif", "video/dv");
    mimeTypeMapping.put("dv", "video/dv");
    mimeTypeMapping.put("fli", "video/fli");
    mimeTypeMapping.put("gl", "video/gl");
    mimeTypeMapping.put("mpeg", VIDEO_MPEG);
    mimeTypeMapping.put("mpg", VIDEO_MPEG);
    mimeTypeMapping.put("mpe", VIDEO_MPEG);
    mimeTypeMapping.put("mp4", "video/mp4");
    mimeTypeMapping.put("qt", "video/quicktime");
    mimeTypeMapping.put("mov", "video/quicktime");
    mimeTypeMapping.put("ogv", "video/ogg");
    mimeTypeMapping.put("mxu", "video/vnd.mpegurl");
    mimeTypeMapping.put("flv", "video/x-flv");
    mimeTypeMapping.put("lsf", "video/x-la-asf");
    mimeTypeMapping.put("lsx", "video/x-la-asf");
    mimeTypeMapping.put("mng", "video/x-mng");
    mimeTypeMapping.put("asf", "video/x-ms-asf");
    mimeTypeMapping.put("asx", "video/x-ms-asf");
    mimeTypeMapping.put("wm", "video/x-ms-wm");
    mimeTypeMapping.put("wmv", "video/x-ms-wmv");
    mimeTypeMapping.put("wmx", "video/x-ms-wmx");
    mimeTypeMapping.put("wvx", "video/x-ms-wvx");
    mimeTypeMapping.put("avi", "video/x-msvideo");
    mimeTypeMapping.put("movie", "video/x-sgi-movie");
    mimeTypeMapping.put("mpv", "video/x-matroska");
    mimeTypeMapping.put("mkv", "video/x-matroska");
    mimeTypeMapping.put("ice", "x-conference/x-cooltalk");
    mimeTypeMapping.put("sisx", "x-epoc/x-sisx-app");
    mimeTypeMapping.put("vrm", "x-world/x-vrml");

    mimeTypeMapping.put("webm", "video/webm");
    mimeTypeMapping.put("aac","audio/aac");
    mimeTypeMapping.put("apng","image/apng");
    mimeTypeMapping.put("arc","application/x-freearc");
    mimeTypeMapping.put("avif","image/avif");
    mimeTypeMapping.put("azw","application/vnd.amazon.ebook");
    mimeTypeMapping.put("bz","application/x-bzip");
    mimeTypeMapping.put("bz2","application/x-bzip2");
    mimeTypeMapping.put("eot","application/vnd.ms-fontobject");
    mimeTypeMapping.put("epub","application/epub+zip");
    mimeTypeMapping.put("gz","application/gzip");
    mimeTypeMapping.put("jsonld","application/ld+json");
    mimeTypeMapping.put("mjs","text/javascript");
    mimeTypeMapping.put("mpkg","application/vnd.apple.installer+xml");
    mimeTypeMapping.put("opus","audio/opus");
    mimeTypeMapping.put("otf","font/otf");
    mimeTypeMapping.put("ttf","font/ttf");
    mimeTypeMapping.put("weba","audio/webm");
    // 音视频容器（不包含视频则应为 audio/3gpp2）
    mimeTypeMapping.put("3g2","video/3gpp2");
    mimeTypeMapping.put("woff","font/woff");
    mimeTypeMapping.put("woff2","font/woff2");

    mimeTypeMapping.put("heic","image/heic");
    mimeTypeMapping.put("heif","image/heif");

    return mimeTypeMapping;
  }

  /**
   * 根据文件后缀获取文件类型
   *
   * @param extension 文件后缀 如：txt、doc、xls
   * @return 文件类型 已知类型 返回如 text/plain、application/msword、application/vnd.ms-excel,
   * 未知类型或extension为空 返回 application/octet-stream
   */
  public static String getMimeTypeByExtension(String extension) {
    if (extension != null && !extension.isEmpty()) {
      String type = Mappings.get(extension);
      if (type != null) {
        return type;
      }
    }
    return "application/octet-stream";
  }

  public static boolean isImageByExtension(String extension) {
    if (extension != null && !extension.isEmpty()) {
      String type = Mappings.get(extension);
      if (type != null) {
        return type.startsWith("image");
      }
    }
    return false;
  }

  public static boolean isExtensionMatchingMimeType(String extension, String mimeType) {
    // 快速失败检查
    if (isEmpty(extension) || isEmpty(mimeType)) {
      return false;
    }

    // 直接使用小写形式查找，避免多次查找
    String type = Mappings.get(extension.toLowerCase());
    return mimeType.equals(type);
  }

  // 辅助方法，检查字符串是否为空
  private static boolean isEmpty(String str) {
    return str == null || str.isEmpty();
  }

  private static final String SUPPORT_IMAGE_EXTENSION = "jpg,jpeg,png,gif,bmp,webp";
  public static boolean isSupportImageByExtension(String extension) {
    if (extension != null && !extension.isEmpty()) {
      return SUPPORT_IMAGE_EXTENSION.contains(extension);
    }
    return false;
  }

  private static final String SUPPORT_IMAGE_MIME_TYPE = "image/jpeg,image/png,image/gif,image/bmp,image/webp";
  public static boolean isSupportImageByMimeType(String mimeType) {
    if (mimeType != null && !mimeType.isEmpty()) {
      return SUPPORT_IMAGE_MIME_TYPE.contains(mimeType);
    }
    return false;
  }

  public static String getExtensionByMimeType(String mimeType) {
    if (mimeType == null || mimeType.isEmpty()) {
      return "";
    }
    for (Map.Entry<String, String> entry : Mappings.entrySet()) {
      if (entry.getValue().equalsIgnoreCase(mimeType)) {
        // 如果有 . 号则去掉
        String extension = entry.getKey();
        if (extension.startsWith(".")) {
          return extension.substring(1);
        }
        return extension;
      }
    }
    return "";
  }

  private static final Pattern SIZE_PATTERN = Pattern.compile("^(\\d+)\\*(\\d+)$");
  public static Pair<Integer, Integer> getWH(String WH) {
    if (!Strings.isNullOrEmpty(WH)){
      Matcher matcher = SIZE_PATTERN.matcher(WH);
      if (matcher.matches()) {
        int width = NumberUtils.toInt(matcher.group(1).trim());
        int height = NumberUtils.toInt(matcher.group(2).trim());
        if ((width <= 5000 && height <= 3000)) {
          return Pair.build(width, height);
        }
      }
    }
    return Pair.build(0, 0);
  }

}
