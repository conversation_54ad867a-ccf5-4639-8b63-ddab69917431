package com.fxiaoke.file.server.utils;

import lombok.experimental.UtilityClass;

import java.time.Instant;

/**
 * creator: liuys
 * CreateTime: 2025-05-27
 * Description:
 */

@UtilityClass
public class DataUtil {
  /**
   * 检查时间字符串是否已过期
   * @param expireTimeStr ISO-8601格式的时间字符串
   * @return true表示已过期，false表示未过期
   */
  public static boolean isExpired(String expireTimeStr) {
    try {
      Instant expireTime = Instant.parse(expireTimeStr);
      return Instant.now().isAfter(expireTime);
    } catch (Exception e) {
      // 解析失败，认为已过期
      return true;
    }
  }

}
