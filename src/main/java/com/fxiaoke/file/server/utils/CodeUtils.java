package com.fxiaoke.file.server.utils;

import com.fxiaoke.file.server.domain.exception.FileServerException;

public class CodeUtils {
  private CodeUtils() {
  }

  public static void assertConfigItemNotEmpty(String item,String configFileName,String itemName){
    if (item==null|| item.isEmpty()) {
      throw new FileServerException("config item "+itemName+" is null or empty,please check the [ "+configFileName+" ] config file",item);
    }
  }
}
