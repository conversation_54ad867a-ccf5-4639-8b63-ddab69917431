package com.fxiaoke.file.server.utils;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

public class DataTimeFormatUtil {

  private static final String yyyyMMdd = "yyyyMMdd";

  private DataTimeFormatUtil() {
  }

  public static String getCurrentDate(String format) {
    LocalDate currentDate = LocalDate.now();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
    return currentDate.format(formatter);
  }

  public static String getCurrentTime(String format) {
    LocalTime currentTime = LocalTime.now();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
    return currentTime.format(formatter);
  }

  public static String getMinusDays(int days) {
    LocalDate currentDate = LocalDate.now();
    LocalDate clearDate = currentDate.minusDays(days);
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(yyyyMMdd);
    return clearDate.format(formatter);
  }

  // 获取当前年份
  public static String getCurrentYear() {
    LocalDate currentDate = LocalDate.now();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy");
    return currentDate.format(formatter);
  }

  // 获取当前月份
  public static String getCurrentMonth() {
    LocalDate currentDate = LocalDate.now();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM");
    return currentDate.format(formatter);
  }

  // 获取当前日期
  public static String getCurrentDay() {
    LocalDate currentDate = LocalDate.now();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd");
    return currentDate.format(formatter);
  }
}
