package com.fxiaoke.file.server.utils;

import java.util.HashSet;
import java.util.Set;

public final class IgnoreErrorUtil {

  private IgnoreErrorUtil() {
    throw new AssertionError();
  }

  // 使用HashSet存储错误码，提供O(1)查询性能
  private static final Set<String> IGNORABLE_ERRORS = new HashSet<>(8) {{
    add("StatusCode:400");  // HTTP 400 Bad Request
    add("s312010001");     // 文件不存在错误
    add("s312010002");     // 文件过期错误
    add("s312010003");     // META信息不匹配错误
    add("s312010004");     // 其他文件访问错误
    add("s312010009");     // 其他文件访问错误
    add("s312120001");     // 企业Mongo库查询错误
  }};


  public static boolean isMiseMeta(String errStr){
    if (errStr == null || errStr.isEmpty()) {
      return false;
    }
    return errStr.contains("s312010003");
  }

  /**
   * 判断是否是可忽略的错误
   */
  public static boolean isIgnore(String errStr) {
    if (errStr == null || errStr.isEmpty()) {
      return false;
    }

    for (String error : IGNORABLE_ERRORS) {
      if (errStr.contains(error)) {
        return true;
      }
    }
    return false;
  }
}

