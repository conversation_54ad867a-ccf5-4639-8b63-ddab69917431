package com.fxiaoke.file.server.utils;

import java.nio.charset.StandardCharsets;
import java.util.Optional;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class EncDecUtil {
  private static final SecretKeySpec FS_ROUTE_ENCRYPT_KEY =
      new SecretKeySpec("L20s01H0a9Sz8bX0".getBytes(StandardCharsets.UTF_8), "AES");
  private static final IvParameterSpec FS_ROUTE_ENCRYPT_IV =
      new IvParameterSpec("F12k31KOwS86u1T8".getBytes(StandardCharsets.UTF_8));

  public static Optional<String> decryptFsRoute(String encryptedData) {
    if (encryptedData == null || encryptedData.isEmpty()) {
      return Optional.empty();
    }
    try {
      Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
      cipher.init(Cipher.DECRYPT_MODE, FS_ROUTE_ENCRYPT_KEY, FS_ROUTE_ENCRYPT_IV);

      // 先进行Base64解码
      byte[] encryptedBytes = java.util.Base64.getDecoder().decode(encryptedData);

      // 执行解密
      byte[] decrypted = cipher.doFinal(encryptedBytes);

      // 转换为字符串
      return new String(decrypted, StandardCharsets.UTF_8).describeConstable();
    } catch (Exception e) {
      log.error("decry fsRoute fail: {}", encryptedData, e);
      return Optional.empty();
    }
  }
}
