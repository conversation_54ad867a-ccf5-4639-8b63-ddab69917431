package com.fxiaoke.file.server.utils;

import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import com.fxiaoke.file.server.domain.constants.Constant;
import com.google.common.base.Strings;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

@Slf4j
public class BrowserUtils {

  private BrowserUtils() {
  }

  private static boolean isSafari(String userAgent) {
    if (!Strings.isNullOrEmpty(userAgent)) {
      String userAgentLowerCase = userAgent.toLowerCase();
      return userAgentLowerCase.contains("safari") && !userAgentLowerCase.contains("chrome");
    }
    return false;
  }

  /**
   * <pre>
   * 浏览器下载文件时需要在服务端给出下载的文件名，当文件名是ASCII字符时没有问题
   * 当文件名有非ASCII字符时就有可能出现乱码
   *
   * 这里的实现方式参考这篇文章
   * <a href="http://blog.robotshell.org/2012/deal-with-http-header-encoding-for-file-download/">...</a>
   *
   * 最终设置的response header是这样:
   *
   * Content-Disposition: attachment;
   *                      filename="encoded_text";
   *                      filename*=utf-8''encoded_text
   *
   * 其中encoded_text是经过RFC 3986的“百分号URL编码”规则处理过的文件名
   * </pre>
   */
  public static String getDispositionName(String userAgent,String acModel,String filename){
    boolean isSafari = isSafari(userAgent);
    String encodeUTF8Filename = encodeURIComponent(filename);
    String encodedFilename = isSafari?encodeURIComponentForSafari(filename): encodeUTF8Filename;
    StringBuilder contentDispositionNameHeader = new StringBuilder(acModel);
    contentDispositionNameHeader.append(";filename=\"").append(encodedFilename).append("\";");
    contentDispositionNameHeader.append("filename*=utf-8''").append(encodeUTF8Filename);
    return contentDispositionNameHeader.toString();
  }

  /**
   * <pre>
   * 符合 RFC 3986 标准的“百分号URL编码”
   * 在这个方法里，空格会被编码成%20，而不是+
   * 和浏览器的encodeURIComponent行为一致
   * </pre>
   */
  private static String encodeURIComponent(String value) {
    return URLEncoder.encode(value, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
  }

  private static String encodeURIComponentForSafari(String value) {
    return new String(value.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
  }

  public static void setHead(HttpServletResponse response,String acModel,String extensions,String userAgent,String filename){
    response.setHeader(HttpHeaders.CONTENT_DISPOSITION, getDispositionName(userAgent,acModel,filename));
    response.setHeader(HttpHeaders.CONTENT_ENCODING, Constant.DEFAULT_CONTENT_ENCODING);
    response.setHeader(HttpHeaders.CACHE_CONTROL, Constant.DEFAULT_CACHE_CONTROL);
    response.setHeader(HttpHeaders.CONTENT_TYPE, FileInfoUtil.getMimeTypeByExtension(extensions));
  }

  public static void setBase64Head(HttpServletResponse response){
    response.setHeader(HttpHeaders.CONTENT_ENCODING, Constant.DEFAULT_CONTENT_ENCODING);
    response.setHeader(HttpHeaders.CACHE_CONTROL, Constant.DEFAULT_CACHE_CONTROL);
    response.setHeader(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_PLAIN_VALUE);
  }

  public static HttpHeaders getHead(String acModel,String extensions,String userAgent,String filename){
    HttpHeaders headers = new HttpHeaders();
    headers.add(HttpHeaders.CONTENT_DISPOSITION, getDispositionName(userAgent,acModel,filename));
    headers.add(HttpHeaders.CONTENT_ENCODING,  Constant.DEFAULT_CONTENT_ENCODING);
    headers.add(HttpHeaders.CACHE_CONTROL, Constant.DEFAULT_CACHE_CONTROL);
    headers.add(HttpHeaders.CONTENT_TYPE, FileInfoUtil.getMimeTypeByExtension(extensions));
    return headers;
  }

  public static HttpHeaders getHead(String extensions){
    HttpHeaders headers = new HttpHeaders();
    headers.add(HttpHeaders.CONTENT_DISPOSITION, Constant.DEFAULT_CONTENT_DISPOSITION);
    headers.add(HttpHeaders.CONTENT_ENCODING,  Constant.DEFAULT_CONTENT_ENCODING);
    headers.add(HttpHeaders.CACHE_CONTROL, Constant.DEFAULT_CACHE_CONTROL);
    headers.add(HttpHeaders.CONTENT_TYPE, FileInfoUtil.getMimeTypeByExtension(extensions));
    return headers;
  }

  public static String getSimpUserAgent(String ua) {
    try {
      UserAgent userAgent = UserAgentUtil.parse(ua);
      if (userAgent != null) {
        boolean mobile = userAgent.isMobile();
        if (mobile) {
          return "OS:" + userAgent.getOs() + ",OsVersion:" + userAgent.getOsVersion();
        } else {
          return "Browser:" + userAgent.getBrowser() + ",BrowserVersion:" + userAgent.getVersion();
        }
      }
    } catch (Exception e) {
      log.warn("parse user agent fail,userAgent:{}", ua, e);
    }
    return ua;
  }
}
