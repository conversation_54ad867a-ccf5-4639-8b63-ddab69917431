package com.fxiaoke.file.server.utils;

import com.fxiaoke.file.server.domain.model.imaginary.ImageDimension;
import com.fxiaoke.file.server.domain.model.imaginary.ImageExifInfo;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

public class ImageBaseUtils {

  private ImageBaseUtils() {
    // 私有构造函数，禁止实例化
  }

  /**
   * 获取图片的宽度和高度及类型。
   *
   * @param data 图片的字节数组
   * @return ImageExifInfo 对象，包含图片的宽度和高度
   * @throws IllegalArgumentException 如果数据不合法或格式不支持
   */
  public static ImageExifInfo getImageDimensions(byte[] data) throws IllegalArgumentException {

    if (data == null || data.length < 24) {
      throw new IllegalArgumentException("Invalid byte array");
    }

    ByteBuffer buffer = ByteBuffer.wrap(data).order(ByteOrder.BIG_ENDIAN);

    if (isJPEG(data)) {
      int[] dims = parseJPEG(buffer);
      return ImageExifInfo.of(dims[0], dims[1], "jpeg");
    } else if (isPNG(data)) {
      int[] dims = parsePNG(buffer);
      return ImageExifInfo.of(dims[0], dims[1], "png");
    } else if (isGIF(data)) {
      int[] dims = parseGIF(buffer);
      return ImageExifInfo.of(dims[0], dims[1], "gif");
    } else if (isBMP(data)) {
      int[] dims = parseBMP(buffer);
      return ImageExifInfo.of(dims[0], dims[1], "bmp");
    } else if (isWebP(data)) {
      int[] dims = parseWebP(buffer);
      return ImageExifInfo.of(dims[0], dims[1], "webp");
    } else if (isTIFF(data)) {
      int[] dims = parseTIFF(buffer);
      return ImageExifInfo.of(dims[0], dims[1], "tiff");
    }

    throw new IllegalArgumentException("Unsupported image format");
  }

  /**
   * 根据给定的宽度、高度和最大分辨率计算图像压缩后的尺寸。
   * 如果原始图像的像素总数超过最大分辨率，则计算压缩后的宽度和高度。
   * 如果原始图像的像素总数未超过最大分辨率，则返回原始的宽度和高度。
   *
   * @param width 原始图像的宽度
   * @param height 原始图像的高度
   * @param maxResolution 允许的最大分辨率（像素总数）
   * @return 一个 ImageDimension 对象，包含压缩后的宽度和高度
   */
  public static ImageDimension getCompressingWH(int width, int height, double maxResolution) {
    // 检查原始图像的像素总数是否超过最大分辨率
    if (width * height > maxResolution) {
      // 计算压缩比例。通过原始图像像素总数与最大分辨率的比值的平方根得到
      double ratio = Math.sqrt(width * height / maxResolution);
      // 计算压缩后的宽度，将结果四舍五入为整数
      int newWidth = (int) Math.round(width / ratio);
      // 计算压缩后的高度，将结果四舍五入为整数
      int newHeight = (int) Math.round(height / ratio);
      // 创建一个 ImageDimension 对象，包含压缩后的宽度和高度，并返回
      return new ImageDimension(newWidth, newHeight);
    }
    // 如果原始图像的像素总数未超过最大分辨率，直接返回原始的宽度和高度
    return new ImageDimension(width, height);
  }
  
  // 新增 TIFF 格式检测
  private static boolean isTIFF(byte[] data) {
    if (data.length < 4) return false;

    // TIFF 可能是大端序(MM)或小端序(II)
    return (data[0] == 0x4D && data[1] == 0x4D && data[2] == 0x00 && data[3] == 0x2A) || // MM
        (data[0] == 0x49 && data[1] == 0x49 && data[2] == 0x2A && data[3] == 0x00);    // II
  }

  // 新增 TIFF 解析逻辑
  private static int[] parseTIFF(ByteBuffer buffer) {
    boolean isBigEndian = buffer.get(0) == 0x4D;
    buffer.order(isBigEndian ? ByteOrder.BIG_ENDIAN : ByteOrder.LITTLE_ENDIAN);

    // IFD 偏移量在第4-7字节
    int ifdOffset = buffer.getInt(4);

    // 读取 IFD 条目数量
    int numEntries = buffer.getShort(ifdOffset);
    int width = 0;
    int height = 0;

    // 遍历 IFD 条目寻找宽度(0x100)和高度(0x101)标签
    for (int i = 0; i < numEntries; i++) {
      int entryOffset = ifdOffset + 2 + (i * 12);
      int tag = buffer.getShort(entryOffset);

      if (tag == 0x100) { // ImageWidth
        width = buffer.getInt(entryOffset + 8);
      } else if (tag == 0x101) { // ImageLength
        height = buffer.getInt(entryOffset + 8);
      }

      if (width > 0 && height > 0) break;
    }

    if (width == 0 || height == 0) {
      throw new IllegalArgumentException("Unable to parse TIFF size information");
    }

    return new int[]{width, height};
  }

  // 完善 WebP 解析逻辑
  private static int[] parseWebP(ByteBuffer buffer) {
    if (buffer.capacity() < 30) {
      throw new IllegalArgumentException("WebP data length is insufficient");
    }

    // 检查 WebP 类型
    byte[] vp = new byte[4];
    buffer.position(12);
    buffer.get(vp, 0, 4);
    String vpType = new String(vp);

    return switch (vpType) {
      case "VP8 " -> // 有损 WebP
          parseWebPLossy(buffer);
      case "VP8L" -> // 无损 WebP
          parseWebPLossless(buffer);
      case "VP8X" -> // 扩展 WebP
          parseWebPExtended(buffer);
      default -> throw new IllegalArgumentException("Unsupported WebP formats: " + vpType);
    };
  }

  private static int[] parseWebPLossy(ByteBuffer buffer) {
    buffer.position(26);
    int bits = buffer.getShort() & 0x3FFF;
    int width = bits & 0x3FFF;
    bits = buffer.getShort();
    int height = bits & 0x3FFF;
    return new int[]{width, height};
  }

  private static int[] parseWebPLossless(ByteBuffer buffer) {
    buffer.position(21);
    int bits = buffer.get() & 0xFF;
    bits |= (buffer.get() & 0xFF) << 8;
    bits |= (buffer.get() & 0xFF) << 16;
    int width = (bits & 0x3FFF) + 1;
    bits = bits >> 14;
    int height = (bits & 0x3FFF) + 1;
    return new int[]{width, height};
  }

  private static int[] parseWebPExtended(ByteBuffer buffer) {
    buffer.position(24);
    int width = (buffer.get() & 0xFF) | ((buffer.get() & 0xFF) << 8) |
        ((buffer.get() & 0xFF) << 16);
    int height = (buffer.get() & 0xFF) | ((buffer.get() & 0xFF) << 8) |
        ((buffer.get() & 0xFF) << 16);
    return new int[]{width + 1, height + 1};
  }

  private static boolean isJPEG(byte[] data) {
    return data.length >= 3 && data[0] == (byte) 0xFF && data[1] == (byte) 0xD8 && data[2] == (byte) 0xFF;
  }

  private static boolean isPNG(byte[] data) {
    return data.length >= 8 &&
        data[0] == (byte) 0x89 && data[1] == (byte) 0x50 && data[2] == (byte) 0x4E &&
        data[3] == (byte) 0x47 && data[4] == (byte) 0x0D && data[5] == (byte) 0x0A &&
        data[6] == (byte) 0x1A && data[7] == (byte) 0x0A;
  }

  private static boolean isGIF(byte[] data) {
    return data.length >= 6 &&
        data[0] == (byte) 0x47 && data[1] == (byte) 0x49 && data[2] == (byte) 0x46 &&
        data[3] == (byte) 0x38 && (data[4] == (byte) 0x37 || data[4] == (byte) 0x39) && data[5] == (byte) 0x61;
  }

  private static boolean isBMP(byte[] data) {
    return data.length >= 2 && data[0] == (byte) 0x42 && data[1] == (byte) 0x4D;
  }

  private static boolean isWebP(byte[] data) {
    return data.length >= 12 &&
        data[0] == (byte) 0x52 && data[1] == (byte) 0x49 && data[2] == (byte) 0x46 &&
        data[3] == (byte) 0x46 && /* 文件大小 */ data[8] == (byte) 0x57 && data[9] == (byte) 0x45 &&
        data[10] == (byte) 0x42 && data[11] == (byte) 0x50;
  }

  private static int[] parseJPEG(ByteBuffer buffer) {
    // 扫描到 SOF 标记 (e.g., FF C0)
    int offset = 2; // 跳过初始 FF D8 FF
    while (offset < buffer.capacity() - 10) {
      if (buffer.get(offset) == (byte) 0xFF && buffer.get(offset + 1) == (byte) 0xC0) {
        int height = ((buffer.get(offset + 5) & 0xFF) << 8) | (buffer.get(offset + 6) & 0xFF);
        int width = ((buffer.get(offset + 7) & 0xFF) << 8) | (buffer.get(offset + 8) & 0xFF);
        return new int[]{width, height};
      }
      offset++;
    }
    throw new IllegalArgumentException("Unable to parse the JPEG header");
  }

  private static int[] parsePNG(ByteBuffer buffer) {
    // IHDR 块在第8字节后
    if (buffer.capacity() >= 24) {
      int width = buffer.getInt(16);  // 宽度在第16-19字节
      int height = buffer.getInt(20); // 高度在第20-23字节
      return new int[]{width, height};
    }
    throw new IllegalArgumentException("Unable to parse the PNG header");
  }

  private static int[] parseGIF(ByteBuffer buffer) {
    if (buffer.capacity() >= 10) {
      int width = ((buffer.get(6) & 0xFF) | ((buffer.get(7) & 0xFF) << 8));
      int height = ((buffer.get(8) & 0xFF) | ((buffer.get(9) & 0xFF) << 8));
      return new int[]{width, height};
    }
    throw new IllegalArgumentException("Unable to parse the GIF header");
  }

  private static int[] parseBMP(ByteBuffer buffer) {
    if (buffer.capacity() >= 26) {
      buffer.order(ByteOrder.LITTLE_ENDIAN); // BMP 使用小端序
      int width = buffer.getInt(18);  // 宽度在第18-21字节
      int height = buffer.getInt(22); // 高度在第22-25字节
      return new int[]{width, height};
    }
    throw new IllegalArgumentException("Unable to parse the BMP header");
  }

}
