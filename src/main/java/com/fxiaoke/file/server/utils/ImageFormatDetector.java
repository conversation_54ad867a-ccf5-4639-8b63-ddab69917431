package com.fxiaoke.file.server.utils;

import java.io.IOException;
import java.io.InputStream;

/**
 * 图片格式检测工具类
 * 通过读取文件头的魔数来快速判断图片的真实格式
 * 支持 PNG、JPG/JPEG、WebP、BMP、GIF、TIFF、ICO、HEIC、AVIF 格式
 */
public class ImageFormatDetector {

  private ImageFormatDetector() {
    // 私有构造函数，禁止实例化
  }

  /**
   * 检测图片格式
   *
   * @param inputStream 图片输入流
   * @return 图片格式字符串，如果无法识别则返回 null
   * @throws IOException 读取流时发生错误
   */
  public static String detectImageMimeType(InputStream inputStream) throws IOException {
    // 标记当前位置，以便后续重置
    if (!inputStream.markSupported()) {
      throw new IllegalArgumentException("InputStream must support mark/reset");
    }

    // 标记位置，读取足够的字节来判断格式（最多需要16字节用于HEIC检测）
    inputStream.mark(16);

    try {
      byte[] header = new byte[16];
      int bytesRead = inputStream.read(header);

      if (bytesRead < 4) {
        return null; // 文件太小，无法判断格式
      }

      // 检测各种图片格式，按照常见程度排序
      if (isPNG(header, bytesRead)) {
        return "image/png";
      } else if (isJPEG(header, bytesRead)) {
        return "image/jpeg";
      } else if (isWebP(header, bytesRead)) {
        return "image/webp";
      } else if (isTIFF(header, bytesRead)) {
        return "image/tiff";
      } else if (isBMP(header, bytesRead)) {
        return "image/bmp";
      } else if (isGIF(header, bytesRead)) {
        return "image/gif";
      } else if (isHEIC(header, bytesRead)) {
        return "image/heic";
      } else if (isAVIF(header, bytesRead)) {
        return "image/avif";
      } else if (isICO(header, bytesRead)) {
        return "image/x-icon";
      }

      return null; // 无法识别的格式
    } finally {
      // 重置流位置，确保后续读取不受影响
      inputStream.reset();
    }
  }

  /**
   * 检测是否为PNG格式
   * PNG文件签名: 89 50 4E 47 0D 0A 1A 0A
   */
  private static boolean isPNG(byte[] header, int bytesRead) {
    return bytesRead >= 8 &&
        header[0] == (byte) 0x89 && header[1] == (byte) 0x50 &&
        header[2] == (byte) 0x4E && header[3] == (byte) 0x47 &&
        header[4] == (byte) 0x0D && header[5] == (byte) 0x0A &&
        header[6] == (byte) 0x1A && header[7] == (byte) 0x0A;
  }

  /**
   * 检测是否为JPEG格式
   * JPEG文件签名: FF D8 FF (后面可能跟不同的应用段标识)
   * 支持 JFIF (E0)、Exif (E1)、Adobe (EE) 等变体
   */
  private static boolean isJPEG(byte[] header, int bytesRead) {
    if (bytesRead < 4) {
      return false;
    }
    // 所有JPEG文件都以 FF D8 FF 开头
    if (header[0] != (byte) 0xFF || header[1] != (byte) 0xD8 || header[2] != (byte) 0xFF) {
      return false;
    }
    // 第四个字节通常是应用段标识，常见的有：
    // E0 (JFIF), E1 (Exif), E2 (Canon), E3 (Samsung), E8 (SPIFF), EE (Adobe), DB (Quantization Table)
    byte fourthByte = header[3];
    return fourthByte == (byte) 0xE0 || fourthByte == (byte) 0xE1 || fourthByte == (byte) 0xE2 ||
           fourthByte == (byte) 0xE3 || fourthByte == (byte) 0xE8 || fourthByte == (byte) 0xEE ||
           fourthByte == (byte) 0xDB;
  }

  /**
   * 检测是否为WebP格式
   * WebP文件签名: RIFF....WEBP (前4字节是RIFF，第8-11字节是WEBP)
   */
  private static boolean isWebP(byte[] header, int bytesRead) {
    return bytesRead >= 12 &&
        header[0] == (byte) 0x52 && header[1] == (byte) 0x49 && // RI
        header[2] == (byte) 0x46 && header[3] == (byte) 0x46 && // FF
        header[8] == (byte) 0x57 && header[9] == (byte) 0x45 &&  // WE
        header[10] == (byte) 0x42 && header[11] == (byte) 0x50; // BP
  }

  /**
   * 检测是否为TIFF格式
   * TIFF文件签名: 
   * Little-endian: 49 49 2A 00 (II*.）
   * Big-endian: 4D 4D 00 2A (MM.*)
   */
  private static boolean isTIFF(byte[] header, int bytesRead) {
    if (bytesRead < 4) {
      return false;
    }
    // Little-endian TIFF
    if (header[0] == (byte) 0x49 && header[1] == (byte) 0x49 &&
        header[2] == (byte) 0x2A && header[3] == (byte) 0x00) {
      return true;
    }
    // Big-endian TIFF
    return header[0] == (byte) 0x4D && header[1] == (byte) 0x4D &&
           header[2] == (byte) 0x00 && header[3] == (byte) 0x2A;
  }

  /**
   * 检测是否为BMP格式
   * BMP文件签名: 42 4D (BM)
   */
  private static boolean isBMP(byte[] header, int bytesRead) {
    return bytesRead >= 2 &&
        header[0] == (byte) 0x42 && header[1] == (byte) 0x4D;
  }

  /**
   * 检测是否为GIF格式
   * GIF文件签名: 47 49 46 38 37 61 (GIF87a) 或 47 49 46 38 39 61 (GIF89a)
   */
  private static boolean isGIF(byte[] header, int bytesRead) {
    return bytesRead >= 6 &&
        header[0] == (byte) 0x47 && header[1] == (byte) 0x49 && header[2] == (byte) 0x46 && // GIF
        header[3] == (byte) 0x38 &&
        (header[4] == (byte) 0x37 || header[4] == (byte) 0x39) && // 7 or 9
        header[5] == (byte) 0x61; // a
  }

  /**
   * 检测是否为HEIC格式
   * HEIC文件签名: ftyp....heic 或 ftyp....mif1 (从第4字节开始是ftyp，第8-11字节是品牌标识)
   */
  private static boolean isHEIC(byte[] header, int bytesRead) {
    if (bytesRead < 12) {
      return false;
    }
    // 检查是否以 ftyp 开头（从第4字节开始）
    if (header[4] != (byte) 0x66 || header[5] != (byte) 0x74 ||
        header[6] != (byte) 0x79 || header[7] != (byte) 0x70) { // ftyp
      return false;
    }
    // 检查品牌标识是否为 heic、heix、hvc1、mif1 等
    // heic
    if (header[8] == (byte) 0x68 && header[9] == (byte) 0x65 &&
        header[10] == (byte) 0x69 && header[11] == (byte) 0x63) {
      return true;
    }
    // mif1
    return header[8] == (byte) 0x6D && header[9] == (byte) 0x69 &&
        header[10] == (byte) 0x66 && header[11] == (byte) 0x31;
  }

  /**
   * 检测是否为AVIF格式
   * AVIF文件签名: ftyp....avif (从第4字节开始是ftyp，第8-11字节是avif)
   */
  private static boolean isAVIF(byte[] header, int bytesRead) {
    return bytesRead >= 12 &&
        header[4] == (byte) 0x66 && header[5] == (byte) 0x74 && // ft
        header[6] == (byte) 0x79 && header[7] == (byte) 0x70 && // yp
        header[8] == (byte) 0x61 && header[9] == (byte) 0x76 &&  // av
        header[10] == (byte) 0x69 && header[11] == (byte) 0x66; // if
  }

  /**
   * 检测是否为ICO格式
   * ICO文件签名: 00 00 01 00
   */
  private static boolean isICO(byte[] header, int bytesRead) {
    return bytesRead >= 4 &&
        header[0] == (byte) 0x00 && header[1] == (byte) 0x00 &&
        header[2] == (byte) 0x01 && header[3] == (byte) 0x00;
  }
}