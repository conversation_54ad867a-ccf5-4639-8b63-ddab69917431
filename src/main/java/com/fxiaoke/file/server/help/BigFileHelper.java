package com.fxiaoke.file.server.help;

import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.file.server.config.CmsPropertiesConfig;
import com.fxiaoke.file.server.domain.model.api.FileMeta;
import com.fxiaoke.file.server.domain.model.api.FilePathRecord;
import com.fxiaoke.file.server.domain.model.api.Result;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.stereotype.Component;

import com.fxiaoke.common.http.spring.OkHttpSupport;

import jakarta.annotation.Resource;

@Component
public class BigFileHelper {

    @Resource
    CmsPropertiesConfig cmsPropertiesConfig;
    @Resource(name = "httpSupport")
    private OkHttpSupport client;

    public FileMeta getMetaData(String path) {
        String url = cmsPropertiesConfig.getFileMetaUrl().formatted(path);
        Request request = new Request.Builder()
            .url(url)
            .get()
            .build();
        String response = (String) client.syncExecute(request, new SyncCallback() {
          @Override
          public Object response(Response response) throws Exception {
            if (response.body() != null) {
              return response.body().string();
            }
            return null;
          }
        });
        Gson gson = new Gson();
        Result<FileMeta> result = gson.fromJson(response, new TypeToken<Result<FileMeta>>() {}.getType());
        return result.getData();
    }

    public FilePathRecord getFilePathRecord(String path) {
        String url = cmsPropertiesConfig.getFilePathRecordUrl().formatted(path);
        Request request = new Request.Builder()
            .url(url)
            .get()
            .build();
        String response = (String) client.syncExecute(request, new SyncCallback() {
          @Override
          public Object response(Response response) throws Exception {
            if (response.body() != null) {
              return response.body().string();
            }
            return null;
          } 
        });
        Gson gson = new Gson();
        Result<FilePathRecord> result = gson.fromJson(response, new TypeToken<Result<FilePathRecord>>() {}.getType());
        return result.getData();
    }


    
}
