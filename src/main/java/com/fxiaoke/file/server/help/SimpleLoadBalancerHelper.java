package com.fxiaoke.file.server.help;

import com.fxiaoke.file.server.config.CmsPropertiesConfig;
import com.google.common.hash.HashCode;
import com.google.common.hash.Hashing;
import java.nio.charset.StandardCharsets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j(topic = "SimpleLoadBalancer")
public class SimpleLoadBalancerHelper {

  private final CmsPropertiesConfig cmsPropertiesConfig;

  public SimpleLoadBalancerHelper(CmsPropertiesConfig cmsPropertiesConfig) {
    this.cmsPropertiesConfig = cmsPropertiesConfig;
  }

  private int getNodeIndex(String input, int nodeSize) {
    int nodeIndex = 0;
    try {
      HashCode hashCode = HashCode.fromBytes(input.getBytes(StandardCharsets.UTF_8));
      nodeIndex = Hashing.consistentHash(hashCode, nodeSize);
    } catch (Exception e) {
      log.error("Simple Load Balancer Hash Code Error,Input={},NodeSize={}", input, nodeSize, e);
    }
    return nodeIndex;
  }

  public String getAtsNode(String input){
    int nodeIndex = getNodeIndex(input, cmsPropertiesConfig.getAtsServerNodes().size());
    return cmsPropertiesConfig.getAtsServerNodes().get(nodeIndex);
  }

  public String getImaginaryNodeByImageSize(int imageSize) {
    if (imageSize>cmsPropertiesConfig.getImageSizeThreshold()){
      return getBigImaginaryNode();
    }
    return getNormalImaginaryNode();
  }

  public String getNormalImaginaryNode(){
    return cmsPropertiesConfig.getImaginaryServerNodes().getFirst();
  }

  public String getBigImaginaryNode(){
    return cmsPropertiesConfig.getImaginaryServerNodes().getLast();
  }
}
