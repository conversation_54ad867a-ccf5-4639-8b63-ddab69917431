package com.fxiaoke.file.server.help;

import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import org.springframework.stereotype.Component;

@Component
public class GrayHelper {
  // fs-gray-stone配置文件
  private final FsGrayReleaseBiz stone = FsGrayRelease.getInstance("stone");
  // fs-gray-file-system配置文件
  private final FsGrayReleaseBiz fileSystem = FsGrayRelease.getInstance("file-system");

  /**
   * 该企业是否被路由到Stone灰度环境
   * @param enterpriseAccount 企业账号
   * @return 是否被路由到Stone灰度环境
   */
  public boolean isStoneGray(String enterpriseAccount) {
    return stone.isAllow("stone-gray", enterpriseAccount);
  }

  /**
   * 该企业访问原图 是否移除文件类型 以限制Stone服务自动进行图片缩略处理
   * @param enterpriseAccount 企业账号
   * @return 是否获取不进行任何处理的原图
   */
  public boolean isStoneOriginalImageGray(String enterpriseAccount) {
    return stone.isAllow("originalImage", enterpriseAccount);
  }

  /**
   * 该企业访问是否使用ATS进行缓存加速
   * @param enterpriseAccount 企业账号
   * @return 是否使用ATS进行缓存加速
   */
  public boolean isEnableATSCache(String enterpriseAccount) {
    return fileSystem.isAllow("atsCacheEnable", enterpriseAccount);
  }

  /**
   * 该企业访问是否强制压缩原图
   * @param enterpriseAccount 企业账号
   * @return 是否强制压缩原图
   */
  public boolean isForceCompressOriginalImage(String enterpriseAccount){
    return fileSystem.isAllow("forceCompressOriginalImage", enterpriseAccount);
  }

  public boolean downloadSpeedLimit(String ea) {
    return fileSystem.isAllow("speedLimit", ea);
  }

  /**
   *  s3 文件夹下载url走对象存储公网访问还是纷享云接口
   */
  public boolean cloudAllowPublicInternet(String ea) {
    return fileSystem.isAllow("publicInternet", ea);
  }
}
