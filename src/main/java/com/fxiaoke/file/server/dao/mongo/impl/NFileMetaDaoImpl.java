package com.fxiaoke.file.server.dao.mongo.impl;


import com.fxiaoke.file.server.dao.mongo.NFileMetaDao;
import com.fxiaoke.file.server.domain.constants.PathType;
import com.fxiaoke.file.server.domain.entity.NFileMeta;
import com.fxiaoke.file.server.domain.entity.fieids.NFileMetaFieIds;
import com.fxiaoke.file.server.utils.PathStrUtil;
import com.mongodb.ReadPreference;
import java.util.Optional;
import org.mongodb.morphia.Datastore;
import org.mongodb.morphia.query.Query;
import org.springframework.stereotype.Service;

@Service
public class NFileMetaDaoImpl implements NFileMetaDao {

  // 根据Path 的日期是否与当前年月日一致决定使用主库查询还是从库查询
  private Query<NFileMeta> getQuery(Datastore db, PathType pathType, String path) {
    boolean currentTimeEnterpriseFile = PathStrUtil.isCurrentTimeEnterpriseFile(pathType, path);
    // 是当天创建的企业文件查询主库否则查询从库
    if (currentTimeEnterpriseFile) {
      return db.createQuery(NFileMeta.class).useReadPreference(ReadPreference.primary());
    } else {
      return db.createQuery(NFileMeta.class).useReadPreference(ReadPreference.secondaryPreferred());
    }
  }

  @Override
  public Optional<NFileMeta> find(Datastore db, PathType pathType, String ea, String path) {

    Query<NFileMeta> query = getQuery(db, pathType, path);
    query.field(NFileMetaFieIds.ea).equal(ea);

    switch (pathType) {
      case N_FILE -> {
        if (PathStrUtil.is44NPath(path, pathType)) {
          query.or(
              query.criteria(NFileMetaFieIds.nPath).equal(path),
              query.criteria(NFileMetaFieIds.nPath).equal(path + "1")
          );
        } else {
          query.field(NFileMetaFieIds.nPath).equal(path);
        }
      }
      case TN_FILE -> query.field(NFileMetaFieIds.tnPath).equal(path);
      case C_FILE -> query.field(NFileMetaFieIds.cPath).equal(path);
      case TC_FILE -> query.or(query.criteria(NFileMetaFieIds.cPath).equal(path),
          query.criteria(NFileMetaFieIds.tcPath).equal(path));
    }

    return Optional.ofNullable(query.get());
  }
}
