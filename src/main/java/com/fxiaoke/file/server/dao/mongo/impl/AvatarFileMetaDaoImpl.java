package com.fxiaoke.file.server.dao.mongo.impl;

import com.fxiaoke.file.server.dao.mongo.AvatarFileMetaDao;
import com.fxiaoke.file.server.domain.entity.AvatarFileMeta;
import com.fxiaoke.file.server.domain.entity.fieids.AvatarFileMetaFieIds;
import com.github.mongo.support.DatastoreExt;
import jakarta.annotation.PostConstruct;
import org.mongodb.morphia.query.Query;

public class AvatarFileMetaDaoImpl implements AvatarFileMetaDao {

  private final DatastoreExt avatarDataStore;

  public AvatarFileMetaDaoImpl(DatastoreExt avatarDataStore) {
    this.avatarDataStore = avatarDataStore;
  }

  @PostConstruct
  private void initIndex(){
    avatarDataStore.ensureIndexes(AvatarFileMeta.class);
  }

  private Query<AvatarFileMeta> getQuery(){
    return avatarDataStore.createQuery(AvatarFileMeta.class);
  }

  @Override
  public AvatarFileMeta findByPath(String path) {
    Query<AvatarFileMeta> query = getQuery();
    query.field(AvatarFileMetaFieIds.path).equal(path);
    return query.get();
  }

  @Override
  public void save(AvatarFileMeta avatarFileMeta) {
    avatarDataStore.save(avatarFileMeta);
  }

}
