package com.fxiaoke.file.server.dao.mongo.impl;

import com.fxiaoke.file.server.dao.mongo.CdnFileMetaDao;
import com.fxiaoke.file.server.domain.entity.CdnFileMeta;
import com.github.mongo.support.DatastoreExt;
import org.mongodb.morphia.query.Query;
import org.springframework.stereotype.Repository;

@Repository
public class CdnFileMetaDaoImpl implements CdnFileMetaDao {

  private final DatastoreExt warehouseDataStore;

  public CdnFileMetaDaoImpl(DatastoreExt warehouseDataStore) {
    this.warehouseDataStore = warehouseDataStore;
  }

  private DatastoreExt getDatastore() {
    return warehouseDataStore;
  }

  private Query<CdnFileMeta> getQuery() {
    return warehouseDataStore.createQuery(CdnFileMeta.class);
  }

  @Override
  public void create(CdnFileMeta cdnFileMeta) {
    getDatastore().save(cdnFileMeta);
  }
}
