package com.fxiaoke.file.server.domain.entity.model;

import com.fxiaoke.file.server.domain.entity.fieids.NFileMetaFieIds;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.mongodb.morphia.annotations.Property;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class NThumbSubFile {

  @Property(NFileMetaFieIds.nThumbSubFiles_nPath)
  private String nPath;

  @Property(NFileMetaFieIds.nThumbSubFiles_width)
  private Integer width;

  @Property(NFileMetaFieIds.nThumbSubFiles_height)
  private Integer height;

}
