package com.fxiaoke.file.server.domain.vo.request;


import com.fxiaoke.file.server.domain.model.api.CookieInfo;
import com.fxiaoke.file.server.domain.model.api.request.StsCredential;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class S3UploadRequest {
    private String filePath;
    private String fileSize;
    private String chunkCount;
    private String chunkSize;
    private String extension;
    private String expireTime;
    StsCredential credential;
    CookieInfo cookieInfo;
}
