package com.fxiaoke.file.server.domain.model;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * S3对象的元数据信息类，用于存储和传输S3对象的基本属性信息
 */
@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class S3ObjectMetadata {

  /**
   * 存储桶名称
   */
  private String bucketName;

  /**
   * 对象键(在存储桶中的完整路径)
   */
  private String objectKey;

  /**
   * 对象的唯一标识符(Entity Tag)
   */
  private String eTag;

  /**
   * 对象最后修改时间
   */
  private Date lastModified;

  /**
   * 对象内容大小(字节)
   */
  private long contentLength;

  /**
   * 对象的MIME类型
   */
  private String contentType;

  /**
   * HTTP缓存控制头信息
   */
  private String cacheControl;

  /**
   * 内容编码方式(如gzip)
   */
  private String contentEncoding;

  /**
   * 内容处置方式(如attachment)
   */
  private String contentDisposition;

}
