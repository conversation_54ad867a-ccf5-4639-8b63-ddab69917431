package com.fxiaoke.file.server.domain.model.api.request;

import com.fxiaoke.file.server.domain.constants.Constant;
import com.fxiaoke.stone.commons.domain.utils.SignatureUtil;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SignNFileAcRequest extends NFileAcRequestAbstract {

  // 过期时间戳
  @NotNull(message = "Ets not null")
  private Long Ets;

  @Override
  public String getBiz() {
    return Constant.BUSINESS;
  }

  @Override
  public boolean isVerifyMetaSign() {
    return true;
  }

  public String getDsSignRaw(){
    return SignatureUtil.generatorRaw(getFid(),getAcid(), getEts(), getAk(), getFn());
  }

  @Override
  public String toString(){
    StringBuilder builder = superToString(new StringBuilder("SignNFileAcRequest{"));
    builder.append(", Ets=").append(Ets);
    builder.append('}');
    return builder.toString();
  }
}
