package com.fxiaoke.file.server.domain.model.api.request;

import lombok.Data;
import lombok.ToString;

/**
 * 带签名的下载请求
 *
 */
@Data
@ToString
public class SignedDownloadRequest {
    /**
     * accessKey
     */
    private String ak;

    /**
     * 加密的路径
     */
    private String path;

    /**
     * 签名
     */
    private String sign;

    /**
     * 访问者信息
     */
    private String acid;

    /**
     * 过期时间戳
     */
    private String ets;

    /**
     * 文件名 (可选)
     */
    private String filename;
}