package com.fxiaoke.file.server.domain.entity;

import com.fxiaoke.file.server.domain.entity.fieids.CdnFileMetaFieIds;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Field;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Index;
import org.mongodb.morphia.annotations.IndexOptions;
import org.mongodb.morphia.annotations.Indexes;
import org.mongodb.morphia.annotations.Property;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Entity(value = "CdnFileMeta", noClassnameStored = true)
@Indexes({@Index(fields = {@Field(CdnFileMetaFieIds.ea), @Field(CdnFileMetaFieIds.originalPath)},
        options = @IndexOptions(name = CdnFileMetaFieIds.ea_originalPath_index_name, background = true)),
    @Index(fields = {@Field(CdnFileMetaFieIds.cdnReqPath)},
        options = @IndexOptions(name = CdnFileMetaFieIds.cdnReqPath_index_name, background = true)),
    @Index(fields = {@Field(CdnFileMetaFieIds.hashCode)},
        options = @IndexOptions(name = CdnFileMetaFieIds.hashCode_index_name, background = true)),
    @Index(fields = {@Field(CdnFileMetaFieIds.business), @Field(CdnFileMetaFieIds.cdnReqPath)},
        options = @IndexOptions(name = CdnFileMetaFieIds.business_cdnReqPath_index_name, background = true)),

})
public class CdnFileMeta {

  @Id
  private ObjectId _id;

  @Property(CdnFileMetaFieIds.ea)
  private String ea;
  @Property(CdnFileMetaFieIds.employeeId)
  private Long employeeId;

  @Property(CdnFileMetaFieIds.originalPath)
  private String originalPath;

  @Property(CdnFileMetaFieIds.business)
  private String business;
  @Property(CdnFileMetaFieIds.businessUnit)
  private String businessUnit;

  @Property(CdnFileMetaFieIds.tags)
  private List<String> tags;
  @Property(CdnFileMetaFieIds.description)
  private String description;

  @Property(CdnFileMetaFieIds.size)
  private long size;
  @Property(CdnFileMetaFieIds.name)
  private String name;
  @Property(CdnFileMetaFieIds.hashCode)
  private String hashCode;
  @Property(CdnFileMetaFieIds.extension)
  private String extension;

  @Property(CdnFileMetaFieIds.bucket)
  private String bucket;
  @Property(CdnFileMetaFieIds.objectKey)
  private String objectKey;
  @Property(CdnFileMetaFieIds.storageType)
  private String storageType;

  @Property(CdnFileMetaFieIds.cdnReqPath)
  private String cdnReqPath;

  @Property(CdnFileMetaFieIds.status)
  private boolean status;

  @Property(CdnFileMetaFieIds.createDate)
  private Date createDate;
}
