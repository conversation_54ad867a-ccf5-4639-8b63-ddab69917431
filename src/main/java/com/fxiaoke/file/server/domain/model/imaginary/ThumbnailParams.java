package com.fxiaoke.file.server.domain.model.imaginary;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * 缩略图操作参数
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ThumbnailParams extends CommonParams {
    
    /**
     * 缩略图宽度
     */
    @JsonProperty("width")
    private Integer width;
    
    /**
     * 缩略图高度
     */
    @JsonProperty("height")
    private Integer height;
    
    /**
     * 是否保持宽高比
     */
    @JsonProperty("keep_aspect_ratio")
    private Boolean keepAspectRatio = true;
    
    /**
     * 缩略图质量（1-100）
     */
    @JsonProperty("quality")
    private Integer quality = 80;
}