package com.fxiaoke.file.server.domain.vo.request;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 分片下载请求参数
 * 支持HTTP Range请求标准，实现断点续传和分片下载
 */
@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class ChunkDownloadRequest {

    /**
     * 文件路径标识 (必填)
     * 用于定位要下载的文件
     */
    @NotBlank(message = "filePath not empty")
    private String filePath;

    /**
     * 下载令牌 (必填)
     * 用于权限验证和访问控制
     */
    @NotBlank(message = "downloadToken not empty")
    private String downloadToken;

    /**
     * 下载字节范围 (可选)
     * 格式: "bytes=start-end" 例如: "bytes=0-1023"
     * 如果不提供则下载整个文件
     */
    private String range;

    /**
     * 分片大小 (可选)
     * 单位字节，默认1MB (1048576)
     */
    private Integer chunkSize = 1048576;

    /**
     * 条件下载验证 (可选)
     * 文件ETag或Last-Modified，用于验证文件是否被修改
     */
    private String ifRange;

    /**
     * 下载文件名 (可选)
     * 用于设置Content-Disposition响应头
     */
    private String fileName;

    /**
     * 是否内联显示 (可选)
     * true: 浏览器内联显示, false: 下载附件
     * 默认false
     */
    private Boolean inline = false;

    /**
     * 预期的文件大小 (可选)
     * 用于验证文件完整性
     */
    private Long expectedFileSize;

    /**
     * 预期的文件ETag (可选)
     * 用于验证文件完整性
     */
    private String expectedETag;
}