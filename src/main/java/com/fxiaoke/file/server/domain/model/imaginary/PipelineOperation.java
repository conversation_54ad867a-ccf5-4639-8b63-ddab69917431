package com.fxiaoke.file.server.domain.model.imaginary;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.Builder;
import lombok.Data;

/**
 * 图像处理管道操作类
 */
@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PipelineOperation {
  /**
   * 操作名称
   */
  private String operation;

  /**
   * 是否忽略失败并继续执行下一个操作
   */
  private Boolean ignore_failure;

  /**
   * 操作参数
   */
  @JsonTypeInfo(
      use = JsonTypeInfo.Id.NAME,
      include = JsonTypeInfo.As.EXTERNAL_PROPERTY,
      property = "operation"
  )
  @JsonSubTypes({
      @JsonSubTypes.Type(value = ResizeParams.class, name = "resize"),
      @JsonSubTypes.Type(value = CropParams.class, name = "crop"),
      @JsonSubTypes.Type(value = SmartCropParams.class, name = "smartcrop"),
      @JsonSubTypes.Type(value = WatermarkParams.class, name = "watermark"),
      @JsonSubTypes.Type(value = WatermarkImageParams.class, name = "watermarkImage"),
      @JsonSubTypes.Type(value = BlurParams.class, name = "blur"),
      @JsonSubTypes.Type(value = ExtractParams.class, name = "extract"),
      @JsonSubTypes.Type(value = ConvertParams.class, name = "convert"),
      @JsonSubTypes.Type(value = ThumbnailParams.class, name = "thumbnail")
  })
  private OperationParams params;
}
