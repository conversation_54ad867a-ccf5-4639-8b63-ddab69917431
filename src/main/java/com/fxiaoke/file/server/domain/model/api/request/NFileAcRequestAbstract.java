package com.fxiaoke.file.server.domain.model.api.request;

import com.fxiaoke.file.server.utils.StrUtils;
import com.fxiaoke.stone.commons.domain.utils.FilenameUtil;
import com.fxiaoke.stone.commons.domain.utils.SignatureUtil;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString
@EqualsAndHashCode
public abstract class NFileAcRequestAbstract implements NFileAcRequest {
  // 文件加密信息串
  @NotBlank(message = "Fid not empty")
  private String Fid;
  // 访问者身份链 例如：
  // 场景一. acid=acEI.acEmployeeId
  // 场景二. acid=acEI.acEmployeeId.acOutEnterpriseId.acOutEmployeeId
  @NotBlank(message = "Acid not empty")
  private String Acid;
  // 访问accessKey
  @NotBlank(message = "Ak not empty")
  @Size(min = 24, max = 24, message = "Invalid accessKey")
  private String Ak;
  // 文件名
  @NotBlank(message = "Fn not empty")
  private String Fn;
  // 访问签名
  @NotBlank(message = "Sig not empty")
  private String Sig;
  // 请求参数消息摘要
  @NotBlank(message = "Ds not empty")
  private String Ds;
  // 附件在浏览器中呈现方式 内联/下载模式 （可选）
  @Pattern(regexp = "inline|attachment", message = "Invalid acModel type. Must be 'inline' or 'attachment'")
  private String acModel="inline";
  // 文件像素宽高 例如：100*100 0*0（表示原图）（可选）
  private String size="0*0";

  private String linkId;

  public boolean isInline() {
    return "inline".equalsIgnoreCase(acModel);
  }

  /**
   * 获取扩展名并转换为小写
   * @return 不带 . 号的文件扩展名，转换为小写
   */
  public String getExt(){
    String ext = FilenameUtil.getExtension(getFn());
    if (StrUtils.notBlank(ext)) {
      return ext.toLowerCase();
    }
    return "";
  }

  public String getAcSignRaw(){
    return SignatureUtil.generatorRaw(getFid(), getAcid(), getEts());
  }

  public StringBuilder superToString(StringBuilder strBuilder) {
    strBuilder.append("Fid='").append(Fid).append('\'');
    strBuilder.append(", Acid='").append(Acid).append('\'');
    strBuilder.append(", Ak='").append(Ak).append('\'');
    strBuilder.append(", Fn='").append(Fn).append('\'');
    strBuilder.append(", Sig='").append(Sig).append('\'');
    strBuilder.append(", Ds='").append(Ds).append('\'');
    strBuilder.append(", acModel='").append(acModel).append('\'');
    strBuilder.append(", size='").append(size).append('\'');
    strBuilder.append(", linkId='").append(linkId).append('\'');
    return strBuilder;
  }

}
