package com.fxiaoke.file.server.domain.model.api.request;

import com.fxiaoke.file.server.domain.constants.Constant;
import com.fxiaoke.stone.commons.domain.utils.SignatureUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class CookieNFileAcRequest extends NFileAcRequestAbstract {

  @Override
  public Long getEts() {
    return 0L;
  }

  @Override
  public String getBiz() {
    return Constant.BUSINESS;
  }

  @Override
  public boolean isVerifyMetaSign() {
    return true;
  }

  @Override
  public String getDsSignRaw(){
    return SignatureUtil.generatorRaw(getFid(),getAcid(),getAk(), getFn());
  }

  @Override
  public String getAcSignRaw(){
    return SignatureUtil.generatorRaw(getFid(), getAcid());
  }

  @Override
  public String toString(){
    StringBuilder builder = superToString(new StringBuilder("CookieNFileAcRequest{"));
    builder.append('}');
    return builder.toString();
  }
}
