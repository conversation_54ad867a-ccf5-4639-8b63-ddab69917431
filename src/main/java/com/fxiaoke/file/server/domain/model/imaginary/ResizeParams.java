package com.fxiaoke.file.server.domain.model.imaginary;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * 调整大小操作的参数类
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ResizeParams extends CommonParams {
    /**
     * 图像宽度
     */
    private Integer width;
    
    /**
     * 图像高度
     */
    private Integer height;
    
    /**
     * 是否禁用裁剪转换，默认为true
     */
    private Boolean nocrop;
    
    /**
     * 应用宽高比，例如：16:9
     */
    private String aspectratio;
} 