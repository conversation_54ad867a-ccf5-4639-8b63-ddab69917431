package com.fxiaoke.file.server.domain.model.imaginary;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.SuperBuilder;

/**
 * 通用参数类，包含所有操作都可能用到的参数
 */
@Data
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CommonParams implements OperationParams {
    /**
     * JPEG图像质量，范围1-100，默认为80
     */
    private Integer quality;
    
    /**
     * PNG压缩级别，默认为6
     */
    private Integer compression;
    
    /**
     * 输出图像格式，可选值：jpeg, png, webp, auto
     */
    private String type;
    
    /**
     * 是否强制图像转换大小，默认为false
     */
    private Boolean force;
    
    /**
     * 旋转角度，必须是90的倍数
     */
    private Integer rotate;
    
    /**
     * 是否禁用基于EXIF方向的自动旋转，默认为false
     */
    private Boolean norotation;
    
    /**
     * 是否禁用添加ICC配置文件元数据，默认为false
     */
    private Boolean noprofile;
    
    /**
     * 是否移除原始图像元数据，如EXIF元数据，默认为false
     */
    private Boolean stripmeta;
    
    /**
     * 是否翻转图像，默认为false
     */
    private Boolean flip;
    
    /**
     * 是否上下翻转图像，默认为false
     */
    private Boolean flop;
    
    /**
     * 扩展模式，可选值：black, copy, mirror, white, lastpixel, background
     * black：用黑色填充
     * copy：复制边缘像素
     * mirror：镜像边缘像素（默认值）
     * white：用白色填充
     * lastpixel：使用最后一个像素填充
     * background：使用 background 参数指定的颜色填充
     */
    private String extend;
    
    /**
     * 背景RGB颜色，例如：255,200,150
     */
    private String background;
    
    /**
     * 输出图像的颜色空间，可选值：srgb, bw
     */
    private String colorspace;
    
    /**
     * 是否使用渐进式/交错式格式输出图像，默认为false
     */
    private Boolean interlace;
    
    /**
     * 是否启用8位量化，仅适用于PNG图像，默认为false
     */
    private Boolean palette;
} 