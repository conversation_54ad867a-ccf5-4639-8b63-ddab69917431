package com.fxiaoke.file.server.domain.model.api.request;

public interface NFileAcRequest {
  String getFid();
  String getAcid();
  void setAcid(String acid);
  Long getEts();
  String getAk();
  String getFn();
  String getSig();
  String getDs();
  String getAcModel();
  String getSize();
  String getLinkId();

  boolean isInline();

  /**
   * 获取业务线标识
   * @return 业务线标识
   */
  String getBiz();

  /**
   * 是否验证元数据签名
   * @return 验证/不验证
   */
  boolean isVerifyMetaSign();

  /**
   * 获取参数签名原文
   * @return 参数签名原文
   */
  String getDsSignRaw();

  /**
   * 获取访问签名原文
   * @return 访问签名原文
   */
  String getAcSignRaw();

  /**
   * 从文件名中获取文件扩展类型
   * @return 不带 . 号的文件扩展名
   */
  String getExt();
}
