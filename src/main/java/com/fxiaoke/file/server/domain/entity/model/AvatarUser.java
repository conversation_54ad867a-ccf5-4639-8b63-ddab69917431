package com.fxiaoke.file.server.domain.entity.model;

import com.fxiaoke.file.server.domain.entity.fieids.AvatarFileMetaFieIds;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.mongodb.morphia.annotations.Property;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class AvatarUser {
  @Property(AvatarFileMetaFieIds.avatarUser_enterpriseAccount)
  private String enterpriseAccount;
  @Property(AvatarFileMetaFieIds.avatarUser_employeeId)
  private Integer employeeId;
  @Property(AvatarFileMetaFieIds.avatarUser_sessionId)
  private String sessionId;
}
