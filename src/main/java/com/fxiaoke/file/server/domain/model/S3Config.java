package com.fxiaoke.file.server.domain.model;

import com.amazonaws.Protocol;
import com.fxiaoke.file.server.domain.constants.Constant;
import com.fxiaoke.file.server.utils.CodeUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class S3Config {
  // 企业账号
  private String ea;
  // 云类型
  private String cloudType;

  // 正式存储桶
  private String bucket;
  // 临时存储桶
  private String tempBucket;

  // ASK
  private String accessKeyId;
  private String accessKeySecret;
  // 签名算法类型
  private String type;

  // 对象存储端点
  private String endpoint;
  // 对象存储区域
  private String region;
  // 是否启用路径样式
  private boolean pathStyleEnabled;

  // 代理地址
  private String proxyHost;
  private String proxyPort;

  // 获取网络配置 连接超时、读取超时、最大连接数、最大重试次数
  private int connectionTimeout;
  private int socketTimeout;
  private int maxConnections;
  private int maxErrorRetry;

  // 是否直传正式存储桶
  private boolean ableShardUploadDirectlyOfficial;
  // 最小分片大小 默认5M,小于5M走本地合并上传
  private int minSegmentSize;
  // 临时文件存储目录
  private String sharedDisksDir;

  // 读缓冲区大小
  private int readLimit;

  // 检查配置
  public void checkConfig(){
    CodeUtils.assertConfigItemNotEmpty(ea, Constant.DEFAULT_S3_CONFIG_NAME,"ea");
    CodeUtils.assertConfigItemNotEmpty(cloudType, Constant.DEFAULT_S3_CONFIG_NAME,"cloudType");
    CodeUtils.assertConfigItemNotEmpty(bucket,Constant.DEFAULT_S3_CONFIG_NAME,"bucket");
    // 备份服务不需要临时存储桶
    if (!ea.contains("backup_")){
      CodeUtils.assertConfigItemNotEmpty(tempBucket,Constant.DEFAULT_S3_CONFIG_NAME,"tempBucket");
    }
    CodeUtils.assertConfigItemNotEmpty(accessKeyId,Constant.DEFAULT_S3_CONFIG_NAME,"accessKeyId");
    CodeUtils.assertConfigItemNotEmpty(accessKeySecret,Constant.DEFAULT_S3_CONFIG_NAME,"accessKeySecret");
    CodeUtils.assertConfigItemNotEmpty(endpoint,Constant.DEFAULT_S3_CONFIG_NAME,"endPoint");
  }

  // 获取协议
  public Protocol getProtocol(){
    return Constant.DEFAULT_HTTP_TYPE.equals(type)?Protocol.HTTPS:Protocol.HTTP;
  }

  // 是否启用代理
  public boolean isProxyEnabled(){
    return proxyHost!=null && !proxyHost.isEmpty() && proxyPort!=null && !proxyPort.isEmpty();
  }

  public boolean isSpecifySignerOverride(){
    return type != null && !type.isEmpty();
  }

}
