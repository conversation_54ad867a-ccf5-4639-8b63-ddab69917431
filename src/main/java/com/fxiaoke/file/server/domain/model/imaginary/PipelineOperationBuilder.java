package com.fxiaoke.file.server.domain.model.imaginary;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * 管道操作构建工具类
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class PipelineOperationBuilder {

    /**
     * 创建调整大小操作
     *
     * @param width 宽度
     * @param height 高度
     * @return 调整大小操作
     */
    public static PipelineOperation resize(Integer width, Integer height) {
        ResizeParams params = ResizeParams.builder()
                .width(width)
                .height(height)
                .build();
        
        return PipelineOperation.builder()
                .operation("resize")
                .params(params)
                .build();
    }
    
    /**
     * 创建裁剪操作
     *
     * @param width 宽度
     * @param height 高度
     * @param gravity 重力，可选值：north, south, centre, west, east, smart
     * @return 裁剪操作
     */
    public static PipelineOperation crop(Integer width, Integer height, String gravity) {
        CropParams params = CropParams.builder()
                .width(width)
                .height(height)
                .gravity(gravity)
                .build();
        
        return PipelineOperation.builder()
                .operation("crop")
                .params(params)
                .build();
    }
    
    /**
     * 创建智能裁剪操作
     *
     * @param width 宽度
     * @param height 高度
     * @return 智能裁剪操作
     */
    public static PipelineOperation smartCrop(Integer width, Integer height) {
        SmartCropParams params = SmartCropParams.builder()
                .width(width)
                .height(height)
                .build();
        
        return PipelineOperation.builder()
                .operation("smartcrop")
                .params(params)
                .build();
    }
    
    /**
     * 创建水印操作
     *
     * @param text 水印文本
     * @param opacity 不透明度
     * @return 水印操作
     */
    public static PipelineOperation watermark(String text, Float opacity) {
        WatermarkParams params = WatermarkParams.builder()
                .text(text)
                .opacity(opacity)
                .build();
        
        return PipelineOperation.builder()
                .operation("watermark")
                .params(params)
                .build();
    }
    
    /**
     * 创建图像水印操作
     *
     * @param imageUrl 水印图像URL
     * @param opacity 不透明度
     * @return 图像水印操作
     */
    public static PipelineOperation watermarkImage(String imageUrl, Float opacity) {
        WatermarkImageParams params = WatermarkImageParams.builder()
                .image(imageUrl)
                .opacity(opacity)
                .build();
        
        return PipelineOperation.builder()
                .operation("watermarkImage")
                .params(params)
                .build();
    }
    
    /**
     * 创建模糊操作
     *
     * @param sigma 高斯掩码大小
     * @return 模糊操作
     */
    public static PipelineOperation blur(Float sigma) {
        BlurParams params = BlurParams.builder()
                .sigma(sigma)
                .build();
        
        return PipelineOperation.builder()
                .operation("blur")
                .params(params)
                .build();
    }
    
    /**
     * 创建提取操作
     *
     * @param top 顶部边缘
     * @param left 左侧边缘
     * @param areaWidth 区域宽度
     * @param areaHeight 区域高度
     * @return 提取操作
     */
    public static PipelineOperation extract(Integer top, Integer left, Integer areaWidth, Integer areaHeight) {
        ExtractParams params = ExtractParams.builder()
                .top(top)
                .left(left)
                .areawidth(areaWidth)
                .areaheight(areaHeight)
                .build();
        
        return PipelineOperation.builder()
                .operation("extract")
                .params(params)
                .build();
    }
    
    /**
     * 创建转换操作
     *
     * @param type 输出格式，可选值：jpeg, png, webp, auto
     * @return 转换操作
     */
    public static PipelineOperation convert(String type) {
        ConvertParams params = ConvertParams.builder()
                .type(type)
                .build();
        
        return PipelineOperation.builder()
                .operation("convert")
                .params(params)
                .build();
    }

    /**
     * 创建缩略图操作
     *
     * @param width 缩略图宽度
     * @param height 缩略图高度
     * @param type 目标图片类型
     * @param keepAspectRatio 是否保持宽高比，默认为true
     * @param quality 缩略图质量（1-100），默认为80
     * @return 缩略图操作
     */
    public static PipelineOperation thumbnail(Integer width, Integer height,String type, Boolean keepAspectRatio, Integer quality) {
        ThumbnailParams params = ThumbnailParams.builder()
                .width(width)
                .height(height)
                .build();
        params.setWidth(width);
        params.setHeight(height);

        if (type != null) {
            params.setType(type);
        }

        if (keepAspectRatio != null) {
            params.setKeepAspectRatio(keepAspectRatio);
        }

        if (quality != null) {
            params.setQuality(quality);
        }

        return PipelineOperation.builder()
            .operation("thumbnail")
            .params(params)
            .build();
    }

    /**
     * 创建缩略图操作（使用默认参数）
     *
     * @param width 缩略图宽度
     * @param height 缩略图高度
     * @return 缩略图操作
     */
    public static PipelineOperation thumbnail(Integer width, Integer height) {
        return thumbnail(width, height, null, null, null);
    }

} 