package com.fxiaoke.file.server.domain.constants;

import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public enum DataBaseTransferStatus {

  NOT_STARTED(0), // 未开始
  IN_PROGRESS(1), // 进行中
  COMPLETED(2), // 已完成
  ANOMALY(-1), // 异常状态
  OTHER(3) // 其他为止
  ;

  private final int status;

  DataBaseTransferStatus(int status) {
    this.status = status;
  }

  public static DataBaseTransferStatus of(int status) {
    for (DataBaseTransferStatus value : DataBaseTransferStatus.values()) {
      if (value.status == status) {
        return value;
      }
    }
    return OTHER; // 默认返回OTHER
  }
}
