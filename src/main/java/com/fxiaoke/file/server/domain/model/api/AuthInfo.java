package com.fxiaoke.file.server.domain.model.api;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class AuthInfo {
  private String enterpriseAccount;
  private long enterpriseId;
  private long employeeId;

  private String upstreamEa;
  private long downstreamOuterTenantId;
  private long downstreamOuterUid;

  public String getAcid() {
    return enterpriseId + "." + employeeId;
  }

  public String getEi() {
    return String.valueOf(enterpriseId);
  }
  public String getDownstreamAcid() {
    return downstreamOuterTenantId + "." + downstreamOuterUid;
  }

  public static AuthInfo ofDownstream(long downstreamOuterTenantId, long downstreamOuterUid) {
    AuthInfo authInfo = new AuthInfo();
    authInfo.setDownstreamOuterTenantId(downstreamOuterTenantId);
    authInfo.setDownstreamOuterUid(downstreamOuterUid);
    return authInfo;
  }
}
