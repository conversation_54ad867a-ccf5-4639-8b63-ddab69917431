package com.fxiaoke.file.server.domain.model.imaginary;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * 图像水印操作的参数类
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WatermarkImageParams extends CommonParams {
    /**
     * 水印图像URL，指向远程HTTP服务器
     */
    private String image;
    
    /**
     * 水印图像的顶部位置
     */
    private Integer top;
    
    /**
     * 水印图像的左侧位置
     */
    private Integer left;
    
    /**
     * 水印图像的不透明度
     */
    private Float opacity;
} 