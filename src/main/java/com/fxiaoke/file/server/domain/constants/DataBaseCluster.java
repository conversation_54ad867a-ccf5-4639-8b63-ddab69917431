package com.fxiaoke.file.server.domain.constants;

import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public enum DataBaseCluster {

  N("n","线下默认MongoDB集群"),
  N1("n1","线上MongoDB集群1"),
  N2("n2","线上MongoDB集群2"),
  Shard("sharded","分片MongoDB集群")
  ;

  private final String clusterName;
  private final String description;

  DataBaseCluster(String clusterName, String description) {
    this.clusterName = clusterName;
    this.description = description;
  }
}
