package com.fxiaoke.file.server.domain.model.api.request;

import com.fxiaoke.stone.commons.domain.utils.SignatureUtil;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class NoSignNFileAcRequest extends NFileAcRequestAbstract {

  // 过期时间戳
  @NotNull(message = "Ets not null")
  private Long Ets;

  // 业务线标识
  @NotBlank(message = "Bn not empty")
  private String Bn;

  @Override
  public String getBiz() {
    return Bn;
  }

  @Override
  public boolean isVerifyMetaSign() {
    return false;
  }

  public String getDsSignRaw(){
    return SignatureUtil.generatorRaw(getFid(),getAcid(), getEts(), getAk(), getFn(),getBn());
  }

  @Override
  public String toString(){
    StringBuilder builder = superToString(new StringBuilder("NoSignNFileAcRequest{"));
    builder.append(", Ets=").append(Ets);
    builder.append(", Bn='").append(getBiz()).append('\'');
    builder.append('}');
    return builder.toString();
  }

}
