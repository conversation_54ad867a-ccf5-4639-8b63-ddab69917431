package com.fxiaoke.file.server.domain.entity.fieids;

public class NFileMetaFieIds {

  public static final String ea = "EA";

  public static final String employeeId = "SU";

  public static final String nPath = "NBP";

  public static final String tnPath = "TNBP";

  public static final String cPath = "CBP";

  public static final String tcPath = "TCBP";

  public static final String securityGroup = "FSG";

  public static final String name = "FNA";

  public static final String extension = "NEx";

  public static final String size = "Sz";

  public static final String createDate = "Dt";

  public static final String group = "Gp";

  public static final String masterId = "Ma";

  public static final String storageIp = "UEP";

  public static final String trackerGroup = "TGP";

  public static final String bigGroup = "BGp";

  public static final String bigMasterId = "BMa";

  public static final String bigStorageIp = "BUEP";

  public static final String nS2Mp3SubFile = "Cv";

  public static final String nThumbSubFiles = "TINF";

  public static final String nThumbSubFiles_nPath = "TBP";

  public static final String nThumbSubFiles_width = "TW";

  public static final String nThumbSubFiles_height = "TH";

  public static final String imageSubFilePaths = "CL";

  public static final String nSubFiles = "CF";

  public static final String nSubFiles_type = "CT";

  public static final String nSubFiles_index = "CI";

  public static final String nSubFiles_masterId = "CM";

  public static final String nSubFiles_group = "CGP";

  public static final String nSubFiles_storageIp = "CUEP";

  public static final String nChunkSubFiles = "CIF";

  public static final String nPermissionRegexps = "Pm";

  public static final String nPermissionRegexps_allow = "A";

  public static final String nChunkSubFiles_chunkSubFileSize = "CSZ";

  public static final String nChunkSubFiles_lastChunkSubFileSize = "LCS";

  public static final String nChunkSubFiles_chunkSubFileCount = "CC";

  public static final String nChunkSubFiles_nChunkSubFiles = "CKS";

  public static final String nChunkSubFiles_nChunkSubFiles_index = "IDX";

  public static final String nChunkSubFiles_nChunkSubFiles_group = "GP";

  public static final String nChunkSubFiles_nChunkSubFiles_masterId = "MSD";

  public static final String nChunkSubFiles_nChunkSubFiles_size = "SZ";

  public static final String nChunkSubFiles_nChunkSubFiles_createDate = "CD";

  public static final String mp3DerivedPath = "MP3";

  public static final String business = "Biz";

  public static final String sha256Code = "SHA256";

  public static final String md5Code = "CODE";

  public static final String md5SamplingRule = "HR";

  public static final String isTempFile = "IT";

  public static final String disableFile = "DISABLE";

  public static final String isChunkSubFile = "ICF";
  public static final String width = "W";
  public static final String height = "H";

  public static final String s3Type = "OT";
  public static final String bucket = "BU";
  public static final String objectKey = "OBK";

  public static final String mimeType = "MT";

  public static final String expireTime = "ET";

  public static final String dataDoubleWrite = "DW";

  public static final String isTempFile_group_crateDate_index_name = "IT_1_Gp_1_Dt_1";
  public static final String ea_tnPath_index_name = "EA_1_TNBP_1";
  public static final String ea_nPath_index_name = "EA_1_NBP_1";
  public static final String nPath_index_name = "NBP_1";
  public static final String ea_md5Code_isTempFile_index_name = "EA_1_CODE_1_IT_1";
  public static final String expireTime_index_name = "ET_1";
  public static final String ea_bigGroup_bigMasterId_index_name = "EA_1_BGp_1_BMa_1";
  public static final String ea_cPath_index_name = "EA_1_CBP_1";
  public static final String ea_bucket_objectKey_index_name = "EA_1_BU_1_OBK_1";
  public static final String ea_createDate_index_name = "EA_1_Dt_1";
  public static final String ea_expireTime_index_name = "EA_1_ET_1";

  public static final String uploadId = "UID";
}
