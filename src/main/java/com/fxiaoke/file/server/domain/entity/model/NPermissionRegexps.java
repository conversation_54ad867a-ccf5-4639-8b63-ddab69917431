package com.fxiaoke.file.server.domain.entity.model;

import com.fxiaoke.file.server.domain.entity.fieids.NFileMetaFieIds;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Property;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Embedded
public class NPermissionRegexps {
  @Property(NFileMetaFieIds.nPermissionRegexps_allow)
  private String allow;
}
