package com.fxiaoke.file.server.domain.model.imaginary;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * 智能裁剪操作的参数类
 * 基本功能：使用智能算法自动检测图像中最有趣/最重要的区域进行裁剪
 * 裁剪方式：基于 libvips 内置算法 自动分析图像内容
 * 工作原理：算法会分析图像的边缘、对比度、亮度等特征，找出最吸引人的区域
 * 适用场景：当您不确定图像中哪个部分最重要，或者需要自动提取图像的主要内容时
 * 特点：更智能，但计算量较大
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SmartCropParams extends CommonParams {
    /**
     * 图像宽度
     */
    private Integer width;
    
    /**
     * 图像高度
     */
    private Integer height;
    
    /**
     * 是否嵌入图像，默认为false
     */
    private Boolean embed;
    
    /**
     * 应用宽高比，例如：16:9
     */
    private String aspectratio;
} 