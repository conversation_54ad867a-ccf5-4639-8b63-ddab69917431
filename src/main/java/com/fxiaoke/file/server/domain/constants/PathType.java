package com.fxiaoke.file.server.domain.constants;

import lombok.Getter;

@Getter
public enum PathType {

  N_FILE("N_", "企业文件-N类型,基本生成逻辑为：N_yyyyMM_dd_uuid(去除-)"),
  TN_FILE("TN_", "企业文件-临时N类型,基本生成逻辑为：TN_uuid(去除-)"),
  C_FILE("C_", "企业文件-C类型,基本生成逻辑为：C_yyyyMM_dd_uuid(去除-)"),
  TC_FILE("TC_", "企业文件-临时C类型,基本生成逻辑为：TC_uuid(去除-)"),
  A_FILE("A_", "互联文件-A类型,基本生成逻辑为：A_yyyyMM_dd_uuid(去除-)"),
  TA_FILE("TA_", "互联文件-临时A类型,基本生成逻辑为：TA_uuid(去除-)"),
  G_FILE("G_", "全局文件-G类型,基本生成逻辑为：G_yyyyMM_dd_uuid(去除-)"),
  OLD_FILE("20_", "老文件-20类型,基本生成逻辑为：20_yyyyMM_dd_uuid(保留-)"),
  S_FILE("S_", "老文件-S类型,基本生成逻辑为：S_yyyyMM_dd_uuid(去除-),数据库中存储为 path.replace(\"S_\", \"\") 即，实际也是20开头的path"),
  F_FILE("F_", "企业文件-F类型,基本生成逻辑为：F_yyyyMM_dd_uuid(去除-),由于嵌入EA 该PATH 不定长"),
  BIG_FILE("ALIOSS", "大附件-对象存储类型,基本生成逻辑为：ALIOSS_uuid(去除-)"),
  UNKNOWN("", "未知类型");

  private final String prefix;
  private final String description;

  PathType(String prefix, String description) {
    this.prefix = prefix;
    this.description = description;
  }

}
