package com.fxiaoke.file.server.domain.constants;

import lombok.Getter;

@Getter
public enum ErInfo {

  SERVER_INIT_ERROR(500, "Server initialization error", "Server init error"),

  AUTH_EXPIRED_SIGN(400, "Authentication information expired", "Expired sign"),
  AUTH_MESSAGE_LOSS(400, "Request tampered or lost, access denied", "Fake message"),

  AUTH_INVALID_FID(400, "Invalid fid", "Invalid fid"),

  AUTH_INVALID_AK(401, "Invalid authentication information", "Invalid AK"),
  AUTH_INVALID_SIGN(401, "Invalid signature", "Invalid sign"),
  AUTH_INVALID_META_SIGN(401, "Invalid file signature", "Invalid MetaData sign"),
  // 没有认证信息
  AUTH_MISS_USER_INFO(401, "Incomplete user information, access denied", "Missing user information"),
  // 非有效用户Cookie解析失败
  AUTH_FAIL(401, "Authentication failed", "Auth fail"),

  AUTH_USER_INFO_NO_MATCH(403, "User identity does not match, access is denied", "User info not match"),

  ACCESS_FILE_RESOURCE_NOT_SUPPORT(400, "Unsupported file resource type", "Unsupported file resource"),
  ACCESS_EXPIRED_OR_NOT_EXIST_FILE(400, "File expired or does not exist", "The file has expired or does not exist"),
  ACCESS_FILE_CLIENT_EOF(499, "Client canceled download", "Client cancel download"),
  ACCESS_STONE_FILE_FAIL(500, "Failed to download enterprise file, please contact administrator", "Failed to access stone file"),
  ACCESS_CROSS_FILE_FAIL(500, "Failed to download cross file, please contact administrator", "Failed to access cross file"),
  ACCESS_AVATAR_IMAGE_FAIL(500, "Failed to download avatar file, please contact administrator", "Failed to access avatar file"),
  ACCESS_FILE_FAIL_BY_STONE(500, "Failed to download file, please contact administrator", "Failed to access file by STONE"),
  ACCESS_FILE_FAIL_BY_ATS(500, "Failed to download file, please contact administrator", "Failed to access file by ATS"),
  ACCESS_THUMB_IMAGE_FAIL(500, "Failed to retrieve thumbnail, please contact administrator", "Failed to access thumb image"),
  ACCESS_IO_EXCEPTION(500, "File server is busy, please contact administrator", "File server is busy"),

  UPLOAD_MISS_FORM_ITEM(400, "Missing specified file form item facishareFile", "The facishareFile form field is missing"),
  UPLOAD_FILE_IS_EMPTY(400, "File is empty", "The uploaded file is empty"),
  USER_UPLOAD_EOF(499, "User canceled upload", "User upload file EOF"),
  UPLOAD_FILE_SIZE_NOT_MATCH(400, "Upload file size does not match", "The actual file size does not match the expected file size"),
  UPLOAD_FILE_TYPE_NOT_MATCH(400, "Upload type does not match signature type", "The actual file type does not match the signature type"),

  UPLOAD_FILE_TO_STORAGE_FAIL(500, "File server is busy, please contact administrator", "Failed to upload file to storage"),

  REMOTE_SERVICE_EXCEPTION(500, "Service exception,please contact administrator", "Remote service exception"),

  AVATAR_DATABASE_ERROR(500, "Database error", "Avatar Database error"),
  AVATAR_FAST_DFS_ERROR(500, "Storage error", "Avatar FastDFS error"),
  AVATAR_DEFAULT_ERROR(500, "Default avatar error", "Avatar default error"),
  AVATAR_NOT_FOUND(400,"Avatar not found","Avatar Not Found" ),
  AVATAR_FILE_SYSTEM_NOT_EXISTS(503, "Avatar file system does not exist", "Avatar file system not exists"),
  INI_REQUEST_BODY_FAIL(500,"Failed to initialize RequestBody" ,"Init RequestBody fail"),
  IMAGE_GET_INFO_FAIL(500,"Failed to retrieve image information" ,"Get image info fail"),
  IMAGE_CONVERT_TYPE_FAIL(500,"Failed to convert image type","Convert image type fail" ),


  STONE_CORE_META_NOT_FOUND(500, "Enterprise file system database core route is missing",
      "StoneCore table registration information for this business is missing"),
  STONE_CORE_META_TRANSFER_STATUS_ERROR(
      500, "Enterprise file system database core route status error",
      "StoneCore Transfer status error"),
  DATABASE_MANAGER_PATH_TYPE_NOT_SUPPORT(500,
      "DatabaseManager path type not support", "DatabaseManager path type not support"),
  FILE_META_SERVICE_PATH_TYPE_NOT_SUPPORT(
      400,"Non-enterprise file type path access is not supported for the time being",
      "Only types N|TN|C|TC are supported"),
  FILE_META_FILE_NOT_FOUND(400,"File expired or does not exist",
      "The file has expired or does not exist"),
  S3_CONFIG_LOAD_ERROR(500,"S3 Config load Fail","S3 Config load Fail" ),
  FILE_NOT_SUPPORT_GET_METADATA(400,"Enterprises or files do not support obtaining file metadata",
      "Enterprises or files do not support obtaining file metadata" );

  private final int code;
  private final String message;
  private final String reason;
  
  ErInfo(int code, String message, String reason) {
    this.code = code;
    this.message = message;
    this.reason = reason;
  }
}