package com.fxiaoke.file.server.domain.entity.fieids;

public class AvatarFileMetaFieIds {

  // path 在数据库中的字段名
  public static final String path = "BP";
  // 企业账号,该文件所属的企业账号
  public static final String enterpriseAccount = "EA";
  // 用户信息
  public static final String avatarUser = "USER";
  // 用户enterpriseAccount
  public static final String avatarUser_enterpriseAccount= "EA";
  // 用户employeeId
  public static final String avatarUser_employeeId= "EMPID";
  // 用户sessionId(2016年老数据使用，早期采用EA+SESSIONID作为区分是哪个企业哪个用户的唯一标识，后面改为EA+EMPID 此字段废弃)
  public static final String avatarUser_sessionId= "SESSIONID";

  // 文件大小
  public static final String size= "Sz";
  // 文件扩展名 不带点
  public static final String extension = "Ex";

  //----- 用户使用的存储可能是fastDFS，也可能是对象存储 -----
  // 存在用户一开始使用fastDFS，后来使用对象存储的情况，也就是说会存在两种存储方式都存在的情况
  // 优先使用对象存储，如果对象存储不存在，再使用fastDFS

  // fastDFS group
  public static final String fastDFSGroup = "Gp";
  // fastDFS 在 group 中的具体存储位置
  public static final String fastDFSMasterId = "Ma";
  // fastDFS 存储服务器地址
  public static final String fastDFSStorageAddress = "UEP";

  // bucket
  public static final String bucket = "BU";
  // objectKey
  public static final String objectKey = "OBK";

  // 业务类型
  public static final String business = "Biz";
  // 创建时间
  public static final String createDate = "Dt";
  // 是否禁用
  public static final String disable = "DISABLE";
  // 禁用时间
  public static final String disableTimeMillis = "DTS";

}
