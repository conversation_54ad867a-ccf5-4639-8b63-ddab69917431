package com.fxiaoke.file.server.domain.model.api;

import com.fxiaoke.common.Pair;
import com.fxiaoke.file.server.domain.constants.Constant;
import com.fxiaoke.file.server.utils.FileInfoUtil;
import com.fxiaoke.file.server.utils.StrUtils;
import com.fxiaoke.stone.commons.domain.utils.SignatureUtil;
import com.google.common.base.Strings;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class AcFileInfo {

  // 文件path
  private String path;
  // 文件所有者/访问者信息
  private UserInfo userInfo;

  // 文件名
  private String filename;
  // 文件扩展名
  private String extension;

  // 图片宽度
  private int width;
  // 图片高度
  private int height;

  // 文件大小
  private Long size;

  private String biz;

  public void setWH(String whStr) {
    Pair<Integer, Integer> wh = FileInfoUtil.getWH(whStr);
    this.width = wh.first;
    this.height = wh.second;
  }

  public String getMetaSignRaw() {
    return SignatureUtil.generatorRaw(userInfo.getFileOwnerTenantId(), path);
  }

  public boolean isMetaVerifySign() {
    return path.startsWith("N") || path.startsWith("TN");
  }

  public boolean isEnterpriseFile() {
    return isNFile() || isCFile();
  }

  public boolean isNFile() {
    return path.startsWith("N_") || path.startsWith("TN_");
  }

  public boolean isCFile() {
    return path.startsWith("C_") || path.startsWith("TC_");
  }

  public String getExtension(){
    if (StrUtils.notBlank(extension)){
      return "jpg".equalsIgnoreCase(extension)
          ? "jpeg"
          : extension.toLowerCase();
    }
    return extension;
  }

  public boolean isImage() {
    if (Strings.isNullOrEmpty(extension)){
      return false;
    }
    return FileInfoUtil.isSupportImageByExtension(getExtension());
  }

  public boolean isGetOriginal(){
    return this.width == 0 && this.height == 0;
  }

  /**
   * 获取图片分辨率(不考虑图片宽高都为0的情况)
   * @return 图片分辨率
   */
  public int getImageResolution(){
    if (this.width != 0 && this.height != 0){
      return this.width * this.height;
    }
    // 如果宽高的任意一边为0,另一边不为零,则视为获取正方形图,以不为零的边为准
    if (this.width != 0){
      return this.width * this.width;
    }

    return this.height * this.height;
  }

  public Integer getReportEmployeeId(){
    if (userInfo.ownerEqualsAc()){
      return userInfo.getFileAcEmployeeId();
    }
    return Constant.DEFAULT_EM_ID;
  }

}
