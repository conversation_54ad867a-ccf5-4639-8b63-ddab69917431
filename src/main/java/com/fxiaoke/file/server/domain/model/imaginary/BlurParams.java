package com.fxiaoke.file.server.domain.model.imaginary;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * 模糊操作的参数类
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BlurParams extends CommonParams {
    /**
     * 高斯掩码的大小，用于模糊图像
     */
    private Float sigma;
    
    /**
     * 高斯滤波器的最小振幅，默认为0.5
     */
    private Float minampl;
    
    /**
     * 图像宽度
     */
    private Integer width;
    
    /**
     * 图像高度
     */
    private Integer height;
    
    /**
     * 应用宽高比，例如：16:9
     */
    private String aspectratio;
} 