package com.fxiaoke.file.server.domain.entity;

import com.fxiaoke.file.server.domain.entity.fieids.NFileMetaFieIds;
import com.fxiaoke.file.server.domain.entity.model.NChunkSubFiles;
import com.fxiaoke.file.server.domain.entity.model.NPermissionRegexps;
import com.fxiaoke.file.server.domain.entity.model.NS2Mp3SubFile;
import com.fxiaoke.file.server.domain.entity.model.NSubFile;
import com.fxiaoke.file.server.domain.entity.model.NThumbSubFile;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Field;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Index;
import org.mongodb.morphia.annotations.IndexOptions;
import org.mongodb.morphia.annotations.Indexes;
import org.mongodb.morphia.annotations.Property;

/**
 * N、C、F(部分)、20(部分) 文件元数据
 * 注: 与原始定义相比 移除了 biz2、uploadId、doubleWrite、move2BinDate、apiTempFileName
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Entity(value = "N_Files", noClassnameStored = true)
@Indexes({
    @Index(fields = {@Field(NFileMetaFieIds.isTempFile),@Field(NFileMetaFieIds.group),@Field(NFileMetaFieIds.createDate)},
          options = @IndexOptions(name = NFileMetaFieIds.isTempFile_group_crateDate_index_name,background = true)),
    @Index(fields = {@Field(NFileMetaFieIds.ea),@Field(NFileMetaFieIds.tnPath)},
          options = @IndexOptions(name = NFileMetaFieIds.ea_tnPath_index_name,background = true)),
    @Index(fields = {@Field(NFileMetaFieIds.ea),@Field(NFileMetaFieIds.nPath)},
          options = @IndexOptions(name = NFileMetaFieIds.ea_nPath_index_name,background = true)),
    @Index(fields = {@Field(NFileMetaFieIds.nPath)},
          options = @IndexOptions(name = NFileMetaFieIds.nPath_index_name,background = true)),
    @Index(fields = {@Field(NFileMetaFieIds.ea),@Field(NFileMetaFieIds.md5Code),@Field(NFileMetaFieIds.isTempFile)},
          options = @IndexOptions(name = NFileMetaFieIds.ea_md5Code_isTempFile_index_name,background = true)),
    @Index(fields = {@Field(NFileMetaFieIds.expireTime)},
          options = @IndexOptions(name = NFileMetaFieIds.expireTime_index_name,background = true)),
    @Index(fields = {@Field(NFileMetaFieIds.ea),@Field(NFileMetaFieIds.bigGroup),@Field(NFileMetaFieIds.bigMasterId)},
          options = @IndexOptions(name = NFileMetaFieIds.ea_bigGroup_bigMasterId_index_name,background = true)),
    @Index(fields = {@Field(NFileMetaFieIds.ea),@Field(NFileMetaFieIds.cPath)},
          options = @IndexOptions(name = NFileMetaFieIds.ea_cPath_index_name,background = true)),
    @Index(fields = {@Field(NFileMetaFieIds.ea),@Field(NFileMetaFieIds.bucket),@Field(NFileMetaFieIds.objectKey)},
          options = @IndexOptions(name = NFileMetaFieIds.ea_bucket_objectKey_index_name,background = true)),
    @Index(fields = {@Field(NFileMetaFieIds.ea),@Field(NFileMetaFieIds.createDate)},
          options = @IndexOptions(name = NFileMetaFieIds.ea_createDate_index_name,background = true)),
    @Index(fields = {@Field(NFileMetaFieIds.ea),@Field(NFileMetaFieIds.expireTime)},
          options = @IndexOptions(name = NFileMetaFieIds.ea_expireTime_index_name,background = true))
})
public class NFileMeta {

  @Id
  private ObjectId _id;

  @Property(NFileMetaFieIds.ea)
  private String ea;

  // 存储形式绝大部分为E.employeeId,及少部分为(B.UUID 去-)
  @Property(NFileMetaFieIds.employeeId)
  private String employeeId;

  @Property(NFileMetaFieIds.nPath)
  private String nPath;

  @Property(NFileMetaFieIds.tnPath)
  private String tnPath;

  @Property(NFileMetaFieIds.cPath)
  private String cPath;

  @Property(NFileMetaFieIds.tcPath)
  private String tcPath;

  @Property(NFileMetaFieIds.securityGroup)
  private String securityGroup;

  @Embedded(NFileMetaFieIds.nPermissionRegexps)
  private List<NPermissionRegexps> nPermissionRegexps;

  @Property(NFileMetaFieIds.extension)
  private String extension;

  @Property(NFileMetaFieIds.name)
  private String name;

  @Property(NFileMetaFieIds.size)
  private long size;

  @Property(NFileMetaFieIds.createDate)
  private Date createDate;

  // FastDFS 存储信息x

  // 早期 FastDFS 存储信息
  @Property(NFileMetaFieIds.group)
  private String group;

  @Property(NFileMetaFieIds.masterId)
  private String masterId;

  @Property(NFileMetaFieIds.storageIp)
  private String storageIp;

  // 当前正在使用的 FastDFS 存储信息
  @Property(NFileMetaFieIds.trackerGroup)
  private String trackerGroup;

  @Property(NFileMetaFieIds.bigGroup)
  private String bigGroup;

  @Property(NFileMetaFieIds.bigMasterId)
  private String bigMasterId;

  @Property(NFileMetaFieIds.bigStorageIp)
  private String bigStorageIp;

  // S3 存储信息
  @Property(NFileMetaFieIds.s3Type)
  private String s3Type;

  @Property(NFileMetaFieIds.bucket)
  private String bucket;

  @Property(NFileMetaFieIds.objectKey)
  private String objectKey;

  // MP3 转码子文件（现已变更为实时转码）
  @Embedded(NFileMetaFieIds.nS2Mp3SubFile)
  private NS2Mp3SubFile nS2Mp3SubFile;

  // 缩略图子文件(企信中存在较多使用,目前已转向使用实时缩略）
  @Embedded(NFileMetaFieIds.nThumbSubFiles)
  private List<NThumbSubFile> nThumbSubFiles;

  // AMR转码MP3从文件 (现已变更为实时转码)
  @Property(NFileMetaFieIds.mp3DerivedPath)
  private String mp3DerivedPath;

  // 图片从文件路径（F|N|TN 类型PATH 均有使用）
  @Property(NFileMetaFieIds.imageSubFilePaths)
  private List<String> imageSubFilePaths;

  // 缩略图子文件 (早期生成缩略图的逻辑)
  @Property(NFileMetaFieIds.nSubFiles)
  private List<NSubFile> nSubFiles;

  // 分片文件信息 （分片上传逻辑，FastDFS会保留详细分片信息用于在下载时合并,对象存储不会保留,详细信息存留在另外的表中）
  @Embedded(NFileMetaFieIds.nChunkSubFiles)
  private NChunkSubFiles nChunkSubFiles;

  // 业务线标识（目前允许为null,和存在错误传递)
  @Property(NFileMetaFieIds.business)
  private String business;

  // 完整文件的SHA256 (未知使用目的）
  @Property(NFileMetaFieIds.sha256Code)
  private String sha256Code;

  // 文件的MD5 (用于分片上传时秒传文件)
  @Property(NFileMetaFieIds.md5Code)
  private String md5Code;

  // 计算文件的MD5的采样规则（前后端约定一致,存在多个版本算法）
  @Property(NFileMetaFieIds.md5SamplingRule)
  private String md5SamplingRule;

  // 是否是临时文件（临时文件会在一定时间后自动删除）
  @Property(NFileMetaFieIds.isTempFile)
  private boolean isTempFile;

  // 是否禁用文件（禁用文件无法访问,业务无法查找到此文件的MetaInfo）
  @Property(NFileMetaFieIds.disableFile)
  private boolean disableFile;

  // 是否是分片文件（分片文件会在下载时合并）
  @Property(NFileMetaFieIds.isChunkSubFile)
  private boolean isChunkSubFile;

  // 图片宽度
  @Property(NFileMetaFieIds.width)
  private int width;

  // 图片高度
  @Property(NFileMetaFieIds.height)
  private int height;

  @Property(NFileMetaFieIds.mimeType)
  private String mimeType;
  // 过期时间
  @Property(NFileMetaFieIds.expireTime)
  private Date expireTime;

  // 元数据是否双写（新老MongoDB）
  @Property(NFileMetaFieIds.dataDoubleWrite)
  private boolean metaDoubleWrite;

  // 上传ID（用于分片上传时标识上传任务）
  @Property(NFileMetaFieIds.uploadId)
  private String uploadId;
}
