package com.fxiaoke.file.server.domain.model.imaginary;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * 水印操作的参数类
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WatermarkParams extends CommonParams {
    /**
     * 水印文本内容
     */
    private String text;
    
    /**
     * 文本区域边距
     */
    private Integer margin;
    
    /**
     * 水印DPI值
     */
    private Integer dpi;
    
    /**
     * 水印文本区域宽度
     */
    private Integer textwidth;
    
    /**
     * 水印文本或水印图像的不透明度，默认为0.2
     */
    private Float opacity;
    
    /**
     * 是否禁用水印文本复制，默认为false
     */
    private Boolean noreplicate;
    
    /**
     * 水印文本字体类型和格式，例如：sans bold 12
     */
    private String font;
    
    /**
     * 水印文本RGB十进制基色，例如：255,200,150
     */
    private String color;
} 