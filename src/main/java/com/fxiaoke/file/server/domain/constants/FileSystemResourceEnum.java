package com.fxiaoke.file.server.domain.constants;

import com.fxiaoke.file.server.domain.exception.FileServerException;
import lombok.Getter;

@Getter
public enum FileSystemResourceEnum {
  N("N"),
  TN("TN"),
  C("C"),
  TC("TC"),
  A("A"),
  TA("TA"),
  G("G");
  private final String resource;
  FileSystemResourceEnum(String resources) {
    this.resource = resources;
  }
  public static FileSystemResourceEnum of(String resources) {
    for (FileSystemResourceEnum value : FileSystemResourceEnum.values()) {
      if (resources.startsWith(value.resource)) {
        return value;
      }
    }
    throw new FileServerException("FileSystemResourceEnum",ErInfo.ACCESS_FILE_RESOURCE_NOT_SUPPORT,resources);
  }
}
