package com.fxiaoke.file.server.domain.model.api.request;

import com.fxiaoke.stone.commons.domain.utils.SignatureUtil;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString
@EqualsAndHashCode
public class CFileAcRequestAbstract implements CFileAcRequest {
  @NotBlank(message = "Cid not empty")
  private String Cid;
  @NotBlank(message = "Acid not empty")
  private String Acid;
  @NotBlank(message = "Ext not empty")
  private String Ext;
  @NotBlank(message = "Bn not empty")
  private String Bn;
  @NotBlank(message = "Ds not empty")
  private String Ds;
  @Pattern(regexp = "inline|attachment", message = "Invalid acModel type. Must be 'inline' or 'attachment'")
  private String acModel="inline";
  private String size="0*0";
  private String traceId;

  public String getDsSignRaw() {
    return SignatureUtil.generatorRaw(Cid, Acid, Ext, Bn);
  }

  public String getFn(){
    return Cid.concat(".").concat(Ext);
  }

  @Override
  public String getBiz() {
    return Bn;
  }
}
