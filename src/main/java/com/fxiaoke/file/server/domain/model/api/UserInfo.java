package com.fxiaoke.file.server.domain.model.api;

import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class UserInfo {
  // 文件所有者企业账号
  private String fileOwnerEnterpriseAccount;
  // 文件所有者企业id
  private Integer fileOwnerTenantId;

  // 文件访问者企业账号
  private String fileAcEnterpriseAccount;
  // 文件访问者企业id
  private Integer fileAcTenantId;
  // 文件访问者员工id
  private Integer fileAcEmployeeId;

  // 文件访问者外部企业账号
  private Long fileAcOutTenantId;
  // 文件访问者外部员工id
  private Long fileAcOutEmployeeId;

  public boolean ownerEqualsAc(){
    return Objects.equals(fileOwnerTenantId, fileAcTenantId);
  }

}
