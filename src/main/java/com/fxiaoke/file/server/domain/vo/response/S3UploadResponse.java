package com.fxiaoke.file.server.domain.vo.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * creator: liuys
 * CreateTime: 2025-05-23
 * Description: 对象存储上传响应定义类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class S3UploadResponse {
  private String objectKey;
  private String uploadId;
}
