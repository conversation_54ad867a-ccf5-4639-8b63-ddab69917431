package com.fxiaoke.file.server.domain.model.api;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class UpFileInfo {

  String ea;
  Integer employeeId;

  String resourceType;
  String fileName;
  String extension;
  Integer fileSize;

  public static UpFileInfo of(String ea, Integer employeeId,
      String resourceType, String fileName,String extension, Integer fileSize) {
    return new UpFileInfo(ea, employeeId, resourceType, fileName,extension, fileSize);
  }

}
