package com.fxiaoke.file.server.domain.model.imaginary;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * 裁剪操作的参数类
 * 基本功能：按照指定的宽度和高度裁剪图像，保持图像比例
 * 裁剪方式：从图像中心进行裁剪（默认行为）
 * 控制方式：通过 gravity 参数可以控制裁剪的方向（north、south、centre、west、east）
 * 适用场景：当您知道需要从图像的哪个位置裁剪时使用
 * 特点：简单直接，计算量较小
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CropParams extends CommonParams {
    /**
     * 图像宽度
     */
    private Integer width;
    
    /**
     * 图像高度
     */
    private Integer height;
    
    /**
     * 是否嵌入图像，默认为false
     * 当 embed=true 时，如果指定的裁剪区域超出原始图像的边界，系统会扩展图像边界而不是裁剪掉超出部分
     * 当 embed=false 时（默认值），系统会严格按指定的尺寸裁剪，不会扩展图像
     */
    private Boolean embed;
    
    /**
     * 定义裁剪操作的起点,可选值：north, south, centre, west, east 默认为centre
     * north表示从顶部开始裁剪
     * south表示从底部开始裁剪
     * centre表示从中心开始裁剪
     * west表示从左侧开始裁剪
     * east表示从右侧开始裁剪
     */
    private String gravity;
    
    /**
     * 图像宽高比,例如：16:9,4:3,1:1
     * 当指定 width 和 aspectratio 时：height = width * (比例分母/比例分子)
     * 当指定 height 和 aspectratio 时：width = height * (比例分子/比例分母)
     * 当同时指定 width、height 和 aspectratio 时：保留 width，重新计算 height
     */
    private String aspectratio;
} 