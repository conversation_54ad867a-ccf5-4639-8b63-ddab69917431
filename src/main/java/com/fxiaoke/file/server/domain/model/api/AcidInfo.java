package com.fxiaoke.file.server.domain.model.api;

import com.fxiaoke.file.server.domain.constants.Constant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class AcidInfo {
  // 文件访问者企业id
  private Integer fileAcTenantId;
  // 文件访问者员工id
  private Integer fileAcEmployeeId;

  // 文件访问者外部企业账号
  private Long fileAcOutTenantId;
  // 文件访问者外部员工id
  private Long fileAcOutEmployeeId;
  
  public static AcidInfo of(String acid){
    AcidInfo acidInfo = new AcidInfo();
    String[] acidChainInfo = acid.split("\\.");
    if (acidChainInfo.length==1){
      acidInfo.setFileAcTenantId(Integer.valueOf(acidChainInfo[0]));
      acidInfo.setFileAcEmployeeId(Constant.DEFAULT_DOWN_STREAM_EM_ID);
      return acidInfo;
    }
    acidInfo.setFileAcTenantId(Integer.valueOf(acidChainInfo[0]));
    acidInfo.setFileAcEmployeeId(Integer.valueOf(acidChainInfo[1]));
    if(acidChainInfo.length==4){
      acidInfo.setFileAcOutTenantId(Long.valueOf(acidChainInfo[2]));
      acidInfo.setFileAcOutEmployeeId(Long.valueOf(acidChainInfo[3]));
    }
    return acidInfo;
  }
}
