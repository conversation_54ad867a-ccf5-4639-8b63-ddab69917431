package com.fxiaoke.file.server.domain.vo.request;

import com.fxiaoke.file.server.domain.model.api.CookieInfo;
import com.fxiaoke.file.server.domain.model.api.request.StsCredential;
import lombok.*;

/**
 * creator: liuys
 * CreateTime: 2025-06-04
 * Description:
 */
@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class AbortUploadRequest {
  String filePath;
  String expireTime;
  StsCredential credential;
  CookieInfo cookieInfo;
}
