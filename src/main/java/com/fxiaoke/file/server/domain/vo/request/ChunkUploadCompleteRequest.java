package com.fxiaoke.file.server.domain.vo.request;

import com.fxiaoke.file.server.domain.model.api.CookieInfo;
import com.fxiaoke.file.server.domain.model.api.request.StsCredential;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.Comparator;

@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class ChunkUploadCompleteRequest {
    String path;
    Long fileSize;
    String fileExt;
    List<PartInfo> partInfos;
    String expireTime;
    String callback;
    String uploadId;
    StsCredential credential;
    CookieInfo cookieInfo;

    public List<PartInfo> getPartInfos() {
        if (partInfos != null) {
            partInfos.sort(Comparator.comparingInt(PartInfo::getPartNum));
        }
        return partInfos;
    }
}
