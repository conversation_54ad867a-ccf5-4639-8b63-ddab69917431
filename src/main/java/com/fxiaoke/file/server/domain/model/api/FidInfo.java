package com.fxiaoke.file.server.domain.model.api;

import com.fxiaoke.file.server.domain.constants.ErInfo;
import com.fxiaoke.file.server.domain.exception.FileServerException;
import com.fxiaoke.stone.commons.domain.constant.Constants;
import com.fxiaoke.stone.commons.help.SecureCryptHelp;
import com.google.common.base.Splitter;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.io.FilenameUtils;

@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class FidInfo {
  Integer fileOwnerTenantId;
  String signature;
  String accessKey;
  String path;

  public static FidInfo of(String fid){
    String plainText = SecureCryptHelp.decry(fid);
    List<String> fidPlainTextInfo = Splitter.on(Constants.DELIMITER).splitToList(plainText);
    if (fidPlainTextInfo.size()==4) {
      return ofSignFid(fidPlainTextInfo);
    }
    return ofNoSignFid(fidPlainTextInfo);
  }

  private static FidInfo ofSignFid(List<String> fidPlainTextInfo) {
    FidInfo fidInfo = new FidInfo();
    try {
      Integer fileOwnerTenantId = Integer.valueOf(fidPlainTextInfo.getFirst());
      fidInfo.setFileOwnerTenantId(fileOwnerTenantId);
    } catch (Exception e) {
      throw new FileServerException(e, "FidInfo.ofSignFid", ErInfo.AUTH_INVALID_FID,
          fidPlainTextInfo);
    }
    fidInfo.setSignature(fidPlainTextInfo.get(1));
    fidInfo.setAccessKey(fidPlainTextInfo.get(2));
    fidInfo.setPath(FilenameUtils.getBaseName(fidPlainTextInfo.get(3)));
    return fidInfo;
  }

  private static FidInfo ofNoSignFid(List<String> fidPlainTextInfo) {
    FidInfo fidInfo = new FidInfo();
    try {
      Integer fileOwnerTenantId = Integer.valueOf(fidPlainTextInfo.getFirst());
      fidInfo.setFileOwnerTenantId(fileOwnerTenantId);
    } catch (Exception e) {
      throw new FileServerException(e, "FidInfo.ofNoSignFid", ErInfo.AUTH_INVALID_FID,
          fidPlainTextInfo);
    }
    fidInfo.setPath(FilenameUtils.getBaseName(fidPlainTextInfo.get(1)));
    return fidInfo;
  }
}
