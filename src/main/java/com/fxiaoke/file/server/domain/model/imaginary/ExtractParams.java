package com.fxiaoke.file.server.domain.model.imaginary;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

/**
 * 提取操作的参数类
 */
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ExtractParams extends CommonParams {
    /**
     * 要提取的区域的顶部边缘
     */
    private Integer top;
    
    /**
     * 要提取的区域的左侧边缘
     */
    private Integer left;
    
    /**
     * 要提取的区域宽度
     */
    private Integer areawidth;
    
    /**
     * 要提取的区域高度
     */
    private Integer areaheight;
    
    /**
     * 图像宽度
     */
    private Integer width;
    
    /**
     * 图像高度
     */
    private Integer height;
    
    /**
     * 是否嵌入图像，默认为false
     */
    private Boolean embed;
    
    /**
     * 应用宽高比，例如：16:9
     */
    private String aspectratio;
} 