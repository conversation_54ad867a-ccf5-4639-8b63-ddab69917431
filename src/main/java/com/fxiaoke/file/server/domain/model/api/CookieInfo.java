package com.fxiaoke.file.server.domain.model.api;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CookieInfo {

  private String fsAuthXC;
  private String erInfo;


  public static CookieInfo empty() {
    return new CookieInfo();
  }

  public static CookieInfo ofFsAuthXC(String fsAuthXC) {
    return new CookieInfo(fsAuthXC, null);
  }

  public static CookieInfo ofErInfo(String erInfo) {
    return new CookieInfo(null, erInfo);
  }

}