package com.fxiaoke.file.server.domain.entity;

import com.fxiaoke.file.server.domain.entity.fieids.AvatarFileMetaFieIds;
import com.fxiaoke.file.server.domain.entity.model.AvatarUser;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Field;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Index;
import org.mongodb.morphia.annotations.IndexOptions;
import org.mongodb.morphia.annotations.Indexes;
import org.mongodb.morphia.annotations.Property;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Entity(value = "FileMetaData", noClassnameStored = true)
@Indexes({
    @Index(fields = {@Field(AvatarFileMetaFieIds.path)}, options = @IndexOptions(name = "BP_1",background = true)),
    // 为enterpriseAccount+path的查询创建索引
    // @Index(fields = {@Field(AvatarFileMetaFieIds.path),@Field(AvatarFileMetaFieIds.enterpriseAccount)}, options = @IndexOptions(name = "BP_EA_1")),
})
public class AvatarFileMeta {
  @Id
  private ObjectId id;

  @Property(AvatarFileMetaFieIds.path)
  private String path;
  @Property(AvatarFileMetaFieIds.enterpriseAccount)
  private String enterpriseAccount;
  @Embedded(AvatarFileMetaFieIds.avatarUser)
  private AvatarUser avatarUser;

  @Property(AvatarFileMetaFieIds.size)
  private long size;
  @Property(AvatarFileMetaFieIds.extension)
  private String extension;

  @Property(AvatarFileMetaFieIds.fastDFSGroup)
  private String fastDFSGroup;
  @Property(AvatarFileMetaFieIds.fastDFSMasterId)
  private String fastDFSMasterId;
  @Property(AvatarFileMetaFieIds.fastDFSStorageAddress)
  private String fastDFSStorageAddress;

  @Property(AvatarFileMetaFieIds.bucket)
  private String bucket;
  @Property(AvatarFileMetaFieIds.objectKey)
  private String objectKey;

  @Property(AvatarFileMetaFieIds.business)
  private String business;
  @Property(AvatarFileMetaFieIds.createDate)
  private Date createDate;

  @Property(AvatarFileMetaFieIds.disable)
  private boolean disable;
  @Property(AvatarFileMetaFieIds.disableTimeMillis)
  private long disableTimeMillis;

  public String getFileId(){
    return this.fastDFSGroup+ "/" + this.fastDFSMasterId;
  }
}
