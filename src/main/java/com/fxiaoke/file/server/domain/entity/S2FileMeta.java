package com.fxiaoke.file.server.domain.entity;


import com.fxiaoke.file.server.domain.entity.fieids.NS2FileMetaFieIds;
import com.fxiaoke.file.server.domain.entity.model.NS2Mp3SubFile;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Field;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Index;
import org.mongodb.morphia.annotations.IndexOptions;
import org.mongodb.morphia.annotations.Indexes;
import org.mongodb.morphia.annotations.Property;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Entity(value = "D_UploadFile", noClassnameStored = true)
@Indexes({
    @Index(fields = {@Field(NS2FileMetaFieIds.ea), @Field(NS2FileMetaFieIds.path)}, options = @IndexOptions(name = "EA_1_Path_1"))
})
public class S2FileMeta {
  @Id
  private ObjectId _id;
  @Property(NS2FileMetaFieIds.ea)
  private String ea;
  @Property(NS2FileMetaFieIds.path)
  private String path;
  @Property(NS2FileMetaFieIds.size)
  private long size;
  @Property(NS2FileMetaFieIds.date)
  private Date date;
  @Property(NS2FileMetaFieIds.group)
  private String group;
  @Property(NS2FileMetaFieIds.masterId)
  private String masterId;
  @Property(NS2FileMetaFieIds.storageIp)
  private String storageIp;
  @Embedded(NS2FileMetaFieIds.nS2Mp3SubFile)
  private NS2Mp3SubFile nS2Mp3SubFile;
}
