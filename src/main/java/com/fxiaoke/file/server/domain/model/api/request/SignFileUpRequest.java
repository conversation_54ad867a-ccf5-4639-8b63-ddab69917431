package com.fxiaoke.file.server.domain.model.api.request;

import com.fxiaoke.stone.commons.domain.utils.FilenameUtil;
import com.fxiaoke.stone.commons.domain.utils.SignatureUtil;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class SignFileUpRequest {

  @NotBlank(message = "acid not empty")
  private String acid;

  @NotBlank(message = "resource not empty")
  private String resource;

  @NotBlank(message = "ak not empty")
  private String ak;

  @NotBlank(message = "sign not empty")
  private String sign;

  @NotNull(message = "expiry not null")
  private Long expiry;

  @NotBlank(message = "filename not empty")
  private String fileName;

  @Max(value = 104857600, message = "size exceeds the maximum limit")
  @NotNull(message = "size not null")
  private Integer size;

  @NotBlank(message = "digest not empty")
  private String digest;

  @NotBlank(message = "User-Agent not empty")
  private String userAgent;

  public String getExtension() {
    return FilenameUtil.getExtension(this.getFileName());
  }

  public String getMsgRaw() {
    return SignatureUtil.generatorRaw(this.acid, this.expiry, this.resource, this.size, this.sign,
        this.ak, this.fileName);
  }

  public String getSignRaw() {
    return SignatureUtil.generatorRaw(this.acid, this.expiry, this.resource, this.size);
  }

}
