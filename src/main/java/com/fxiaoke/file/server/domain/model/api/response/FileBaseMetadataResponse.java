package com.fxiaoke.file.server.domain.model.api.response;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class FileBaseMetadataResponse {

  /**
   * 企业账号
   */
  private String ea;

  /**
   * 文件名称
   */
  private String name;

  /**
   * 文件类型扩展名
   */
  private String extension;
  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 文件ID
   */
  private String path;

  /**
   * 对象的唯一标识符(Entity Tag)
   */
  private String eTag;

  /**
   * 对象内容大小(字节)
   */
  private long contentLength;

  /**
   * 对象的MIME类型
   */
  private String contentType;
}
