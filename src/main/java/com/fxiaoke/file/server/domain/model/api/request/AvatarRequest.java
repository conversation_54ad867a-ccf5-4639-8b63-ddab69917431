package com.fxiaoke.file.server.domain.model.api.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class AvatarRequest {
  @NotBlank(message = "Fid not empty")
  String Fid;
  @NotBlank(message = "Acid not empty")
  String Acid;
  @NotBlank(message = "Fn not empty")
  String Fn;
  @NotBlank(message = "Ds not empty")
  String Ds;
  @Pattern(regexp = "inline|attachment", message = "Invalid acModel type. Must be 'inline' or 'attachment'")
  String acModel="inline";
  String size="0*0";
  String traceId;
}
