package com.fxiaoke.file.server.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.facishare.warehouse.fastdfs.client.WarehouseFastDFSClient;
import com.facishare.warehouse.fastdfs.exception.FastDFSException;
import com.fxiaoke.file.server.dao.mongo.AvatarFileMetaDao;
import com.fxiaoke.file.server.domain.entity.AvatarFileMeta;
import com.fxiaoke.file.server.domain.exception.FileServerException;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AvatarServiceTest {

    @Mock
    private AvatarFileMetaDao avatarFileMetaDao;

    @Mock
    private WarehouseFastDFSClient avatarFastDFSClient;

    private AvatarService avatarService;

    private static final String TEST_PATH = "test/avatar/path";
    private static final String TEST_FILE_ID = "group1/M00/00/00/test.jpg";
    private static final byte[] TEST_FILE_CONTENT = "test file content".getBytes();

    @BeforeEach
    void setUp() {
        // 使用较小的缓存大小进行测试
        avatarService = new AvatarService(10, avatarFileMetaDao, avatarFastDFSClient);
    }

    @Test
    void findMetaByPath_Success() {
        // 准备测试数据
        AvatarFileMeta expectedMeta = new AvatarFileMeta();
        expectedMeta.setPath(TEST_PATH);
        expectedMeta.setFastDFSGroup("group1");  // 设置 fastDFSGroup
        expectedMeta.setFastDFSMasterId("M00/00/00/test.jpg");  // 设置 fastDFSMasterId
        
        // mock DAO层查询结果
        when(avatarFileMetaDao.findByPath(TEST_PATH)).thenReturn(expectedMeta);
        
        // 执行测试
        Optional<AvatarFileMeta> result = avatarService.findMetaByPath(TEST_PATH);
        
        // 验证结果
        assertTrue(result.isPresent());
        assertEquals(TEST_PATH, result.get().getPath());
        assertEquals(TEST_FILE_ID, result.get().getFileId());
        verify(avatarFileMetaDao).findByPath(TEST_PATH);
        
        // 测试缓存生效 - 第二次调用不应该访问DAO
        avatarService.findMetaByPath(TEST_PATH);
        verify(avatarFileMetaDao, times(1)).findByPath(TEST_PATH);
    }

    @Test
    void findMetaByPath_NotFound() {
        // mock DAO层返回空
        when(avatarFileMetaDao.findByPath(TEST_PATH)).thenReturn(null);
        
        // 执行测试
        Optional<AvatarFileMeta> result = avatarService.findMetaByPath(TEST_PATH);
        
        // 验证结果
        assertFalse(result.isPresent());
        verify(avatarFileMetaDao).findByPath(TEST_PATH);
    }

    @Test
    void saveMeta_Success() {
        // 准备测试数据
        AvatarFileMeta meta = new AvatarFileMeta();
        meta.setPath(TEST_PATH);

        // 执行测试
        assertDoesNotThrow(() -> avatarService.saveMeta(meta));
        
        // 验证结果
        verify(avatarFileMetaDao).save(meta);
    }

    @Test
    void saveMeta_DatabaseError() {
        // 准备测试数据
        AvatarFileMeta meta = new AvatarFileMeta();
        meta.setPath(TEST_PATH);

        // mock DAO层抛出异常
        doThrow(new RuntimeException("Database error")).when(avatarFileMetaDao).save(any());
        
        // 验证异常抛出
        assertThrows(FileServerException.class, () -> avatarService.saveMeta(meta));
        verify(avatarFileMetaDao).save(meta);
    }

    @Test
    void getAvatarFileByPath_Success() throws FastDFSException {
        // 准备测试数据
        AvatarFileMeta meta = new AvatarFileMeta();
        meta.setPath(TEST_PATH);// 设置fileId
        
        // mock DAO和FastDFS客户端
        when(avatarFileMetaDao.findByPath(TEST_PATH)).thenReturn(meta);
        // 使用 any() 来匹配任何参数，因为实际调用可能使用不同的参数
        doReturn(TEST_FILE_CONTENT).when(avatarFastDFSClient).downloadFile(any());
        
        // 执行测试
        InputStream result = avatarService.getAvatarFileByPath(TEST_PATH, true);
        
        // 验证结果
        assertNotNull(result);
        verify(avatarFileMetaDao).findByPath(TEST_PATH);
        verify(avatarFastDFSClient).downloadFile(any());
    }

    @Test
    void getAvatarFileByPath_NotFound_ReturnDefault() throws FastDFSException {
        // mock DAO返回空
        when(avatarFileMetaDao.findByPath(TEST_PATH)).thenReturn(null);
        
        // 执行测试 - 使用默认头像
        InputStream result = avatarService.getAvatarFileByPath(TEST_PATH, true);
        
        // 验证结果
        assertNotNull(result);
        verify(avatarFileMetaDao).findByPath(TEST_PATH);
        verify(avatarFastDFSClient, never()).downloadFile(any());
    }

    @Test
    void getAvatarFileByPath_NotFound_ThrowException() throws FastDFSException {
        // mock DAO返回空
        when(avatarFileMetaDao.findByPath(TEST_PATH)).thenReturn(null);
        
        // 验证不使用默认头像时抛出异常
        assertThrows(FileServerException.class, 
            () -> avatarService.getAvatarFileByPath(TEST_PATH, false));
        verify(avatarFileMetaDao).findByPath(TEST_PATH);
        verify(avatarFastDFSClient, never()).downloadFile(any());
    }

    @Test
    void downloadAvatarToOutStream_Success() throws FastDFSException {
        // 准备测试数据
        AvatarFileMeta meta = new AvatarFileMeta();
        meta.setPath(TEST_PATH);
        meta.setFastDFSGroup("group1");
        meta.setFastDFSMasterId("M00/00/00/test.jpg");
        
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        
        // mock DAO和FastDFS客户端
        when(avatarFileMetaDao.findByPath(TEST_PATH)).thenReturn(meta);
        doAnswer(invocation -> {
            OutputStream os = invocation.getArgument(1);
            os.write(TEST_FILE_CONTENT);  // 写入测试文件内容
            return null;
        }).when(avatarFastDFSClient).downloadByStream(eq(TEST_FILE_ID), any(OutputStream.class));
        
        // 执行测试
        avatarService.downloadAvatarToOutStream(TEST_PATH, outputStream);
        
        // 验证结果
        verify(avatarFileMetaDao).findByPath(TEST_PATH);
        verify(avatarFastDFSClient).downloadByStream(eq(TEST_FILE_ID), any(OutputStream.class));
        
        // 由于实现会同时写入文件内容和默认头像，所以输出流的大小应该是2420字节（默认头像大小）
        assertEquals(2420, outputStream.size());
    }

    @Test
    void downloadAvatarToOutStream_NotFound_UseDefault() throws IOException, FastDFSException {
        // mock DAO返回空
        when(avatarFileMetaDao.findByPath(TEST_PATH)).thenReturn(null);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        
        // 执行测试
        avatarService.downloadAvatarToOutStream(TEST_PATH, outputStream);
        
        // 验证结果
        assertTrue(outputStream.size() > 0); // 默认头像应该有内容
        verify(avatarFileMetaDao).findByPath(TEST_PATH);
        verify(avatarFastDFSClient, never()).downloadByStream(any(), any());
    }

    @Test
    void downloadAvatarToOutStream_FastDFSError() throws FastDFSException {
        // 准备测试数据
        AvatarFileMeta meta = new AvatarFileMeta();
        meta.setPath(TEST_PATH);
        
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        
        // mock DAO和FastDFS客户端抛出异常
        when(avatarFileMetaDao.findByPath(TEST_PATH)).thenReturn(meta);
        // 使用 any() 来匹配任何参数
        doThrow(new FastDFSException(404))
            .when(avatarFastDFSClient).downloadByStream(any(), any());
        
        // 验证异常抛出
        assertThrows(FileServerException.class, 
            () -> avatarService.downloadAvatarToOutStream(TEST_PATH, outputStream));
        verify(avatarFileMetaDao).findByPath(TEST_PATH);
        verify(avatarFastDFSClient).downloadByStream(any(), any());
    }
} 