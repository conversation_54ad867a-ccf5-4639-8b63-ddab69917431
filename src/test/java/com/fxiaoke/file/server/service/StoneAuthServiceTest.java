package com.fxiaoke.file.server.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.fxiaoke.file.server.domain.constants.ErInfo;
import com.fxiaoke.file.server.domain.exception.FileServerException;
import com.fxiaoke.stone.commons.StoneAuthClient;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class StoneAuthServiceTest {

    @Mock
    private StoneAuthClient stoneAuthClient;

    @InjectMocks
    private StoneAuthService stoneAuthService;

    @Test
    void getSkByAccessKey_Success() {
        // 准备测试数据
        String accessKey = "WH07ZwPp6ng2sXy7uwthKJz3";
        String expectedSk = "CddM1ML7COaL9WW6pTopvBEA9cEsgO";
        
        // mock StoneAuthClient
        when(stoneAuthClient.getSkByAccessKey(accessKey)).thenReturn(expectedSk);
        
        // 执行测试
        String result = stoneAuthService.getSkByAccessKey(accessKey);
        
        // 验证结果
        assertEquals(expectedSk, result);
        verify(stoneAuthClient).getSkByAccessKey(accessKey);
    }

    @Test
    void getSkByAccessKey_ThrowsException() {
        // 准备测试数据
        String invalidAccessKey = "invalid_access_key";
        
        // mock StoneAuthClient抛出异常
        when(stoneAuthClient.getSkByAccessKey(invalidAccessKey)).thenThrow(new RuntimeException("Failed to get SK"));
        
        // 验证抛出预期的异常
        FileServerException exception = assertThrows(FileServerException.class, 
            () -> stoneAuthService.getSkByAccessKey(invalidAccessKey));
        
        // 验证异常信息
        assertEquals(ErInfo.AUTH_INVALID_AK.getCode(), exception.getCode());
        assertEquals("Invalid authentication information", exception.getMessage());
        verify(stoneAuthClient).getSkByAccessKey(invalidAccessKey);
    }
} 