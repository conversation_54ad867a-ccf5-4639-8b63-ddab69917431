package com.fxiaoke.file.server.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

import com.fxiaoke.file.server.domain.constants.ErInfo;
import com.fxiaoke.file.server.domain.exception.FileServerException;
import com.fxiaoke.file.server.domain.model.api.AcidInfo;
import com.fxiaoke.file.server.domain.model.api.AuthInfo;
import com.fxiaoke.file.server.domain.model.api.FidInfo;
import com.fxiaoke.file.server.domain.model.api.request.AvatarRequest;
import com.fxiaoke.file.server.domain.model.api.request.NoSignCFileAcRequest;
import com.fxiaoke.file.server.domain.model.api.request.SignFileUpRequest;
import com.fxiaoke.file.server.domain.model.api.request.SignNFileAcRequest;
import com.fxiaoke.stone.commons.domain.utils.SignatureUtil;
import java.io.InputStream;
import java.io.OutputStream;
import java.time.Instant;
import java.util.Optional;
import lombok.Cleanup;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class FileServiceTest {

  @Mock
  private StoneAuthService stoneAuthService;

  @Mock
  private AsmService asmService;

  @Mock
  private StoneStorageService stoneStorageService;

  @InjectMocks
  private FileService fileService;

  @Test
  void getFileBySign_Success() {
    // 准备测试数据
    SignNFileAcRequest nFileAcRequest = new SignNFileAcRequest();
    nFileAcRequest.setFid(
        "F8805F0AEBC51F1E90B867C95C4AEEC8476D06B465335731B7BDF010B7875549955B2EB069A2360F6ADDA09D35299F02179EC409CB29ABA5D9498AFEA9D0A2FA1034E3821C56E288E884E40E7BE4A4D45DDC331CB9C374DF10EC3F259B18712445B0813BE6C02FA2B40C0A11DB2D8DE82BD90BAA5FD0494A");
    nFileAcRequest.setAcid("71554.1181");
    long ets = Instant.now().plusSeconds(3600).toEpochMilli();
    nFileAcRequest.setEts(ets);
    nFileAcRequest.setFn("test.jpg");
    String acAk = "WH07ZwPp6ng2sXy7uwthKJz3";
    nFileAcRequest.setAk(acAk);
    String acSk = "CddM1ML7COaL9WW6pTopvBEA9cEsgO";
    String sig = SignatureUtil.getSignatureWithHmacSha1(acSk, nFileAcRequest.getAcSignRaw());
    nFileAcRequest.setSig(sig);
    nFileAcRequest.setDs(SignatureUtil.messageDigest(nFileAcRequest.getDsSignRaw()));
    // mock stoneAuthService 根据acAk获取acSk
    when(stoneAuthService.getSkByAccessKey(acAk)).thenReturn(acSk);
    int tenantId = 71554;
    @Cleanup MockedStatic<FidInfo> fidInfoMockedStatic = mockStatic(FidInfo.class);
    FidInfo mockFidInfo = new FidInfo();
    mockFidInfo.setFileOwnerTenantId(tenantId);
    String fileSign = "fJeOha_cKfJwnZrFF-gLlTL72gE=";
    mockFidInfo.setSignature(fileSign);
    String fileAk = "E66bW2HwtMREywR6Jkl7Jlpr";
    mockFidInfo.setAccessKey(fileAk);
    mockFidInfo.setPath("N_202406_13_dbf22d0a67e84705b98c907200989c69");
    fidInfoMockedStatic.when(() -> FidInfo.of(anyString())).thenReturn(mockFidInfo);
    // mock acid解析(模拟上游访问)
    @Cleanup MockedStatic<AcidInfo> acidInfoMockedStatic = mockStatic(AcidInfo.class);
    AcidInfo mockAcidInfo = new AcidInfo();
    mockAcidInfo.setFileAcTenantId(tenantId);
    mockAcidInfo.setFileAcEmployeeId(1182);
    mockAcidInfo.setFileAcOutTenantId(0L);
    mockAcidInfo.setFileAcOutEmployeeId(0L);
    acidInfoMockedStatic.when(() -> AcidInfo.of(anyString())).thenReturn(mockAcidInfo);
    // mock asmService 根据租户id获取ea
    when(asmService.getEa(anyInt())).thenReturn(String.valueOf(tenantId));
    // mock stoneAuthService 根据fileAk获取fileSk
    String fileSk = "805PK2jPfVP8w0cE5bOC7NUWEfp1Yi";
    when(stoneAuthService.getSkByAccessKey(fileAk)).thenReturn(fileSk);
    when(stoneStorageService.getFile(any())).thenReturn(mock(InputStream.class));
    // 执行测试
    InputStream result = fileService.getFileBySign(nFileAcRequest);
    // 验证结果
    assertNotNull(result);
    verify(stoneAuthService).getSkByAccessKey(acAk);
    verify(stoneAuthService).getSkByAccessKey(fileAk);
    verify(stoneStorageService).getFile(any());
  }

  @Test
  void getFileBySign_SignExpired() {
    // 准备测试数据
    SignNFileAcRequest nFileAcRequest = new SignNFileAcRequest();
    nFileAcRequest.setFid(
        "F8805F0AEBC51F1E90B867C95C4AEEC8476D06B465335731B7BDF010B7875549955B2EB069A2360F6ADDA09D35299F02179EC409CB29ABA5D9498AFEA9D0A2FA1034E3821C56E288E884E40E7BE4A4D45DDC331CB9C374DF10EC3F259B18712445B0813BE6C02FA2B40C0A11DB2D8DE82BD90BAA5FD0494A");
    nFileAcRequest.setAcid("71554.1181");
    // 设置过期时间为1小时前
    long ets = Instant.now().minusSeconds(3600).toEpochMilli();
    nFileAcRequest.setEts(ets);
    nFileAcRequest.setFn("test.jpg");
    String acAk = "WH07ZwPp6ng2sXy7uwthKJz3";
    nFileAcRequest.setAk(acAk);
    String acSk = "CddM1ML7COaL9WW6pTopvBEA9cEsgO";
    String sig = SignatureUtil.getSignatureWithHmacSha1(acSk, nFileAcRequest.getAcSignRaw());
    nFileAcRequest.setSig(sig);
    nFileAcRequest.setDs(SignatureUtil.messageDigest(nFileAcRequest.getDsSignRaw()));

    // 验证签名过期异常
    assertThrows(FileServerException.class, () -> fileService.getFileBySign(nFileAcRequest));
  }

  @Test
  void getFileBySign_InvalidSignature() {
    // 准备测试数据
    SignNFileAcRequest nFileAcRequest = new SignNFileAcRequest();
    nFileAcRequest.setFid(
        "F8805F0AEBC51F1E90B867C95C4AEEC8476D06B465335731B7BDF010B7875549955B2EB069A2360F6ADDA09D35299F02179EC409CB29ABA5D9498AFEA9D0A2FA1034E3821C56E288E884E40E7BE4A4D45DDC331CB9C374DF10EC3F259B18712445B0813BE6C02FA2B40C0A11DB2D8DE82BD90BAA5FD0494A");
    nFileAcRequest.setAcid("71554.1181");
    long ets = Instant.now().plusSeconds(3600).toEpochMilli();
    nFileAcRequest.setEts(ets);
    nFileAcRequest.setFn("test.jpg");
    String acAk = "WH07ZwPp6ng2sXy7uwthKJz3";
    nFileAcRequest.setAk(acAk);
    // 设置错误的签名
    nFileAcRequest.setSig("invalid_signature");
    nFileAcRequest.setDs(SignatureUtil.messageDigest(nFileAcRequest.getDsSignRaw()));

    // mock stoneAuthService 根据acAk获取acSk
    String acSk = "CddM1ML7COaL9WW6pTopvBEA9cEsgO";
    when(stoneAuthService.getSkByAccessKey(acAk)).thenReturn(acSk);

    // 验证签名无效异常
    assertThrows(FileServerException.class, () -> fileService.getFileBySign(nFileAcRequest));
  }

  @Test
  void getFileBySign_InvalidMessageDigest() {
    // 准备测试数据
    SignNFileAcRequest nFileAcRequest = new SignNFileAcRequest();
    nFileAcRequest.setFid(
        "F8805F0AEBC51F1E90B867C95C4AEEC8476D06B465335731B7BDF010B7875549955B2EB069A2360F6ADDA09D35299F02179EC409CB29ABA5D9498AFEA9D0A2FA1034E3821C56E288E884E40E7BE4A4D45DDC331CB9C374DF10EC3F259B18712445B0813BE6C02FA2B40C0A11DB2D8DE82BD90BAA5FD0494A");
    nFileAcRequest.setAcid("71554.1181");
    long ets = Instant.now().plusSeconds(3600).toEpochMilli();
    nFileAcRequest.setEts(ets);
    nFileAcRequest.setFn("test.jpg");
    String acAk = "WH07ZwPp6ng2sXy7uwthKJz3";
    nFileAcRequest.setAk(acAk);
    String acSk = "CddM1ML7COaL9WW6pTopvBEA9cEsgO";
    String sig = SignatureUtil.getSignatureWithHmacSha1(acSk, nFileAcRequest.getAcSignRaw());
    nFileAcRequest.setSig(sig);
    // 设置错误的消息摘要
    nFileAcRequest.setDs("invalid_message_digest");

    // 验证消息摘要无效异常
    assertThrows(FileServerException.class, () -> fileService.getFileBySign(nFileAcRequest));
  }

  @Test
  void getFileBySign_InvalidAccessKey() {
    // 准备测试数据
    SignNFileAcRequest nFileAcRequest = new SignNFileAcRequest();
    nFileAcRequest.setFid(
        "F8805F0AEBC51F1E90B867C95C4AEEC8476D06B465335731B7BDF010B7875549955B2EB069A2360F6ADDA09D35299F02179EC409CB29ABA5D9498AFEA9D0A2FA1034E3821C56E288E884E40E7BE4A4D45DDC331CB9C374DF10EC3F259B18712445B0813BE6C02FA2B40C0A11DB2D8DE82BD90BAA5FD0494A");
    nFileAcRequest.setAcid("71554.1181");
    long ets = Instant.now().plusSeconds(3600).toEpochMilli();
    nFileAcRequest.setEts(ets);
    nFileAcRequest.setFn("test.jpg");
    String acAk = "invalid_access_key";
    nFileAcRequest.setAk(acAk);
    nFileAcRequest.setDs(SignatureUtil.messageDigest(nFileAcRequest.getDsSignRaw()));

    // mock stoneAuthService 抛出异常
    when(stoneAuthService.getSkByAccessKey(acAk)).thenThrow(new FileServerException("StoneAuthService", ErInfo.AUTH_INVALID_AK, acAk));

    // 验证访问密钥无效异常
    assertThrows(FileServerException.class, () -> fileService.getFileBySign(nFileAcRequest));
  }

  @Test
  void getFileByAuthXC_Success() {
    // 准备测试数据
    SignNFileAcRequest nFileAcRequest = new SignNFileAcRequest();
    nFileAcRequest.setFid(
        "F8805F0AEBC51F1E90B867C95C4AEEC8476D06B465335731B7BDF010B7875549955B2EB069A2360F6ADDA09D35299F02179EC409CB29ABA5D9498AFEA9D0A2FA1034E3821C56E288E884E40E7BE4A4D45DDC331CB9C374DF10EC3F259B18712445B0813BE6C02FA2B40C0A11DB2D8DE82BD90BAA5FD0494A");
    nFileAcRequest.setAcid("71554.1181");
    nFileAcRequest.setFn("test.jpg");
    // 设置必需的参数
    nFileAcRequest.setEts(Instant.now().plusSeconds(3600).toEpochMilli());
    String acAk = "WH07ZwPp6ng2sXy7uwthKJz3";
    nFileAcRequest.setAk(acAk);
    // 设置签名
    String acSk = "CddM1ML7COaL9WW6pTopvBEA9cEsgO";
    String sig = SignatureUtil.getSignatureWithHmacSha1(acSk, nFileAcRequest.getAcSignRaw());
    nFileAcRequest.setSig(sig);
    // 设置消息摘要
    nFileAcRequest.setDs(SignatureUtil.messageDigest(nFileAcRequest.getDsSignRaw()));
    
    String authXC = "valid_auth_xc_cookie";
    
    // mock AuthInfo
    AuthInfo authInfo = new AuthInfo();
    authInfo.setEnterpriseId(71554);
    authInfo.setEmployeeId(1181);
    when(asmService.getAuthXC(authXC)).thenReturn(Optional.of(authInfo));
    
    // mock stoneAuthService
    when(stoneAuthService.getSkByAccessKey(acAk)).thenReturn(acSk);
    
    // mock FidInfo
    @Cleanup MockedStatic<FidInfo> fidInfoMockedStatic = mockStatic(FidInfo.class);
    FidInfo mockFidInfo = new FidInfo();
    mockFidInfo.setFileOwnerTenantId(71554);
    String fileSign = "fJeOha_cKfJwnZrFF-gLlTL72gE=";
    mockFidInfo.setSignature(fileSign);
    String fileAk = "E66bW2HwtMREywR6Jkl7Jlpr";
    mockFidInfo.setAccessKey(fileAk);
    mockFidInfo.setPath("N_202406_13_dbf22d0a67e84705b98c907200989c69");
    fidInfoMockedStatic.when(() -> FidInfo.of(anyString())).thenReturn(mockFidInfo);
    
    // mock AcidInfo
    @Cleanup MockedStatic<AcidInfo> acidInfoMockedStatic = mockStatic(AcidInfo.class);
    AcidInfo mockAcidInfo = new AcidInfo();
    mockAcidInfo.setFileAcTenantId(71554);
    mockAcidInfo.setFileAcEmployeeId(1181);
    acidInfoMockedStatic.when(() -> AcidInfo.of(anyString())).thenReturn(mockAcidInfo);
    
    // mock other dependencies
    when(asmService.getEa(anyInt())).thenReturn("71554");
    String fileSk = "805PK2jPfVP8w0cE5bOC7NUWEfp1Yi";
    when(stoneAuthService.getSkByAccessKey(fileAk)).thenReturn(fileSk);
    when(stoneStorageService.getFile(any())).thenReturn(mock(InputStream.class));
    
    // 执行测试
    InputStream result = fileService.getFileByAuthXC(nFileAcRequest, authXC);
    
    // 验证结果
    assertNotNull(result);
    verify(asmService).getAuthXC(authXC);
    verify(stoneStorageService).getFile(any());
  }

  @Test
  void getFileByAuthXC_InvalidCookie() {
    // 准备测试数据
    SignNFileAcRequest nFileAcRequest = new SignNFileAcRequest();
    nFileAcRequest.setFid(
        "F8805F0AEBC51F1E90B867C95C4AEEC8476D06B465335731B7BDF010B7875549955B2EB069A2360F6ADDA09D35299F02179EC409CB29ABA5D9498AFEA9D0A2FA1034E3821C56E288E884E40E7BE4A4D45DDC331CB9C374DF10EC3F259B18712445B0813BE6C02FA2B40C0A11DB2D8DE82BD90BAA5FD0494A");
    nFileAcRequest.setAcid("71554.1181");
    String authXC = "invalid_auth_xc_cookie";
    
    // mock无效的Cookie
    when(asmService.getAuthXC(authXC)).thenReturn(Optional.empty());
    
    // 验证异常
    assertThrows(FileServerException.class, () -> fileService.getFileByAuthXC(nFileAcRequest, authXC));
  }

  @Test
  void getFileByAuthXC_MismatchedIdentity() {
    // 准备测试数据
    SignNFileAcRequest nFileAcRequest = new SignNFileAcRequest();
    nFileAcRequest.setFid(
        "F8805F0AEBC51F1E90B867C95C4AEEC8476D06B465335731B7BDF010B7875549955B2EB069A2360F6ADDA09D35299F02179EC409CB29ABA5D9498AFEA9D0A2FA1034E3821C56E288E884E40E7BE4A4D45DDC331CB9C374DF10EC3F259B18712445B0813BE6C02FA2B40C0A11DB2D8DE82BD90BAA5FD0494A");
    nFileAcRequest.setAcid("71554.1181");
    String authXC = "mismatched_auth_xc_cookie";
    
    // mock不匹配的身份信息
    AuthInfo authInfo = new AuthInfo();
    authInfo.setEnterpriseId(71555); // 不同的企业ID
    authInfo.setEmployeeId(1182); // 不同的员工ID
    when(asmService.getAuthXC(authXC)).thenReturn(Optional.of(authInfo));
    
    // 验证身份不匹配异常
    assertThrows(FileServerException.class, () -> fileService.getFileByAuthXC(nFileAcRequest, authXC));
  }

  @Test
  void getFileByEm6_Success() {
    // 准备测试数据
    SignNFileAcRequest nFileAcRequest = new SignNFileAcRequest();
    nFileAcRequest.setFid(
        "F8805F0AEBC51F1E90B867C95C4AEEC8476D06B465335731B7BDF010B7875549955B2EB069A2360F6ADDA09D35299F02179EC409CB29ABA5D9498AFEA9D0A2FA1034E3821C56E288E884E40E7BE4A4D45DDC331CB9C374DF10EC3F259B18712445B0813BE6C02FA2B40C0A11DB2D8DE82BD90BAA5FD0494A");
    nFileAcRequest.setAcid("71554.1181.71555.1182"); // 包含下游企业信息
    nFileAcRequest.setFn("test.jpg");
    // 设置必需的参数
    nFileAcRequest.setEts(Instant.now().plusSeconds(3600).toEpochMilli());
    String acAk = "WH07ZwPp6ng2sXy7uwthKJz3";
    nFileAcRequest.setAk(acAk);
    // 设置签名
    String acSk = "CddM1ML7COaL9WW6pTopvBEA9cEsgO";
    String sig = SignatureUtil.getSignatureWithHmacSha1(acSk, nFileAcRequest.getAcSignRaw());
    nFileAcRequest.setSig(sig);
    // 设置消息摘要
    nFileAcRequest.setDs(SignatureUtil.messageDigest(nFileAcRequest.getDsSignRaw()));
    
    String erInfo = "valid_er_info_cookie";
    
    // mock AuthInfo with downstream info
    AuthInfo authInfo = AuthInfo.ofDownstream(71555, 1182);
    when(asmService.getEm6(erInfo)).thenReturn(Optional.of(authInfo));
    
    // mock stoneAuthService
    when(stoneAuthService.getSkByAccessKey(acAk)).thenReturn(acSk);
    
    // mock FidInfo
    @Cleanup MockedStatic<FidInfo> fidInfoMockedStatic = mockStatic(FidInfo.class);
    FidInfo mockFidInfo = new FidInfo();
    mockFidInfo.setFileOwnerTenantId(71554);
    String fileSign = "fJeOha_cKfJwnZrFF-gLlTL72gE=";
    mockFidInfo.setSignature(fileSign);
    String fileAk = "E66bW2HwtMREywR6Jkl7Jlpr";
    mockFidInfo.setAccessKey(fileAk);
    mockFidInfo.setPath("N_202406_13_dbf22d0a67e84705b98c907200989c69");
    fidInfoMockedStatic.when(() -> FidInfo.of(anyString())).thenReturn(mockFidInfo);
    
    // mock AcidInfo with both upstream and downstream info
    @Cleanup MockedStatic<AcidInfo> acidInfoMockedStatic = mockStatic(AcidInfo.class);
    AcidInfo mockAcidInfo = new AcidInfo();
    mockAcidInfo.setFileAcTenantId(71554);
    mockAcidInfo.setFileAcEmployeeId(1181);
    mockAcidInfo.setFileAcOutTenantId(71555L);
    mockAcidInfo.setFileAcOutEmployeeId(1182L);
    acidInfoMockedStatic.when(() -> AcidInfo.of(anyString())).thenReturn(mockAcidInfo);
    
    // mock other dependencies
    when(asmService.getEa(anyInt())).thenReturn("71554");
    String fileSk = "805PK2jPfVP8w0cE5bOC7NUWEfp1Yi";
    when(stoneAuthService.getSkByAccessKey(fileAk)).thenReturn(fileSk);
    when(stoneStorageService.getFile(any())).thenReturn(mock(InputStream.class));
    
    // 执行测试
    InputStream result = fileService.getFileByEm6(nFileAcRequest, erInfo);
    
    // 验证结果
    assertNotNull(result);
    verify(asmService).getEm6(erInfo);
    verify(stoneStorageService).getFile(any());
  }

  @Test
  void getFileByCookie_SuccessWithAuthXC() {
    // 准备测试数据
    SignNFileAcRequest nFileAcRequest = new SignNFileAcRequest();
    nFileAcRequest.setFid(
        "F8805F0AEBC51F1E90B867C95C4AEEC8476D06B465335731B7BDF010B7875549955B2EB069A2360F6ADDA09D35299F02179EC409CB29ABA5D9498AFEA9D0A2FA1034E3821C56E288E884E40E7BE4A4D45DDC331CB9C374DF10EC3F259B18712445B0813BE6C02FA2B40C0A11DB2D8DE82BD90BAA5FD0494A");
    nFileAcRequest.setAcid("71554");  // 只包含企业ID
    nFileAcRequest.setFn("test.jpg");
    nFileAcRequest.setEts(Instant.now().plusSeconds(3600).toEpochMilli());
    String acAk = "WH07ZwPp6ng2sXy7uwthKJz3";
    nFileAcRequest.setAk(acAk);
    // 设置签名
    String acSk = "CddM1ML7COaL9WW6pTopvBEA9cEsgO";
    String sig = SignatureUtil.getSignatureWithHmacSha1(acSk, nFileAcRequest.getAcSignRaw());
    nFileAcRequest.setSig(sig);
    nFileAcRequest.setDs(SignatureUtil.messageDigest(nFileAcRequest.getDsSignRaw()));
    
    String authXC = "valid_auth_xc_cookie";
    String erInfo = "valid_er_info_cookie";
    
    // mock AuthInfo for authXC
    AuthInfo authInfo = new AuthInfo();
    authInfo.setEnterpriseId(71554);
    authInfo.setEmployeeId(1181);
    when(asmService.getAuthXC(authXC)).thenReturn(Optional.of(authInfo));
    
    // mock stoneAuthService
    when(stoneAuthService.getSkByAccessKey(acAk)).thenReturn(acSk);
    
    // mock FidInfo
    @Cleanup MockedStatic<FidInfo> fidInfoMockedStatic = mockStatic(FidInfo.class);
    FidInfo mockFidInfo = new FidInfo();
    mockFidInfo.setFileOwnerTenantId(71554);
    String fileSign = "fJeOha_cKfJwnZrFF-gLlTL72gE=";
    mockFidInfo.setSignature(fileSign);
    String fileAk = "E66bW2HwtMREywR6Jkl7Jlpr";
    mockFidInfo.setAccessKey(fileAk);
    mockFidInfo.setPath("N_202406_13_dbf22d0a67e84705b98c907200989c69");
    fidInfoMockedStatic.when(() -> FidInfo.of(anyString())).thenReturn(mockFidInfo);
    
    // mock AcidInfo
    @Cleanup MockedStatic<AcidInfo> acidInfoMockedStatic = mockStatic(AcidInfo.class);
    AcidInfo mockAcidInfo = new AcidInfo();
    mockAcidInfo.setFileAcTenantId(71554);
    mockAcidInfo.setFileAcEmployeeId(1181);
    acidInfoMockedStatic.when(() -> AcidInfo.of(anyString())).thenReturn(mockAcidInfo);
    
    when(asmService.getEa(anyInt())).thenReturn("71554");
    String fileSk = "805PK2jPfVP8w0cE5bOC7NUWEfp1Yi";
    when(stoneAuthService.getSkByAccessKey(fileAk)).thenReturn(fileSk);
    when(stoneStorageService.getFile(any())).thenReturn(mock(InputStream.class));
    
    // 执行测试
    InputStream result = fileService.getFileByCookie(nFileAcRequest, authXC, erInfo);
    
    // 验证结果
    assertNotNull(result);
    verify(asmService).getAuthXC(authXC);
    verify(stoneStorageService).getFile(any());
  }

  @Test
  void getFileByCookie_SuccessWithEm6() {
    // 准备测试数据
    SignNFileAcRequest nFileAcRequest = new SignNFileAcRequest();
    nFileAcRequest.setFid(
        "F8805F0AEBC51F1E90B867C95C4AEEC8476D06B465335731B7BDF010B7875549955B2EB069A2360F6ADDA09D35299F02179EC409CB29ABA5D9498AFEA9D0A2FA1034E3821C56E288E884E40E7BE4A4D45DDC331CB9C374DF10EC3F259B18712445B0813BE6C02FA2B40C0A11DB2D8DE82BD90BAA5FD0494A");
    nFileAcRequest.setAcid("71554");  // 只包含企业ID
    nFileAcRequest.setFn("test.jpg");
    nFileAcRequest.setEts(Instant.now().plusSeconds(3600).toEpochMilli());
    String acAk = "WH07ZwPp6ng2sXy7uwthKJz3";
    nFileAcRequest.setAk(acAk);
    // 设置签名
    String acSk = "CddM1ML7COaL9WW6pTopvBEA9cEsgO";
    String sig = SignatureUtil.getSignatureWithHmacSha1(acSk, nFileAcRequest.getAcSignRaw());
    nFileAcRequest.setSig(sig);
    nFileAcRequest.setDs(SignatureUtil.messageDigest(nFileAcRequest.getDsSignRaw()));
    
    String authXC = null;
    String erInfo = "valid_er_info_cookie";
    
    // mock AuthInfo for em6
    AuthInfo authInfo = new AuthInfo();
    authInfo.setUpstreamEa("71554");
    when(asmService.getEm6(erInfo)).thenReturn(Optional.of(authInfo));
    
    // mock stoneAuthService
    when(stoneAuthService.getSkByAccessKey(acAk)).thenReturn(acSk);
    
    // mock FidInfo
    @Cleanup MockedStatic<FidInfo> fidInfoMockedStatic = mockStatic(FidInfo.class);
    FidInfo mockFidInfo = new FidInfo();
    mockFidInfo.setFileOwnerTenantId(71554);
    String fileSign = "fJeOha_cKfJwnZrFF-gLlTL72gE=";
    mockFidInfo.setSignature(fileSign);
    String fileAk = "E66bW2HwtMREywR6Jkl7Jlpr";
    mockFidInfo.setAccessKey(fileAk);
    mockFidInfo.setPath("N_202406_13_dbf22d0a67e84705b98c907200989c69");
    fidInfoMockedStatic.when(() -> FidInfo.of(anyString())).thenReturn(mockFidInfo);
    
    // mock AcidInfo
    @Cleanup MockedStatic<AcidInfo> acidInfoMockedStatic = mockStatic(AcidInfo.class);
    AcidInfo mockAcidInfo = new AcidInfo();
    mockAcidInfo.setFileAcTenantId(71554);
    mockAcidInfo.setFileAcEmployeeId(1181);
    acidInfoMockedStatic.when(() -> AcidInfo.of(anyString())).thenReturn(mockAcidInfo);
    
    when(asmService.getEa(anyInt())).thenReturn("71554");
    when(asmService.getEid("71554")).thenReturn(71554);
    String fileSk = "805PK2jPfVP8w0cE5bOC7NUWEfp1Yi";
    when(stoneAuthService.getSkByAccessKey(fileAk)).thenReturn(fileSk);
    when(stoneStorageService.getFile(any())).thenReturn(mock(InputStream.class));
    
    // 执行测试
    InputStream result = fileService.getFileByCookie(nFileAcRequest, authXC, erInfo);
    
    // 验证结果
    assertNotNull(result);
    verify(asmService).getEm6(erInfo);
    verify(stoneStorageService).getFile(any());
  }

  @Test
  void getFileByConditionAuthXC_Success() {
    // 准备测试数据
    SignNFileAcRequest nFileAcRequest = new SignNFileAcRequest();
    nFileAcRequest.setFid(
        "F8805F0AEBC51F1E90B867C95C4AEEC8476D06B465335731B7BDF010B7875549955B2EB069A2360F6ADDA09D35299F02179EC409CB29ABA5D9498AFEA9D0A2FA1034E3821C56E288E884E40E7BE4A4D45DDC331CB9C374DF10EC3F259B18712445B0813BE6C02FA2B40C0A11DB2D8DE82BD90BAA5FD0494A");
    nFileAcRequest.setAcid("71554.1181");
    // 设置过期的时间戳
    nFileAcRequest.setEts(Instant.now().minusSeconds(3600).toEpochMilli());
    nFileAcRequest.setFn("test.jpg");
    String acAk = "WH07ZwPp6ng2sXy7uwthKJz3";
    nFileAcRequest.setAk(acAk);
    // 设置签名
    String acSk = "CddM1ML7COaL9WW6pTopvBEA9cEsgO";
    String sig = SignatureUtil.getSignatureWithHmacSha1(acSk, nFileAcRequest.getAcSignRaw());
    nFileAcRequest.setSig(sig);
    nFileAcRequest.setDs(SignatureUtil.messageDigest(nFileAcRequest.getDsSignRaw()));
    
    String authXC = "valid_auth_xc_cookie";
    
    // mock AuthInfo
    AuthInfo authInfo = new AuthInfo();
    authInfo.setEnterpriseId(71554);
    authInfo.setEmployeeId(1181);
    when(asmService.getAuthXC(authXC)).thenReturn(Optional.of(authInfo));
    
    // mock stoneAuthService
    when(stoneAuthService.getSkByAccessKey(acAk)).thenReturn(acSk);
    
    // mock FidInfo
    @Cleanup MockedStatic<FidInfo> fidInfoMockedStatic = mockStatic(FidInfo.class);
    FidInfo mockFidInfo = new FidInfo();
    mockFidInfo.setFileOwnerTenantId(71554);
    String fileSign = "fJeOha_cKfJwnZrFF-gLlTL72gE=";
    mockFidInfo.setSignature(fileSign);
    String fileAk = "E66bW2HwtMREywR6Jkl7Jlpr";
    mockFidInfo.setAccessKey(fileAk);
    mockFidInfo.setPath("N_202406_13_dbf22d0a67e84705b98c907200989c69");
    fidInfoMockedStatic.when(() -> FidInfo.of(anyString())).thenReturn(mockFidInfo);
    
    // mock AcidInfo
    @Cleanup MockedStatic<AcidInfo> acidInfoMockedStatic = mockStatic(AcidInfo.class);
    AcidInfo mockAcidInfo = new AcidInfo();
    mockAcidInfo.setFileAcTenantId(71554);
    mockAcidInfo.setFileAcEmployeeId(1181);
    acidInfoMockedStatic.when(() -> AcidInfo.of(anyString())).thenReturn(mockAcidInfo);
    
    when(asmService.getEa(anyInt())).thenReturn("71554");
    String fileSk = "805PK2jPfVP8w0cE5bOC7NUWEfp1Yi";
    when(stoneAuthService.getSkByAccessKey(fileAk)).thenReturn(fileSk);
    when(stoneStorageService.getFile(any())).thenReturn(mock(InputStream.class));
    
    // 执行测试
    InputStream result = fileService.getFileByConditionAuthXC(nFileAcRequest, authXC);
    
    // 验证结果
    assertNotNull(result);
    verify(asmService).getAuthXC(authXC);
    verify(stoneStorageService).getFile(any());
  }

  @Test
  void getCFile_Success() {
    // 准备测试数据
    NoSignCFileAcRequest cFileAcRequest = new NoSignCFileAcRequest();
    cFileAcRequest.setCid("C_202406_13_test");
    cFileAcRequest.setAcid("71554.1181.71555.1182");
    cFileAcRequest.setExt("jpg");
    cFileAcRequest.setBn("test");

    cFileAcRequest.setDs(SignatureUtil.messageDigest(cFileAcRequest.getDsSignRaw()));

    // mock AcidInfo
    @Cleanup MockedStatic<AcidInfo> acidInfoMockedStatic = mockStatic(AcidInfo.class);
    AcidInfo mockAcidInfo = new AcidInfo();
    mockAcidInfo.setFileAcTenantId(71554);
    mockAcidInfo.setFileAcEmployeeId(1181);
    acidInfoMockedStatic.when(() -> AcidInfo.of(anyString())).thenReturn(mockAcidInfo);
    
    when(asmService.getEa(anyInt())).thenReturn("71554");
    when(stoneStorageService.getFile(any())).thenReturn(mock(InputStream.class));
    
    // 执行测试
    InputStream result = fileService.getCFile(cFileAcRequest);
    
    // 验证结果
    assertNotNull(result);
    verify(stoneStorageService).getFile(any());
  }

  @Test
  void getAvatarFile_Success() {
    // 准备测试数据
    AvatarRequest avatarRequest = new AvatarRequest();
    avatarRequest.setFid("avatar_123");
    avatarRequest.setAcid("71554.1181");
    avatarRequest.setFn("avatar.jpg");
    String raw = SignatureUtil.generatorRaw(avatarRequest.getFid(), avatarRequest.getAcid(), avatarRequest.getFn());
    avatarRequest.setDs(SignatureUtil.messageDigest(raw));
    
    OutputStream outputStream = mock(OutputStream.class);
    
    // 执行测试
    fileService.getAvatarFile(avatarRequest, outputStream);
    
    // 验证结果
    verify(stoneStorageService).getAvatarFile(eq("avatar_123"), eq(outputStream));
  }

  @Test
  void getFileByConditionEm6_Success() {
    // 准备测试数据
    SignNFileAcRequest nFileAcRequest = new SignNFileAcRequest();
    // 设置所有必需的字段
    nFileAcRequest.setFid(
        "F8805F0AEBC51F1E90B867C95C4AEEC8476D06B465335731B7BDF010B7875549955B2EB069A2360F6ADDA09D35299F02179EC409CB29ABA5D9498AFEA9D0A2FA1034E3821C56E288E884E40E7BE4A4D45DDC331CB9C374DF10EC3F259B18712445B0813BE6C02FA2B40C0A11DB2D8DE82BD90BAA5FD0494A");
    nFileAcRequest.setAcid("71554.1181.71555.1182"); // 包含下游企业信息
    // 设置过期的时间戳
    nFileAcRequest.setEts(Instant.now().minusSeconds(3600).toEpochMilli());
    nFileAcRequest.setFn("test.jpg");
    // 设置其他必需字段
    String acAk = "WH07ZwPp6ng2sXy7uwthKJz3";
    nFileAcRequest.setAk(acAk);
    nFileAcRequest.setSize("800x600");
    // 设置签名
    String acSk = "CddM1ML7COaL9WW6pTopvBEA9cEsgO";
    String sig = SignatureUtil.getSignatureWithHmacSha1(acSk, nFileAcRequest.getAcSignRaw());
    nFileAcRequest.setSig(sig);
    // 设置消息摘要
    nFileAcRequest.setDs(SignatureUtil.messageDigest(nFileAcRequest.getDsSignRaw()));
    
    String erInfo = "valid_er_info_cookie";
    
    // mock AuthInfo with downstream info
    AuthInfo authInfo = AuthInfo.ofDownstream(71555, 1182);
    when(asmService.getEm6(erInfo)).thenReturn(Optional.of(authInfo));
    
    // mock stoneAuthService
    when(stoneAuthService.getSkByAccessKey(acAk)).thenReturn(acSk);
    
    // mock FidInfo
    @Cleanup MockedStatic<FidInfo> fidInfoMockedStatic = mockStatic(FidInfo.class);
    FidInfo mockFidInfo = new FidInfo();
    mockFidInfo.setFileOwnerTenantId(71554);
    String fileSign = "fJeOha_cKfJwnZrFF-gLlTL72gE=";
    mockFidInfo.setSignature(fileSign);
    String fileAk = "E66bW2HwtMREywR6Jkl7Jlpr";
    mockFidInfo.setAccessKey(fileAk);
    mockFidInfo.setPath("N_202406_13_dbf22d0a67e84705b98c907200989c69");
    fidInfoMockedStatic.when(() -> FidInfo.of(anyString())).thenReturn(mockFidInfo);
    
    // mock AcidInfo with both upstream and downstream info
    @Cleanup MockedStatic<AcidInfo> acidInfoMockedStatic = mockStatic(AcidInfo.class);
    AcidInfo mockAcidInfo = new AcidInfo();
    mockAcidInfo.setFileAcTenantId(71554);
    mockAcidInfo.setFileAcEmployeeId(1181);
    mockAcidInfo.setFileAcOutTenantId(71555L);
    mockAcidInfo.setFileAcOutEmployeeId(1182L);
    acidInfoMockedStatic.when(() -> AcidInfo.of(anyString())).thenReturn(mockAcidInfo);
    
    when(asmService.getEa(anyInt())).thenReturn("71554");
    // mock file sk
    String fileSk = "805PK2jPfVP8w0cE5bOC7NUWEfp1Yi";
    when(stoneAuthService.getSkByAccessKey(fileAk)).thenReturn(fileSk);
    when(stoneStorageService.getFile(any())).thenReturn(mock(InputStream.class));
    
    // 执行测试
    InputStream result = fileService.getFileByConditionEm6(nFileAcRequest, erInfo);
    
    // 验证结果
    assertNotNull(result);
    verify(asmService).getEm6(erInfo);
    verify(stoneStorageService).getFile(any());
  }

  @Test
  void uploadFile_Success() {
    // 准备测试数据
    SignFileUpRequest fileUpRequest = new SignFileUpRequest();
    fileUpRequest.setAcid("71554.1181");
    fileUpRequest.setFileName("test.jpg");
    fileUpRequest.setResource("test");
    fileUpRequest.setSize(1024);
    fileUpRequest.setExpiry(Instant.now().plusSeconds(3600).toEpochMilli());
    String acAk = "WH07ZwPp6ng2sXy7uwthKJz3";
    fileUpRequest.setAk(acAk);
    
    // 设置签名
    String acSk = "CddM1ML7COaL9WW6pTopvBEA9cEsgO";
    String sign = SignatureUtil.getSignatureWithHmacSha1(acSk, fileUpRequest.getSignRaw());
    fileUpRequest.setSign(sign);
    
    // 设置消息摘要
    String digest = SignatureUtil.messageDigest(fileUpRequest.getMsgRaw());
    fileUpRequest.setDigest(digest);
    
    // mock stoneAuthService
    when(stoneAuthService.getSkByAccessKey(acAk)).thenReturn(acSk);
    
    // mock asmService
    when(asmService.getEa(71554)).thenReturn("71554");
    
    // mock stoneStorageService
    String expectedPath = "N_202406_13_test";
    when(stoneStorageService.uploadSingleFile(any(), any())).thenReturn(expectedPath);
    
    // 执行测试
    InputStream inputStream = mock(InputStream.class);
    String result = fileService.uploadFile(fileUpRequest, inputStream);
    
    // 验证结果
    assertEquals(expectedPath, result);
    verify(stoneAuthService).getSkByAccessKey(acAk);
    verify(asmService).getEa(71554);
    verify(stoneStorageService).uploadSingleFile(any(), eq(inputStream));
  }

} 