package com.fxiaoke.file.server.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.facishare.asm.api.auth.AuthXC;
import com.facishare.asm.api.model.CookieToAuth;
import com.facishare.asm.api.service.ActiveSessionAuthorizeService;
import com.facishare.converter.EIEAConverter;
import com.facishare.uc.api.model.fscore.EnterpriseData;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.enterpriserelation2.arg.AuthWithoutEaArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.AuthUserResult;
import com.fxiaoke.enterpriserelation2.service.AuthService;
import com.fxiaoke.file.server.config.CmsPropertiesConfig;
import com.fxiaoke.file.server.domain.model.api.AuthInfo;
import com.fxiaoke.file.server.domain.model.api.EnterpriseBaseInfo;
import com.github.autoconf.helper.ConfigEiHelper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Date;
import java.util.Optional;

@ExtendWith(MockitoExtension.class)
class AsmServiceTest {

    @Mock
    private AuthService authService;

    @Mock
    private EIEAConverter eieaConverter;

    @Mock
    private EnterpriseEditionService enterpriseEditionService;

    @Mock
    private ActiveSessionAuthorizeService activeSessionAuthorizeService;

    @Mock
    private CmsPropertiesConfig cmsPropertiesConfig;

    @InjectMocks
    private AsmService asmService;

    @Test
    void getEid_Success() {
        // 准备测试数据
        String ea = "71554";
        int expectedEid = 71554;
        
        // mock转换
        when(eieaConverter.enterpriseAccountToId(ea)).thenReturn(expectedEid);
        
        // 执行测试
        int result = asmService.getEid(ea);
        
        // 验证结果
        assertEquals(expectedEid, result);
        verify(eieaConverter).enterpriseAccountToId(ea);
    }

    @Test
    void getEa_Success() {
        // 准备测试数据
        int eid = 71554;
        String expectedEa = "71554";
        
        // mock转换
        when(eieaConverter.enterpriseIdToAccount(eid)).thenReturn(expectedEa);
        
        // 执行测试
        String result = asmService.getEa(eid);
        
        // 验证结果
        assertEquals(expectedEa, result);
        verify(eieaConverter).enterpriseIdToAccount(eid);
    }

    @Test
    void getAuthXC_Success() {
        // 准备测试数据
        String cookie = "valid_cookie";
        AuthXC authXC = new AuthXC();
        authXC.setEnterpriseAccount("71554");
        authXC.setEnterpriseId(71554);
        authXC.setEmployeeId(1181);
        
        // mock cookie转换结果
        @SuppressWarnings("unchecked") // 抑制泛型转换警告
        CookieToAuth.Result<AuthXC> cookieResult = (CookieToAuth.Result<AuthXC>) mock(CookieToAuth.Result.class);
        when(cookieResult.getBody()).thenReturn(authXC);
        when(activeSessionAuthorizeService.cookieToAuthXC(any(CookieToAuth.Argument.class))).thenReturn(cookieResult);
        
        // 执行测试
        Optional<AuthInfo> result = asmService.getAuthXC(cookie);
        
        // 验证结果
        assertTrue(result.isPresent());
        AuthInfo authInfo = result.get();
        assertEquals("71554", authInfo.getEnterpriseAccount());
        assertEquals(71554, authInfo.getEnterpriseId());
        assertEquals(1181, authInfo.getEmployeeId());
        verify(activeSessionAuthorizeService).cookieToAuthXC(any(CookieToAuth.Argument.class));
    }

    @Test
    void getAuthXC_EmptyCookie() {
        // 执行测试
        Optional<AuthInfo> result = asmService.getAuthXC("");
        
        // 验证结果
        assertFalse(result.isPresent());
        verify(activeSessionAuthorizeService, never()).cookieToAuthXC(any());
    }

    @Test
    void getEm6_Success() {
        // 准备测试数据
        String cookie = "valid_cookie";
        String defaultAppid = "default_appid";
        when(cmsPropertiesConfig.getEm6DefaultAppid()).thenReturn(defaultAppid);
        
        // mock认证结果
        AuthUserResult authUserResult = new AuthUserResult();
        authUserResult.setDownstreamOuterTenantId(71555L);
        authUserResult.setDownstreamOuterUid(1182L);
        authUserResult.setUpstreamEa("71554");
        
        // 创建RestResult并设置结果
        @SuppressWarnings("unchecked")
        RestResult<AuthUserResult> restResult = mock(RestResult.class);
        when(restResult.isSuccess()).thenReturn(true);
        when(restResult.getData()).thenReturn(authUserResult);
        when(authService.authWithoutEa(any(HeaderObj.class), any(AuthWithoutEaArg.class))).thenReturn(restResult);
        
        // 执行测试
        Optional<AuthInfo> result = asmService.getEm6(cookie);
        
        // 验证结果
        assertTrue(result.isPresent());
        AuthInfo authInfo = result.get();
        assertEquals(71555L, authInfo.getDownstreamOuterTenantId());
        assertEquals(1182L, authInfo.getDownstreamOuterUid());
        assertEquals("71554", authInfo.getUpstreamEa());
        verify(authService).authWithoutEa(any(HeaderObj.class), any(AuthWithoutEaArg.class));
    }

    @Test
    void getEm6_EmptyCookie() {
        // 执行测试
        Optional<AuthInfo> result = asmService.getEm6("");
        
        // 验证结果
        assertFalse(result.isPresent());
        verify(authService, never()).authWithoutEa(any(), any());
    }

    @Test
    void getBaseEnterpriseInfo_Success() {
        // 准备测试数据
        String ea = "71554";
        EnterpriseData enterpriseData = new EnterpriseData();
        enterpriseData.setEnterpriseId(71554);
        enterpriseData.setDfsAddress("dfs_address");
        Date createTime = new Date();
        enterpriseData.setCreateTime(createTime);
        
        // mock企业数据结果
        com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult getEnterpriseDataResult = 
            new com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult();
        getEnterpriseDataResult.setEnterpriseData(enterpriseData);
        when(enterpriseEditionService.getEnterpriseData(any())).thenReturn(getEnterpriseDataResult);
        
        // 执行测试
        EnterpriseBaseInfo result = asmService.getBaseEnterpriseInfo(ea);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(ea, result.getEnterpriseAccount());
        assertEquals("71554", result.getTenantId());
        assertEquals(createTime, result.getCreateTime());
        assertEquals("dfs_address", result.getDfsAddress());
        verify(enterpriseEditionService).getEnterpriseData(any());
    }

    @Test
    void isCurrentCloudUser_Success() {
        // 准备测试数据
        String tenantId = "71554";
        
        // mock ConfigEiHelper
        try (MockedStatic<ConfigEiHelper> configEiHelperMockedStatic = mockStatic(ConfigEiHelper.class)) {
            ConfigEiHelper configEiHelper = mock(ConfigEiHelper.class);
            configEiHelperMockedStatic.when(ConfigEiHelper::getInstance).thenReturn(configEiHelper);
            when(configEiHelper.isCurrentCloud(tenantId)).thenReturn(true);
            
            // 执行测试
            boolean result = asmService.isCurrentCloudUser(tenantId);
            
            // 验证结果
            assertTrue(result);
            verify(configEiHelper).isCurrentCloud(tenantId);
        }
    }
} 