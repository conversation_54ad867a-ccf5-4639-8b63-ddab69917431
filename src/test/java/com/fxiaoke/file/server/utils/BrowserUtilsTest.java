package com.fxiaoke.file.server.utils;

import com.fxiaoke.file.server.domain.constants.Constant;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpHeaders;
import org.springframework.mock.web.MockHttpServletResponse;

import static org.junit.jupiter.api.Assertions.*;

class BrowserUtilsTest {

    @Test
    void testIsSafari() {
        // Safari浏览器 User-Agent
        String safariUA = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15";
        // Chrome浏览器 User-Agent
        String chromeUA = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36";
        
        String dispositionNameSafari = BrowserUtils.getDispositionName(safariUA, "attachment", "测试文件.txt");
        String dispositionNameChrome = BrowserUtils.getDispositionName(chromeUA, "attachment", "测试文件.txt");
        
        // Safari应该使用ISO-8859-1编码
        assertNotEquals(dispositionNameSafari, dispositionNameChrome);
        assertTrue(dispositionNameSafari.contains("attachment"));
    }

    @Test
    void testGetDispositionName() {
        String filename = "测试文件.pdf";
        String userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/120.0.0.0";
        String dispositionName = BrowserUtils.getDispositionName(userAgent, "attachment", filename);
        
        assertTrue(dispositionName.startsWith("attachment"));
        assertTrue(dispositionName.contains("filename="));
        assertTrue(dispositionName.contains("filename*=utf-8''"));
    }

    @Test
    void testSetHead() {
        HttpServletResponse response = new MockHttpServletResponse();
        String filename = "test.pdf";
        String userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/120.0.0.0";
        
        BrowserUtils.setHead(response, "attachment", "pdf", userAgent, filename);
        
        assertEquals(Constant.DEFAULT_CONTENT_ENCODING, response.getHeader(HttpHeaders.CONTENT_ENCODING));
        assertEquals(Constant.DEFAULT_CACHE_CONTROL, response.getHeader(HttpHeaders.CACHE_CONTROL));
        assertNotNull(response.getHeader(HttpHeaders.CONTENT_TYPE));
        assertNotNull(response.getHeader(HttpHeaders.CONTENT_DISPOSITION));
    }

    @Test
    void testGetHead() {
        String filename = "test.pdf";
        String userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/120.0.0.0";
        
        HttpHeaders headers = BrowserUtils.getHead("attachment", "pdf", userAgent, filename);
        
        assertNotNull(headers.getFirst(HttpHeaders.CONTENT_DISPOSITION));
        assertEquals(Constant.DEFAULT_CONTENT_ENCODING, headers.getFirst(HttpHeaders.CONTENT_ENCODING));
        assertEquals(Constant.DEFAULT_CACHE_CONTROL, headers.getFirst(HttpHeaders.CACHE_CONTROL));
        assertNotNull(headers.getFirst(HttpHeaders.CONTENT_TYPE));
    }

    @Test
    void testGetSimpleUserAgent() {
        String mobileUA = "Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1";
        String pcUA = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/120.0.0.0";
        
        String mobileInfo = BrowserUtils.getSimpUserAgent(mobileUA);
        String pcInfo = BrowserUtils.getSimpUserAgent(pcUA);
        
        assertTrue(mobileInfo.contains("OS:"));
        assertTrue(pcInfo.contains("Browser:"));
    }

    @Test
    void testGetHeadWithExtension() {
        HttpHeaders headers = BrowserUtils.getHead("pdf");
        
        assertEquals(Constant.DEFAULT_CONTENT_DISPOSITION, headers.getFirst(HttpHeaders.CONTENT_DISPOSITION));
        assertEquals(Constant.DEFAULT_CONTENT_ENCODING, headers.getFirst(HttpHeaders.CONTENT_ENCODING));
        assertEquals(Constant.DEFAULT_CACHE_CONTROL, headers.getFirst(HttpHeaders.CACHE_CONTROL));
        assertNotNull(headers.getFirst(HttpHeaders.CONTENT_TYPE));
    }
} 