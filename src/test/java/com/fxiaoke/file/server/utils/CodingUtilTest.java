package com.fxiaoke.file.server.utils;

import com.fxiaoke.file.server.domain.constants.ErInfo;
import com.fxiaoke.file.server.domain.exception.FileServerException;
import com.fxiaoke.stone.commons.domain.utils.SignatureUtil;
import com.fxiaoke.stone.commons.domain.exception.StoneCommonClientException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;

import java.time.Instant;
import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;

class CodingUtilTest {

    private static final String SECRET_KEY = "testSecretKey";
    private static final String RAW_MESSAGE = "testMessage";
    private static String VALID_SIGNATURE;
    private static String VALID_DIGEST;
    private static final String INVALID_SIGNATURE = "invalidSignature";

    @BeforeEach
    void setUp() throws StoneCommonClientException {
        VALID_SIGNATURE = SignatureUtil.getSignatureWithHmacSha1(SECRET_KEY, RAW_MESSAGE);
        VALID_DIGEST = SignatureUtil.messageDigest(RAW_MESSAGE);
    }

    @Test
    void assertSignExpire_ShouldReturnTrue_WhenExpired() {
        Long expiredTime = Instant.now().minusSeconds(60).toEpochMilli();
        assertTrue(CodingUtil.assertSignExpire(expiredTime));
    }

    @Test
    void assertSignExpire_ShouldReturnFalse_WhenNotExpired() {
        Long futureTime = Instant.now().plusSeconds(60).toEpochMilli();
        assertFalse(CodingUtil.assertSignExpire(futureTime));
    }

    @Test
    void assertSignNotExpire_ShouldThrowException_WhenExpired() {
        Long expiredTime = Instant.now().minusSeconds(60).toEpochMilli();
        FileServerException exception = assertThrows(FileServerException.class,
            () -> CodingUtil.assertSignNotExpire(expiredTime));
        assertEquals(ErInfo.AUTH_EXPIRED_SIGN.getCode(), exception.getCode());
    }

    @Test
    void assertSignNotExpire_ShouldNotThrowException_WhenNotExpired() {
        Long futureTime = Instant.now().plusSeconds(60).toEpochMilli();
        assertDoesNotThrow(() -> CodingUtil.assertSignNotExpire(futureTime));
    }

    @Test
    void assertMessageDigest_ShouldThrowException_WhenInvalidDigest() {
        String invalidDigest = "invalidDigest";
        FileServerException exception = assertThrows(FileServerException.class,
            () -> CodingUtil.assertMessageDigest(RAW_MESSAGE, invalidDigest));
        assertEquals(ErInfo.AUTH_MESSAGE_LOSS.getCode(), exception.getCode());
    }

    @Test
    void assertMessageDigest_ShouldNotThrowException_WhenValidDigest() {
        assertDoesNotThrow(() -> CodingUtil.assertMessageDigest(RAW_MESSAGE, VALID_DIGEST));
    }

    @Test
    void assertAcSignConsist_ShouldThrowException_WhenInvalidSignature() {
        FileServerException exception = assertThrows(FileServerException.class,
            () -> CodingUtil.assertAcSignConsist(SECRET_KEY, RAW_MESSAGE, INVALID_SIGNATURE));
        assertEquals(ErInfo.AUTH_INVALID_SIGN.getCode(), exception.getCode());
    }

    @Test
    void assertAcSignConsist_ShouldNotThrowException_WhenValidSignature() {
        assertDoesNotThrow(
            () -> CodingUtil.assertAcSignConsist(SECRET_KEY, RAW_MESSAGE, VALID_SIGNATURE));
    }

    @Test
    void assertMetaDataSignConsist_ShouldThrowException_WhenInvalidSignature() {
        FileServerException exception = assertThrows(FileServerException.class,
            () -> CodingUtil.assertMetaDataSignConsist(SECRET_KEY, RAW_MESSAGE, INVALID_SIGNATURE));
        assertEquals(ErInfo.AUTH_INVALID_META_SIGN.getCode(), exception.getCode());
    }

    @Test
    void assertMetaDataSignConsist_ShouldNotThrowException_WhenValidSignature() {
        assertDoesNotThrow(
            () -> CodingUtil.assertMetaDataSignConsist(SECRET_KEY, RAW_MESSAGE, VALID_SIGNATURE));
    }

    @Test
    void assertMetaDataSignConsist_ShouldNotThrowException_WhenValidSignatureWithJpgSuffix() {
        String signatureWithJpg = SignatureUtil.getSignatureWithHmacSha1(SECRET_KEY, RAW_MESSAGE + ".jpg");
        assertDoesNotThrow(
            () -> CodingUtil.assertMetaDataSignConsist(SECRET_KEY, RAW_MESSAGE, signatureWithJpg));
    }

    @Test
    void assertAcidFormat_ShouldThrowException_WhenListTooShort() {
        FileServerException exception = assertThrows(FileServerException.class,
            () -> CodingUtil.assertAcidFormat(Collections.singletonList("single")));
        assertEquals(ErInfo.AUTH_MISS_USER_INFO.getCode(), exception.getCode());
    }

    @Test
    void assertAcidFormat_ShouldNotThrowException_WhenListHasEnoughElements() {
        assertDoesNotThrow(
            () -> CodingUtil.assertAcidFormat(Arrays.asList("first", "second")));
    }
} 