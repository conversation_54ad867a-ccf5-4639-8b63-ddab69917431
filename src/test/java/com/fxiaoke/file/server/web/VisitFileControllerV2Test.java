package com.fxiaoke.file.server.web;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


import com.fxiaoke.file.server.domain.model.api.request.CookieNFileAcRequest;
import com.fxiaoke.file.server.domain.model.api.request.NoSignNFileAcRequest;
import com.fxiaoke.file.server.domain.model.api.request.SignNFileAcRequest;
import com.fxiaoke.file.server.service.FileService;
import jakarta.servlet.http.Cookie;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ExtendWith(MockitoExtension.class)
class VisitFileControllerV2Test {

  private MockMvc mockMvc;

  @Mock
  private FileService fileService;

  private VisitFileControllerV2 controller;

  @BeforeEach
  void setUp() {
    controller = new VisitFileControllerV2(fileService);
    mockMvc = MockMvcBuilders.standaloneSetup(controller).build();
  }

  @Test
  void visitFileBySign_ShouldReturnFile() throws Exception {
    // given
    String userAgent = "Mozilla/5.0";
    String testData = "test file content";
    InputStream testInputStream = new ByteArrayInputStream(testData.getBytes());
    
    when(fileService.getFileBySign(any(SignNFileAcRequest.class))).thenReturn(testInputStream);

    // when & then
    mockMvc.perform(get("/FilesOne/Sign")
            .header("User-Agent", userAgent)
            .param("Fid", "testFid")
            .param("Acid", "testAcid")
            .param("Ets", "1234567890")
            .param("Ak", "123456789012345678901234")
            .param("Fn", "test.txt")
            .param("Sig", "testSig")
            .param("Ds", "testDs"))
        .andExpect(status().isOk());

    verify(fileService).getFileBySign(any(SignNFileAcRequest.class));
  }

  @Test
  void visitFileBySignAndAuthX_ShouldReturnFile() throws Exception {
    // given
    String userAgent = "Mozilla/5.0";
    String testData = "test file content";
    InputStream testInputStream = new ByteArrayInputStream(testData.getBytes());
    
    when(fileService.getFileByConditionAuthXC(any(SignNFileAcRequest.class), any(String.class)))
        .thenReturn(testInputStream);

    // when & then
    mockMvc.perform(get("/FilesOne/Sign/AuthXC")
            .header("User-Agent", userAgent)
            .cookie(new Cookie("FSAuthXC", "testAuthXC"))
            .param("Fid", "testFid")
            .param("Acid", "testAcid")
            .param("Ets", "1234567890")
            .param("Ak", "123456789012345678901234")
            .param("Fn", "test.txt")
            .param("Sig", "testSig")
            .param("Ds", "testDs"))
        .andExpect(status().isOk());

    verify(fileService).getFileByConditionAuthXC(any(SignNFileAcRequest.class), any(String.class));
  }

  @Test
  void visitFileBySignAndEm6_ShouldReturnFile() throws Exception {
    // given
    String userAgent = "Mozilla/5.0";
    String testData = "test file content";
    InputStream testInputStream = new ByteArrayInputStream(testData.getBytes());
    
    when(fileService.getFileByConditionEm6(any(SignNFileAcRequest.class), any(String.class)))
        .thenReturn(testInputStream);

    // when & then
    mockMvc.perform(get("/FilesOne/Sign/Em6")
            .header("User-Agent", userAgent)
            .cookie(new Cookie("ERInfo", "testERInfo"))
            .param("Fid", "testFid")
            .param("Acid", "testAcid")
            .param("Ets", "1234567890")
            .param("Ak", "123456789012345678901234")
            .param("Fn", "test.txt")
            .param("Sig", "testSig")
            .param("Ds", "testDs"))
        .andExpect(status().isOk());

    verify(fileService).getFileByConditionEm6(any(SignNFileAcRequest.class), any(String.class));
  }

  @Test
  void visitFileByCookie_ShouldReturnFile() throws Exception {
    // given
    String userAgent = "Mozilla/5.0";
    String testData = "test file content";
    InputStream testInputStream = new ByteArrayInputStream(testData.getBytes());
    
    when(fileService.getFileByCookie(any(CookieNFileAcRequest.class), any(String.class), any(String.class)))
        .thenReturn(testInputStream);

    // when & then
    mockMvc.perform(get("/FilesOne/Cookie")
            .header("User-Agent", userAgent)
            .cookie(new Cookie("FSAuthXC", "testAuthXC"))
            .cookie(new Cookie("ERInfo", "testERInfo"))
            .param("Fid", "testFid")
            .param("Acid", "testAcid")
            .param("Ak", "123456789012345678901234")
            .param("Fn", "test.txt")
            .param("Sig", "testSig")
            .param("Ds", "testDs"))
        .andExpect(status().isOk());

    verify(fileService).getFileByCookie(any(CookieNFileAcRequest.class), any(String.class), any(String.class));
  }

  @Test
  void visitFileByAuthX_ShouldReturnFile() throws Exception {
    // given
    String userAgent = "Mozilla/5.0";
    String testData = "test file content";
    InputStream testInputStream = new ByteArrayInputStream(testData.getBytes());
    
    when(fileService.getFileByAuthXC(any(CookieNFileAcRequest.class), any(String.class)))
        .thenReturn(testInputStream);

    // when & then
    mockMvc.perform(get("/FilesOne/Cookie/AuthXC")
            .header("User-Agent", userAgent)
            .cookie(new Cookie("FSAuthXC", "testAuthXC"))
            .param("Fid", "testFid")
            .param("Acid", "testAcid")
            .param("Ak", "123456789012345678901234")
            .param("Fn", "test.txt")
            .param("Sig", "testSig")
            .param("Ds", "testDs"))
        .andExpect(status().isOk());

    verify(fileService).getFileByAuthXC(any(CookieNFileAcRequest.class), any(String.class));
  }

  @Test
  void visitFileByEm6_ShouldReturnFile() throws Exception {
    // given
    String userAgent = "Mozilla/5.0";
    String testData = "test file content";
    InputStream testInputStream = new ByteArrayInputStream(testData.getBytes());
    
    when(fileService.getFileByEm6(any(CookieNFileAcRequest.class), any(String.class)))
        .thenReturn(testInputStream);

    // when & then
    mockMvc.perform(get("/FilesOne/Cookie/Em6")
            .header("User-Agent", userAgent)
            .cookie(new Cookie("ERInfo", "testERInfo"))
            .param("Fid", "testFid")
            .param("Acid", "testAcid")
            .param("Ak", "123456789012345678901234")
            .param("Fn", "test.txt")
            .param("Sig", "testSig")
            .param("Ds", "testDs"))
        .andExpect(status().isOk());

    verify(fileService).getFileByEm6(any(CookieNFileAcRequest.class), any(String.class));
  }

  @Test
  void visitFileBySelfSign_ShouldReturnFile() throws Exception {
    // given
    String userAgent = "Mozilla/5.0";
    String testData = "test file content";
    InputStream testInputStream = new ByteArrayInputStream(testData.getBytes());
    
    when(fileService.getFileBySign(any(NoSignNFileAcRequest.class))).thenReturn(testInputStream);

    // when & then
    mockMvc.perform(get("/FilesOne/Anonymity/Sign")
            .header("User-Agent", userAgent)
            .param("Fid", "testFid")
            .param("Acid", "testAcid")
            .param("Ets", "1234567890")
            .param("Ak", "123456789012345678901234")
            .param("Fn", "test.txt")
            .param("Bn", "testBn")
            .param("Sig", "testSig")
            .param("Ds", "testDs"))
        .andExpect(status().isOk());

    verify(fileService).getFileBySign(any(NoSignNFileAcRequest.class));
  }

  @Test
  void visitFile_WithMissingUserAgent_ShouldReturnBadRequest() throws Exception {
    mockMvc.perform(get("/FilesOne/Sign")
            .param("Fid", "testFid")
            .param("Acid", "testAcid")
            .param("Ets", "1234567890")
            .param("Ak", "123456789012345678901234")
            .param("Fn", "test.txt")
            .param("Sig", "testSig")
            .param("Ds", "testDs"))
        .andExpect(status().isBadRequest());
  }

  @Test
  void visitFile_WithInvalidParams_ShouldReturnBadRequest() throws Exception {
    mockMvc.perform(get("/FilesOne/Sign")
            .header("User-Agent", "Mozilla/5.0")
            .param("Fid", "testFid")) // Missing required parameters
        .andExpect(status().isBadRequest());
  }
} 