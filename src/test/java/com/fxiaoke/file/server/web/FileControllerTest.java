package com.fxiaoke.file.server.web;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.fxiaoke.file.server.domain.model.api.request.NoSignNFileAcRequest;
import com.fxiaoke.file.server.service.FileService;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletResponse;

@ExtendWith(MockitoExtension.class)
class FileControllerTest {

  @Mock
  private FileService fileService;

  private FileController controller;

  @BeforeEach
  void setUp() {
    controller = new FileController(fileService);
  }

  @Test
  void toBase64ROnlySign_ShouldReturnBase64Content() throws Exception {
    // given
    String testData = "test file content";
    InputStream testInputStream = new ByteArrayInputStream(testData.getBytes());
    
    NoSignNFileAcRequest request = new NoSignNFileAcRequest();
    request.setFid("testFid");
    request.setAcid("testAcid");
    request.setEts(System.currentTimeMillis() / 1000 + 3600); // 1 hour from now
    request.setAk("123456789012345678901234");
    request.setFn("test.txt");
    request.setBn("testBn");
    request.setSig("testSig");
    request.setDs("testDs");
    
    MockHttpServletResponse response = new MockHttpServletResponse();
    
    when(fileService.getFileBySign(any(NoSignNFileAcRequest.class))).thenReturn(testInputStream);

    // when
    controller.toBase64ROnlySign(request, response);

    // then
    String responseContent = response.getContentAsString();
    assertTrue(responseContent.startsWith("data:"), "Response should start with data: prefix");
    assertTrue(responseContent.contains(";base64,"), "Response should contain base64 marker");
    assertTrue(response.getHeader("Content-Type").equals("text/plain"), "Content-Type should be text/plain");
  }

  @Test
  void toBase64ROnlySign_WithImageFile_ShouldReturnImageBase64() throws Exception {
    // given
    String testData = "test image content";
    InputStream testInputStream = new ByteArrayInputStream(testData.getBytes());
    
    NoSignNFileAcRequest request = new NoSignNFileAcRequest();
    request.setFid("testFid");
    request.setAcid("testAcid");
    request.setEts(System.currentTimeMillis() / 1000 + 3600); // 1 hour from now
    request.setAk("123456789012345678901234");
    request.setFn("test.jpg");
    request.setBn("testBn");
    request.setSig("testSig");
    request.setDs("testDs");
    
    MockHttpServletResponse response = new MockHttpServletResponse();
    
    when(fileService.getFileBySign(any(NoSignNFileAcRequest.class))).thenReturn(testInputStream);

    // when
    controller.toBase64ROnlySign(request, response);

    // then
    String responseContent = response.getContentAsString();
    assertTrue(responseContent.startsWith("data:image/jpeg;base64,"), "Response should start with image MIME type");
  }
} 