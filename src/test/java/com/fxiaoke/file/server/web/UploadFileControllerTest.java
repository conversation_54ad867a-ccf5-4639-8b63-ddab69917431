package com.fxiaoke.file.server.web;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fxiaoke.file.server.domain.model.api.request.SignFileUpRequest;
import com.fxiaoke.file.server.exception.GlobalExceptionHandler;
import com.fxiaoke.file.server.service.FileService;
import java.io.InputStream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import java.util.List;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter;
import org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor;
import org.springframework.web.servlet.mvc.method.annotation.RequestPartMethodArgumentResolver;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.support.AllEncompassingFormHttpMessageConverter;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;

@ExtendWith(MockitoExtension.class)
class UploadFileControllerTest {

  private MockMvc mockMvc;

  @Mock
  private FileService fileService;

  @BeforeEach
  void setUp() {
    UploadFileController controller = new UploadFileController(fileService);
    
    // 创建异常处理器
    ExceptionHandlerExceptionResolver exceptionResolver = new ExceptionHandlerExceptionResolver();
    
    // 配置消息转换器
    List<HttpMessageConverter<?>> converters = new ArrayList<>();
    converters.add(new StringHttpMessageConverter(StandardCharsets.UTF_8));
    converters.add(new MappingJackson2HttpMessageConverter());
    converters.add(new AllEncompassingFormHttpMessageConverter());
    
    exceptionResolver.setMessageConverters(converters);
    
    // 配置参数解析器
    List<HandlerMethodArgumentResolver> argumentResolvers = new ArrayList<>();
    argumentResolvers.add(new RequestResponseBodyMethodProcessor(converters));
    argumentResolvers.add(new RequestPartMethodArgumentResolver(converters));
    
    // 创建RequestMappingHandlerAdapter并配置
    RequestMappingHandlerAdapter handlerAdapter = new RequestMappingHandlerAdapter();
    handlerAdapter.setMessageConverters(converters);
    handlerAdapter.setArgumentResolvers(argumentResolvers);
    
    // 配置参数验证器
    LocalValidatorFactoryBean validator = new LocalValidatorFactoryBean();
    validator.afterPropertiesSet();
    
    // 初始化异常处理器
    exceptionResolver.afterPropertiesSet();
    
    // 配置全局异常处理器
    GlobalExceptionHandler globalExceptionHandler = new GlobalExceptionHandler();
    
    mockMvc = MockMvcBuilders.standaloneSetup(controller)
        .setHandlerExceptionResolvers(exceptionResolver)
        .setValidator(validator)
        .setControllerAdvice(globalExceptionHandler)
        .build();
  }

  @Test
  void uploadSingleFileWithForm_Success() throws Exception {
    // given
    String filePath = "/path/to/uploaded/file.txt";
    MockMultipartFile file = new MockMultipartFile(
        "facishareFile",
        "test.txt",
        MediaType.TEXT_PLAIN_VALUE,
        "test content".getBytes()
    );

    when(fileService.uploadFile(any(SignFileUpRequest.class), any(InputStream.class)))
        .thenReturn(filePath);

    // when & then
    mockMvc.perform(multipart("/FilesOne")
            .file(file)
            .contentType(MediaType.MULTIPART_FORM_DATA)
            .header("User-Agent", "Mozilla/5.0")
            .header("acid", "1.2")
            .header("resource", "test-resource")
            .header("ak", "123456789012345678901234")
            .header("sign", "test-sign")
            .header("expiry", "1735689600000")
            .header("filename", "test.txt")
            .header("size", "12")
            .header("digest", "test-digest"))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.success").value(true))
        .andExpect(jsonPath("$.code").value(200))
        .andExpect(jsonPath("$.message").value("success"))
        .andExpect(jsonPath("$.data").value(filePath));

    verify(fileService).uploadFile(any(SignFileUpRequest.class), any(InputStream.class));
  }

  @Test
  void uploadSingleFileWithStream_Success() throws Exception {
    // given
    String filePath = "/path/to/uploaded/file.txt";
    byte[] content = "test content".getBytes();

    when(fileService.uploadFile(any(SignFileUpRequest.class), any(InputStream.class)))
        .thenReturn(filePath);

    // when & then
    mockMvc.perform(post("/FilesOne")
            .contentType(MediaType.APPLICATION_OCTET_STREAM)
            .content(content)
            .header("User-Agent", "Mozilla/5.0")
            .header("acid", "1.2")
            .header("resource", "test-resource")
            .header("ak", "123456789012345678901234")
            .header("sign", "test-sign")
            .header("expiry", "1735689600000")
            .header("filename", "test.txt")
            .header("size", "12")
            .header("digest", "test-digest"))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.success").value(true))
        .andExpect(jsonPath("$.code").value(200))
        .andExpect(jsonPath("$.message").value("success"))
        .andExpect(jsonPath("$.data").value(filePath));

    verify(fileService).uploadFile(any(SignFileUpRequest.class), any(InputStream.class));
  }

  @Test
  void uploadFile_WithMissingHeaders_ShouldReturnBadRequest() throws Exception {
    // given
    MockMultipartFile file = new MockMultipartFile(
        "facishareFile",
        "test.txt",
        MediaType.TEXT_PLAIN_VALUE,
        "test content".getBytes()
    );

    // when & then
    mockMvc.perform(multipart("/FilesOne")
            .file(file)
            .header("User-Agent", "Mozilla/5.0")
            // 缺少必要的header
            .header("acid", "1.2"))
        .andExpect(status().isBadRequest());
  }

  @Test
  void uploadFile_WithInvalidSize_ShouldReturnBadRequest() throws Exception {
    // given
    MockMultipartFile file = new MockMultipartFile(
        "facishareFile",
        "test.txt",
        MediaType.TEXT_PLAIN_VALUE,
        "test content".getBytes()
    );

    // when & then
    mockMvc.perform(multipart("/FilesOne")
            .file(file)
            .header("User-Agent", "Mozilla/5.0")
            .header("acid", "1.2")
            .header("resource", "test-resource")
            .header("ak", "123456789012345678901234")
            .header("sign", "test-sign")
            .header("expiry", "1735689600000")
            .header("filename", "test.txt")
            .header("size", "104857601") // 超过100MB的限制
            .header("digest", "test-digest"))
        .andExpect(status().isBadRequest())
        .andExpect(jsonPath("$.success").value(false))
        .andExpect(jsonPath("$.code").value(400))
        .andExpect(jsonPath("$.message").exists());
  }

  @Test
  void uploadFile_WithSizeMismatch_ShouldReturnBadRequest() throws Exception {
    // given
    String testContent = "test content";
    MockMultipartFile file = new MockMultipartFile(
        "facishareFile",
        "test.txt",
        MediaType.TEXT_PLAIN_VALUE,
        testContent.getBytes()
    );

    // when & then
    mockMvc.perform(multipart("/FilesOne")
            .file(file)
            .header("User-Agent", "Mozilla/5.0")
            .header("acid", "1.2")
            .header("resource", "test-resource")
            .header("ak", "123456789012345678901234")
            .header("sign", "test-sign")
            .header("expiry", "1735689600000")
            .header("filename", "test.txt")
            .header("size", String.valueOf(testContent.length() + 1)) // 文件大小不匹配
            .header("digest", "test-digest")
            .contentType(MediaType.MULTIPART_FORM_DATA))
        .andExpect(status().isBadRequest())
        .andExpect(jsonPath("$.success").value(false))
        .andExpect(jsonPath("$.code").value(400));
  }

  @Test
  void uploadFile_WithMissingFile_ShouldReturnBadRequest() throws Exception {
    // when & then
    mockMvc.perform(multipart("/FilesOne")
            .header("User-Agent", "Mozilla/5.0")
            .header("acid", "1.2")
            .header("resource", "test-resource")
            .header("ak", "123456789012345678901234")
            .header("sign", "test-sign")
            .header("expiry", "1735689600000")
            .header("filename", "test.txt")
            .header("size", "12")
            .header("digest", "test-digest")
            .contentType(MediaType.MULTIPART_FORM_DATA))
        .andExpect(status().isBadRequest())
        .andExpect(jsonPath("$.success").value(false))
        .andExpect(jsonPath("$.code").value(400));
  }
} 