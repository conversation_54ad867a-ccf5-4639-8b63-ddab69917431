package com.fxiaoke.file.server.web;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fxiaoke.file.server.domain.constants.ErInfo;
import com.fxiaoke.file.server.domain.exception.FileServerException;
import com.fxiaoke.file.server.domain.model.api.request.NoSignCFileAcRequest;
import com.fxiaoke.file.server.exception.GlobalExceptionHandler;
import com.fxiaoke.file.server.service.FileService;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ExtendWith(MockitoExtension.class)
class CFileVisitControllerTest {

  private MockMvc mockMvc;

  @Mock
  private FileService fileService;

  private CFileVisitController controller;

  @BeforeEach
  void setUp() {
    controller = new CFileVisitController(fileService);
    mockMvc = MockMvcBuilders.standaloneSetup(controller)
        .setControllerAdvice(new GlobalExceptionHandler())
        .build();
  }

  @Test
  void visitFile_WithValidParams_ShouldReturnFile() throws Exception {
    // given
    String userAgent = "Mozilla/5.0";
    String testData = "test image content";
    InputStream testInputStream = new ByteArrayInputStream(testData.getBytes());
    
    when(fileService.getCFile(any(NoSignCFileAcRequest.class))).thenReturn(testInputStream);

    // when & then
    mockMvc.perform(get("/ImagesOne")
            .header("User-Agent", userAgent)
            .param("Cid", "testCid")
            .param("Acid", "testAcid")
            .param("Ext", "jpg")
            .param("Bn", "testBn")
            .param("Ds", "testDs"))
        .andExpect(status().isOk());

    verify(fileService).getCFile(any(NoSignCFileAcRequest.class));
  }

  @Test
  void visitFile_WithInvalidExtension_ShouldReturnBadRequest() throws Exception {
    // given
    String userAgent = "Mozilla/5.0";
    
    when(fileService.getCFile(any(NoSignCFileAcRequest.class)))
        .thenThrow(new FileServerException("CFileVisitController", ErInfo.UPLOAD_FILE_TYPE_NOT_MATCH));

    // when & then
    mockMvc.perform(get("/ImagesOne")
            .header("User-Agent", userAgent)
            .param("Cid", "testCid")
            .param("Acid", "testAcid")
            .param("Ext", "invalid")
            .param("Bn", "testBn")
            .param("Ds", "testDs"))
        .andExpect(status().isBadRequest());
  }

  @Test
  void visitFile_WithMissingParams_ShouldReturnBadRequest() throws Exception {
    // given
    String userAgent = "Mozilla/5.0";

    // when & then
    mockMvc.perform(get("/ImagesOne")
            .header("User-Agent", userAgent)
            .param("Cid", "testCid")) // 缺少必要参数
        .andExpect(status().isBadRequest());
  }

  @Test
  void visitFile_WithMissingUserAgent_ShouldReturnBadRequest() throws Exception {
    // when & then
    mockMvc.perform(get("/ImagesOne")
            .param("Cid", "testCid")
            .param("Acid", "testAcid")
            .param("Ext", "jpg")
            .param("Bn", "testBn")
            .param("Ds", "testDs"))
        .andExpect(status().isBadRequest());
  }

  @Test
  void visitFile_WithValidImageExtensions_ShouldReturnFile() throws Exception {
    // given
    String userAgent = "Mozilla/5.0";
    String testData = "test image content";
    InputStream testInputStream = new ByteArrayInputStream(testData.getBytes());
    
    when(fileService.getCFile(any(NoSignCFileAcRequest.class))).thenReturn(testInputStream);

    // 测试所有有效的图片扩展名
    String[] validExtensions = {"jpg", "jpeg", "png", "gif", "bmp", "webp"};
    
    for (String ext : validExtensions) {
      // when & then
      mockMvc.perform(get("/ImagesOne")
              .header("User-Agent", userAgent)
              .param("Cid", "testCid")
              .param("Acid", "testAcid")
              .param("Ext", ext)
              .param("Bn", "testBn")
              .param("Ds", "testDs"))
          .andExpect(status().isOk());
    }
    
    // 验证调用次数应该等于扩展名的数量
    verify(fileService, times(validExtensions.length)).getCFile(any(NoSignCFileAcRequest.class));
  }

  @Test
  void visitFile_WithDifferentImageSizes_ShouldReturnFile() throws Exception {
    // given
    String userAgent = "Mozilla/5.0";
    String testData = "test image content";
    InputStream testInputStream = new ByteArrayInputStream(testData.getBytes());
    
    when(fileService.getCFile(any(NoSignCFileAcRequest.class))).thenReturn(testInputStream);

    // 测试不同的图片尺寸
    String[] sizes = {"100*100", "200*200", "500*500", "800*600"};
    
    for (String size : sizes) {
      // when & then
      mockMvc.perform(get("/ImagesOne")
              .header("User-Agent", userAgent)
              .param("Cid", "testCid")
              .param("Acid", "testAcid")
              .param("Ext", "jpg")
              .param("Bn", "testBn")
              .param("Ds", "testDs")
              .param("size", size))
          .andExpect(status().isOk());
    }
    
    // 验证调用次数应该等于尺寸的数量
    verify(fileService, times(sizes.length)).getCFile(any(NoSignCFileAcRequest.class));
  }
} 