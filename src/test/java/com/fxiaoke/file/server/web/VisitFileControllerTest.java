package com.fxiaoke.file.server.web;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


import com.fxiaoke.file.server.domain.model.api.request.AvatarRequest;
import com.fxiaoke.file.server.domain.model.api.request.NoSignNFileAcRequest;
import com.fxiaoke.file.server.domain.model.api.request.SignNFileAcRequest;
import com.fxiaoke.file.server.service.FileService;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ExtendWith(MockitoExtension.class)
class VisitFileControllerTest {

  private MockMvc mockMvc;

  @Mock
  private FileService fileService;

  private VisitFileController controller;

  @BeforeEach
  void setUp() {
    controller = new VisitFileController(fileService);
    mockMvc = MockMvcBuilders.standaloneSetup(controller).build();
  }

  @Test
  void visitFile_WithSignNFileAcRequest_ShouldReturnFile() throws Exception {
    // given
    String userAgent = "Mozilla/5.0";
    String testData = "test file content";
    InputStream testInputStream = new ByteArrayInputStream(testData.getBytes());
    
    when(fileService.getFileBySign(any(SignNFileAcRequest.class))).thenReturn(testInputStream);

    // when & then
    mockMvc.perform(get("/FilesOne")
            .header("User-Agent", userAgent)
            .param("Fid", "testFid")
            .param("Acid", "testAcid")
            .param("Ets", "1234567890")
            .param("Ak", "123456789012345678901234")
            .param("Fn", "test.txt")
            .param("Sig", "testSig")
            .param("Ds", "testDs"))
        .andExpect(status().isOk());

    verify(fileService).getFileBySign(any(SignNFileAcRequest.class));
  }

  @Test
  void visitFile_WithNoSignNFileAcRequest_ShouldReturnFile() throws Exception {
    // given
    String userAgent = "Mozilla/5.0";
    String testData = "test file content";
    InputStream testInputStream = new ByteArrayInputStream(testData.getBytes());
    
    when(fileService.getFileBySign(any(NoSignNFileAcRequest.class))).thenReturn(testInputStream);

    // when & then
    mockMvc.perform(get("/FilesOne")
            .header("User-Agent", userAgent)
            .param("Fid", "testFid")
            .param("Acid", "testAcid")
            .param("Ets", "1234567890")
            .param("Ak", "123456789012345678901234")
            .param("Fn", "test.txt")
            .param("Bn", "testBn")
            .param("Sig", "testSig")
            .param("Ds", "testDs"))
        .andExpect(status().isOk());

    verify(fileService).getFileBySign(any(NoSignNFileAcRequest.class));
  }

  @Test
  void visitAvatarFile_ShouldReturnAvatarFile() throws Exception {
    // given
    String userAgent = "Mozilla/5.0";
    
    // 使用doAnswer来验证方法调用
    doAnswer(invocation -> {
      AvatarRequest request = invocation.getArgument(0);
      OutputStream outputStream = invocation.getArgument(1);
      // 可以在这里写入一些测试数据到outputStream
      outputStream.write("test avatar data".getBytes());
      return null;
    }).when(fileService).getAvatarFile(any(AvatarRequest.class), any(OutputStream.class));

    // when & then
    mockMvc.perform(get("/FilesOne")
            .header("User-Agent", userAgent)
            .param("Fid", "testFid")
            .param("Acid", "testAcid")
            .param("Fn", "test.jpg")
            .param("Ds", "testDs"))
        .andExpect(status().isOk());

    verify(fileService).getAvatarFile(any(AvatarRequest.class), any(OutputStream.class));
  }

  @Test
  void visitFile_WithInvalidParams_ShouldReturnBadRequest() throws Exception {
    // given
    String userAgent = "Mozilla/5.0";

    // when & then
    mockMvc.perform(get("/FilesOne")
            .header("User-Agent", userAgent)
            .param("Fid", "testFid")) // Missing required parameters
        .andExpect(status().isBadRequest());
  }

  @Test
  void visitFile_WithMissingUserAgent_ShouldReturnBadRequest() throws Exception {
    // when & then
    mockMvc.perform(get("/FilesOne")
            .param("Fid", "testFid")
            .param("Acid", "testAcid")
            .param("Ets", "1234567890")
            .param("Ak", "123456789012345678901234")
            .param("Fn", "test.txt")
            .param("Sig", "testSig")
            .param("Ds", "testDs"))
        .andExpect(status().isBadRequest());
  }
} 